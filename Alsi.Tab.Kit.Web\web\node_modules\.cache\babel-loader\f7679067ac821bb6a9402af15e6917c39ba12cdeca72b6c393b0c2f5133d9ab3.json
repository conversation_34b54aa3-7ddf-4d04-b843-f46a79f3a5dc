{"ast": null, "code": "import assignInWith from './assignInWith.js';\nimport attempt from './attempt.js';\nimport baseValues from './_baseValues.js';\nimport customDefaultsAssignIn from './_customDefaultsAssignIn.js';\nimport escapeStringChar from './_escapeStringChar.js';\nimport isError from './isError.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport keys from './keys.js';\nimport reInterpolate from './_reInterpolate.js';\nimport templateSettings from './templateSettings.js';\nimport toString from './toString.js';\n\n/** Error message constants. */\nvar INVALID_TEMPL_VAR_ERROR_TEXT = 'Invalid `variable` option passed into `_.template`';\n\n/** Used to match empty string literals in compiled template source. */\nvar reEmptyStringLeading = /\\b__p \\+= '';/g,\n  reEmptyStringMiddle = /\\b(__p \\+=) '' \\+/g,\n  reEmptyStringTrailing = /(__e\\(.*?\\)|\\b__t\\)) \\+\\n'';/g;\n\n/**\n * Used to validate the `validate` option in `_.template` variable.\n *\n * Forbids characters which could potentially change the meaning of the function argument definition:\n * - \"(),\" (modification of function parameters)\n * - \"=\" (default value)\n * - \"[]{}\" (destructuring of function parameters)\n * - \"/\" (beginning of a comment)\n * - whitespace\n */\nvar reForbiddenIdentifierChars = /[()=,{}\\[\\]\\/\\s]/;\n\n/**\n * Used to match\n * [ES template delimiters](http://ecma-international.org/ecma-262/7.0/#sec-template-literal-lexical-components).\n */\nvar reEsTemplate = /\\$\\{([^\\\\}]*(?:\\\\.[^\\\\}]*)*)\\}/g;\n\n/** Used to ensure capturing order of template delimiters. */\nvar reNoMatch = /($^)/;\n\n/** Used to match unescaped characters in compiled string literals. */\nvar reUnescapedString = /['\\n\\r\\u2028\\u2029\\\\]/g;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates a compiled template function that can interpolate data properties\n * in \"interpolate\" delimiters, HTML-escape interpolated data properties in\n * \"escape\" delimiters, and execute JavaScript in \"evaluate\" delimiters. Data\n * properties may be accessed as free variables in the template. If a setting\n * object is given, it takes precedence over `_.templateSettings` values.\n *\n * **Note:** In the development build `_.template` utilizes\n * [sourceURLs](http://www.html5rocks.com/en/tutorials/developertools/sourcemaps/#toc-sourceurl)\n * for easier debugging.\n *\n * For more information on precompiling templates see\n * [lodash's custom builds documentation](https://lodash.com/custom-builds).\n *\n * For more information on Chrome extension sandboxes see\n * [Chrome's extensions documentation](https://developer.chrome.com/extensions/sandboxingEval).\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category String\n * @param {string} [string=''] The template string.\n * @param {Object} [options={}] The options object.\n * @param {RegExp} [options.escape=_.templateSettings.escape]\n *  The HTML \"escape\" delimiter.\n * @param {RegExp} [options.evaluate=_.templateSettings.evaluate]\n *  The \"evaluate\" delimiter.\n * @param {Object} [options.imports=_.templateSettings.imports]\n *  An object to import into the template as free variables.\n * @param {RegExp} [options.interpolate=_.templateSettings.interpolate]\n *  The \"interpolate\" delimiter.\n * @param {string} [options.sourceURL='templateSources[n]']\n *  The sourceURL of the compiled template.\n * @param {string} [options.variable='obj']\n *  The data object variable name.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the compiled template function.\n * @example\n *\n * // Use the \"interpolate\" delimiter to create a compiled template.\n * var compiled = _.template('hello <%= user %>!');\n * compiled({ 'user': 'fred' });\n * // => 'hello fred!'\n *\n * // Use the HTML \"escape\" delimiter to escape data property values.\n * var compiled = _.template('<b><%- value %></b>');\n * compiled({ 'value': '<script>' });\n * // => '<b>&lt;script&gt;</b>'\n *\n * // Use the \"evaluate\" delimiter to execute JavaScript and generate HTML.\n * var compiled = _.template('<% _.forEach(users, function(user) { %><li><%- user %></li><% }); %>');\n * compiled({ 'users': ['fred', 'barney'] });\n * // => '<li>fred</li><li>barney</li>'\n *\n * // Use the internal `print` function in \"evaluate\" delimiters.\n * var compiled = _.template('<% print(\"hello \" + user); %>!');\n * compiled({ 'user': 'barney' });\n * // => 'hello barney!'\n *\n * // Use the ES template literal delimiter as an \"interpolate\" delimiter.\n * // Disable support by replacing the \"interpolate\" delimiter.\n * var compiled = _.template('hello ${ user }!');\n * compiled({ 'user': 'pebbles' });\n * // => 'hello pebbles!'\n *\n * // Use backslashes to treat delimiters as plain text.\n * var compiled = _.template('<%= \"\\\\<%- value %\\\\>\" %>');\n * compiled({ 'value': 'ignored' });\n * // => '<%- value %>'\n *\n * // Use the `imports` option to import `jQuery` as `jq`.\n * var text = '<% jq.each(users, function(user) { %><li><%- user %></li><% }); %>';\n * var compiled = _.template(text, { 'imports': { 'jq': jQuery } });\n * compiled({ 'users': ['fred', 'barney'] });\n * // => '<li>fred</li><li>barney</li>'\n *\n * // Use the `sourceURL` option to specify a custom sourceURL for the template.\n * var compiled = _.template('hello <%= user %>!', { 'sourceURL': '/basic/greeting.jst' });\n * compiled(data);\n * // => Find the source of \"greeting.jst\" under the Sources tab or Resources panel of the web inspector.\n *\n * // Use the `variable` option to ensure a with-statement isn't used in the compiled template.\n * var compiled = _.template('hi <%= data.user %>!', { 'variable': 'data' });\n * compiled.source;\n * // => function(data) {\n * //   var __t, __p = '';\n * //   __p += 'hi ' + ((__t = ( data.user )) == null ? '' : __t) + '!';\n * //   return __p;\n * // }\n *\n * // Use custom template delimiters.\n * _.templateSettings.interpolate = /{{([\\s\\S]+?)}}/g;\n * var compiled = _.template('hello {{ user }}!');\n * compiled({ 'user': 'mustache' });\n * // => 'hello mustache!'\n *\n * // Use the `source` property to inline compiled templates for meaningful\n * // line numbers in error messages and stack traces.\n * fs.writeFileSync(path.join(process.cwd(), 'jst.js'), '\\\n *   var JST = {\\\n *     \"main\": ' + _.template(mainText).source + '\\\n *   };\\\n * ');\n */\nfunction template(string, options, guard) {\n  // Based on John Resig's `tmpl` implementation\n  // (http://ejohn.org/blog/javascript-micro-templating/)\n  // and Laura Doktorova's doT.js (https://github.com/olado/doT).\n  var settings = templateSettings.imports._.templateSettings || templateSettings;\n  if (guard && isIterateeCall(string, options, guard)) {\n    options = undefined;\n  }\n  string = toString(string);\n  options = assignInWith({}, options, settings, customDefaultsAssignIn);\n  var imports = assignInWith({}, options.imports, settings.imports, customDefaultsAssignIn),\n    importsKeys = keys(imports),\n    importsValues = baseValues(imports, importsKeys);\n  var isEscaping,\n    isEvaluating,\n    index = 0,\n    interpolate = options.interpolate || reNoMatch,\n    source = \"__p += '\";\n\n  // Compile the regexp to match each delimiter.\n  var reDelimiters = RegExp((options.escape || reNoMatch).source + '|' + interpolate.source + '|' + (interpolate === reInterpolate ? reEsTemplate : reNoMatch).source + '|' + (options.evaluate || reNoMatch).source + '|$', 'g');\n\n  // Use a sourceURL for easier debugging.\n  // The sourceURL gets injected into the source that's eval-ed, so be careful\n  // to normalize all kinds of whitespace, so e.g. newlines (and unicode versions of it) can't sneak in\n  // and escape the comment, thus injecting code that gets evaled.\n  var sourceURL = hasOwnProperty.call(options, 'sourceURL') ? '//# sourceURL=' + (options.sourceURL + '').replace(/\\s/g, ' ') + '\\n' : '';\n  string.replace(reDelimiters, function (match, escapeValue, interpolateValue, esTemplateValue, evaluateValue, offset) {\n    interpolateValue || (interpolateValue = esTemplateValue);\n\n    // Escape characters that can't be included in string literals.\n    source += string.slice(index, offset).replace(reUnescapedString, escapeStringChar);\n\n    // Replace delimiters with snippets.\n    if (escapeValue) {\n      isEscaping = true;\n      source += \"' +\\n__e(\" + escapeValue + \") +\\n'\";\n    }\n    if (evaluateValue) {\n      isEvaluating = true;\n      source += \"';\\n\" + evaluateValue + \";\\n__p += '\";\n    }\n    if (interpolateValue) {\n      source += \"' +\\n((__t = (\" + interpolateValue + \")) == null ? '' : __t) +\\n'\";\n    }\n    index = offset + match.length;\n\n    // The JS engine embedded in Adobe products needs `match` returned in\n    // order to produce the correct `offset` value.\n    return match;\n  });\n  source += \"';\\n\";\n\n  // If `variable` is not specified wrap a with-statement around the generated\n  // code to add the data object to the top of the scope chain.\n  var variable = hasOwnProperty.call(options, 'variable') && options.variable;\n  if (!variable) {\n    source = 'with (obj) {\\n' + source + '\\n}\\n';\n  }\n  // Throw an error if a forbidden character was found in `variable`, to prevent\n  // potential command injection attacks.\n  else if (reForbiddenIdentifierChars.test(variable)) {\n    throw new Error(INVALID_TEMPL_VAR_ERROR_TEXT);\n  }\n\n  // Cleanup code by stripping empty strings.\n  source = (isEvaluating ? source.replace(reEmptyStringLeading, '') : source).replace(reEmptyStringMiddle, '$1').replace(reEmptyStringTrailing, '$1;');\n\n  // Frame code as the function body.\n  source = 'function(' + (variable || 'obj') + ') {\\n' + (variable ? '' : 'obj || (obj = {});\\n') + \"var __t, __p = ''\" + (isEscaping ? ', __e = _.escape' : '') + (isEvaluating ? ', __j = Array.prototype.join;\\n' + \"function print() { __p += __j.call(arguments, '') }\\n\" : ';\\n') + source + 'return __p\\n}';\n  var result = attempt(function () {\n    return Function(importsKeys, sourceURL + 'return ' + source).apply(undefined, importsValues);\n  });\n\n  // Provide the compiled function's source by its `toString` method or\n  // the `source` property as a convenience for inlining compiled templates.\n  result.source = source;\n  if (isError(result)) {\n    throw result;\n  }\n  return result;\n}\nexport default template;", "map": {"version": 3, "names": ["assignInWith", "attempt", "baseValues", "customDefaultsAssignIn", "escapeStringChar", "isError", "isIterateeCall", "keys", "reInterpolate", "templateSettings", "toString", "INVALID_TEMPL_VAR_ERROR_TEXT", "reEmptyStringLeading", "reEmptyStringMiddle", "reEmptyStringTrailing", "reForbiddenIdentifierChars", "reEsTemplate", "reNoMatch", "reUnescapedString", "objectProto", "Object", "prototype", "hasOwnProperty", "template", "string", "options", "guard", "settings", "imports", "_", "undefined", "importsKeys", "importsValues", "isEscaping", "isEvaluating", "index", "interpolate", "source", "reDelimiters", "RegExp", "escape", "evaluate", "sourceURL", "call", "replace", "match", "escapeValue", "interpolateV<PERSON>ue", "esTemplateValue", "evaluateValue", "offset", "slice", "length", "variable", "test", "Error", "result", "Function", "apply"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/template.js"], "sourcesContent": ["import assignInWith from './assignInWith.js';\nimport attempt from './attempt.js';\nimport baseValues from './_baseValues.js';\nimport customDefaultsAssignIn from './_customDefaultsAssignIn.js';\nimport escapeStringChar from './_escapeStringChar.js';\nimport isError from './isError.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport keys from './keys.js';\nimport reInterpolate from './_reInterpolate.js';\nimport templateSettings from './templateSettings.js';\nimport toString from './toString.js';\n\n/** Error message constants. */\nvar INVALID_TEMPL_VAR_ERROR_TEXT = 'Invalid `variable` option passed into `_.template`';\n\n/** Used to match empty string literals in compiled template source. */\nvar reEmptyStringLeading = /\\b__p \\+= '';/g,\n    reEmptyStringMiddle = /\\b(__p \\+=) '' \\+/g,\n    reEmptyStringTrailing = /(__e\\(.*?\\)|\\b__t\\)) \\+\\n'';/g;\n\n/**\n * Used to validate the `validate` option in `_.template` variable.\n *\n * Forbids characters which could potentially change the meaning of the function argument definition:\n * - \"(),\" (modification of function parameters)\n * - \"=\" (default value)\n * - \"[]{}\" (destructuring of function parameters)\n * - \"/\" (beginning of a comment)\n * - whitespace\n */\nvar reForbiddenIdentifierChars = /[()=,{}\\[\\]\\/\\s]/;\n\n/**\n * Used to match\n * [ES template delimiters](http://ecma-international.org/ecma-262/7.0/#sec-template-literal-lexical-components).\n */\nvar reEsTemplate = /\\$\\{([^\\\\}]*(?:\\\\.[^\\\\}]*)*)\\}/g;\n\n/** Used to ensure capturing order of template delimiters. */\nvar reNoMatch = /($^)/;\n\n/** Used to match unescaped characters in compiled string literals. */\nvar reUnescapedString = /['\\n\\r\\u2028\\u2029\\\\]/g;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates a compiled template function that can interpolate data properties\n * in \"interpolate\" delimiters, HTML-escape interpolated data properties in\n * \"escape\" delimiters, and execute JavaScript in \"evaluate\" delimiters. Data\n * properties may be accessed as free variables in the template. If a setting\n * object is given, it takes precedence over `_.templateSettings` values.\n *\n * **Note:** In the development build `_.template` utilizes\n * [sourceURLs](http://www.html5rocks.com/en/tutorials/developertools/sourcemaps/#toc-sourceurl)\n * for easier debugging.\n *\n * For more information on precompiling templates see\n * [lodash's custom builds documentation](https://lodash.com/custom-builds).\n *\n * For more information on Chrome extension sandboxes see\n * [Chrome's extensions documentation](https://developer.chrome.com/extensions/sandboxingEval).\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category String\n * @param {string} [string=''] The template string.\n * @param {Object} [options={}] The options object.\n * @param {RegExp} [options.escape=_.templateSettings.escape]\n *  The HTML \"escape\" delimiter.\n * @param {RegExp} [options.evaluate=_.templateSettings.evaluate]\n *  The \"evaluate\" delimiter.\n * @param {Object} [options.imports=_.templateSettings.imports]\n *  An object to import into the template as free variables.\n * @param {RegExp} [options.interpolate=_.templateSettings.interpolate]\n *  The \"interpolate\" delimiter.\n * @param {string} [options.sourceURL='templateSources[n]']\n *  The sourceURL of the compiled template.\n * @param {string} [options.variable='obj']\n *  The data object variable name.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the compiled template function.\n * @example\n *\n * // Use the \"interpolate\" delimiter to create a compiled template.\n * var compiled = _.template('hello <%= user %>!');\n * compiled({ 'user': 'fred' });\n * // => 'hello fred!'\n *\n * // Use the HTML \"escape\" delimiter to escape data property values.\n * var compiled = _.template('<b><%- value %></b>');\n * compiled({ 'value': '<script>' });\n * // => '<b>&lt;script&gt;</b>'\n *\n * // Use the \"evaluate\" delimiter to execute JavaScript and generate HTML.\n * var compiled = _.template('<% _.forEach(users, function(user) { %><li><%- user %></li><% }); %>');\n * compiled({ 'users': ['fred', 'barney'] });\n * // => '<li>fred</li><li>barney</li>'\n *\n * // Use the internal `print` function in \"evaluate\" delimiters.\n * var compiled = _.template('<% print(\"hello \" + user); %>!');\n * compiled({ 'user': 'barney' });\n * // => 'hello barney!'\n *\n * // Use the ES template literal delimiter as an \"interpolate\" delimiter.\n * // Disable support by replacing the \"interpolate\" delimiter.\n * var compiled = _.template('hello ${ user }!');\n * compiled({ 'user': 'pebbles' });\n * // => 'hello pebbles!'\n *\n * // Use backslashes to treat delimiters as plain text.\n * var compiled = _.template('<%= \"\\\\<%- value %\\\\>\" %>');\n * compiled({ 'value': 'ignored' });\n * // => '<%- value %>'\n *\n * // Use the `imports` option to import `jQuery` as `jq`.\n * var text = '<% jq.each(users, function(user) { %><li><%- user %></li><% }); %>';\n * var compiled = _.template(text, { 'imports': { 'jq': jQuery } });\n * compiled({ 'users': ['fred', 'barney'] });\n * // => '<li>fred</li><li>barney</li>'\n *\n * // Use the `sourceURL` option to specify a custom sourceURL for the template.\n * var compiled = _.template('hello <%= user %>!', { 'sourceURL': '/basic/greeting.jst' });\n * compiled(data);\n * // => Find the source of \"greeting.jst\" under the Sources tab or Resources panel of the web inspector.\n *\n * // Use the `variable` option to ensure a with-statement isn't used in the compiled template.\n * var compiled = _.template('hi <%= data.user %>!', { 'variable': 'data' });\n * compiled.source;\n * // => function(data) {\n * //   var __t, __p = '';\n * //   __p += 'hi ' + ((__t = ( data.user )) == null ? '' : __t) + '!';\n * //   return __p;\n * // }\n *\n * // Use custom template delimiters.\n * _.templateSettings.interpolate = /{{([\\s\\S]+?)}}/g;\n * var compiled = _.template('hello {{ user }}!');\n * compiled({ 'user': 'mustache' });\n * // => 'hello mustache!'\n *\n * // Use the `source` property to inline compiled templates for meaningful\n * // line numbers in error messages and stack traces.\n * fs.writeFileSync(path.join(process.cwd(), 'jst.js'), '\\\n *   var JST = {\\\n *     \"main\": ' + _.template(mainText).source + '\\\n *   };\\\n * ');\n */\nfunction template(string, options, guard) {\n  // Based on John Resig's `tmpl` implementation\n  // (http://ejohn.org/blog/javascript-micro-templating/)\n  // and Laura Doktorova's doT.js (https://github.com/olado/doT).\n  var settings = templateSettings.imports._.templateSettings || templateSettings;\n\n  if (guard && isIterateeCall(string, options, guard)) {\n    options = undefined;\n  }\n  string = toString(string);\n  options = assignInWith({}, options, settings, customDefaultsAssignIn);\n\n  var imports = assignInWith({}, options.imports, settings.imports, customDefaultsAssignIn),\n      importsKeys = keys(imports),\n      importsValues = baseValues(imports, importsKeys);\n\n  var isEscaping,\n      isEvaluating,\n      index = 0,\n      interpolate = options.interpolate || reNoMatch,\n      source = \"__p += '\";\n\n  // Compile the regexp to match each delimiter.\n  var reDelimiters = RegExp(\n    (options.escape || reNoMatch).source + '|' +\n    interpolate.source + '|' +\n    (interpolate === reInterpolate ? reEsTemplate : reNoMatch).source + '|' +\n    (options.evaluate || reNoMatch).source + '|$'\n  , 'g');\n\n  // Use a sourceURL for easier debugging.\n  // The sourceURL gets injected into the source that's eval-ed, so be careful\n  // to normalize all kinds of whitespace, so e.g. newlines (and unicode versions of it) can't sneak in\n  // and escape the comment, thus injecting code that gets evaled.\n  var sourceURL = hasOwnProperty.call(options, 'sourceURL')\n    ? ('//# sourceURL=' +\n       (options.sourceURL + '').replace(/\\s/g, ' ') +\n       '\\n')\n    : '';\n\n  string.replace(reDelimiters, function(match, escapeValue, interpolateValue, esTemplateValue, evaluateValue, offset) {\n    interpolateValue || (interpolateValue = esTemplateValue);\n\n    // Escape characters that can't be included in string literals.\n    source += string.slice(index, offset).replace(reUnescapedString, escapeStringChar);\n\n    // Replace delimiters with snippets.\n    if (escapeValue) {\n      isEscaping = true;\n      source += \"' +\\n__e(\" + escapeValue + \") +\\n'\";\n    }\n    if (evaluateValue) {\n      isEvaluating = true;\n      source += \"';\\n\" + evaluateValue + \";\\n__p += '\";\n    }\n    if (interpolateValue) {\n      source += \"' +\\n((__t = (\" + interpolateValue + \")) == null ? '' : __t) +\\n'\";\n    }\n    index = offset + match.length;\n\n    // The JS engine embedded in Adobe products needs `match` returned in\n    // order to produce the correct `offset` value.\n    return match;\n  });\n\n  source += \"';\\n\";\n\n  // If `variable` is not specified wrap a with-statement around the generated\n  // code to add the data object to the top of the scope chain.\n  var variable = hasOwnProperty.call(options, 'variable') && options.variable;\n  if (!variable) {\n    source = 'with (obj) {\\n' + source + '\\n}\\n';\n  }\n  // Throw an error if a forbidden character was found in `variable`, to prevent\n  // potential command injection attacks.\n  else if (reForbiddenIdentifierChars.test(variable)) {\n    throw new Error(INVALID_TEMPL_VAR_ERROR_TEXT);\n  }\n\n  // Cleanup code by stripping empty strings.\n  source = (isEvaluating ? source.replace(reEmptyStringLeading, '') : source)\n    .replace(reEmptyStringMiddle, '$1')\n    .replace(reEmptyStringTrailing, '$1;');\n\n  // Frame code as the function body.\n  source = 'function(' + (variable || 'obj') + ') {\\n' +\n    (variable\n      ? ''\n      : 'obj || (obj = {});\\n'\n    ) +\n    \"var __t, __p = ''\" +\n    (isEscaping\n       ? ', __e = _.escape'\n       : ''\n    ) +\n    (isEvaluating\n      ? ', __j = Array.prototype.join;\\n' +\n        \"function print() { __p += __j.call(arguments, '') }\\n\"\n      : ';\\n'\n    ) +\n    source +\n    'return __p\\n}';\n\n  var result = attempt(function() {\n    return Function(importsKeys, sourceURL + 'return ' + source)\n      .apply(undefined, importsValues);\n  });\n\n  // Provide the compiled function's source by its `toString` method or\n  // the `source` property as a convenience for inlining compiled templates.\n  result.source = source;\n  if (isError(result)) {\n    throw result;\n  }\n  return result;\n}\n\nexport default template;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,sBAAsB,MAAM,8BAA8B;AACjE,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,4BAA4B,GAAG,oDAAoD;;AAEvF;AACA,IAAIC,oBAAoB,GAAG,gBAAgB;EACvCC,mBAAmB,GAAG,oBAAoB;EAC1CC,qBAAqB,GAAG,+BAA+B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,0BAA0B,GAAG,kBAAkB;;AAEnD;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,iCAAiC;;AAEpD;AACA,IAAIC,SAAS,GAAG,MAAM;;AAEtB;AACA,IAAIC,iBAAiB,GAAG,wBAAwB;;AAEhD;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACxC;EACA;EACA;EACA,IAAIC,QAAQ,GAAGlB,gBAAgB,CAACmB,OAAO,CAACC,CAAC,CAACpB,gBAAgB,IAAIA,gBAAgB;EAE9E,IAAIiB,KAAK,IAAIpB,cAAc,CAACkB,MAAM,EAAEC,OAAO,EAAEC,KAAK,CAAC,EAAE;IACnDD,OAAO,GAAGK,SAAS;EACrB;EACAN,MAAM,GAAGd,QAAQ,CAACc,MAAM,CAAC;EACzBC,OAAO,GAAGzB,YAAY,CAAC,CAAC,CAAC,EAAEyB,OAAO,EAAEE,QAAQ,EAAExB,sBAAsB,CAAC;EAErE,IAAIyB,OAAO,GAAG5B,YAAY,CAAC,CAAC,CAAC,EAAEyB,OAAO,CAACG,OAAO,EAAED,QAAQ,CAACC,OAAO,EAAEzB,sBAAsB,CAAC;IACrF4B,WAAW,GAAGxB,IAAI,CAACqB,OAAO,CAAC;IAC3BI,aAAa,GAAG9B,UAAU,CAAC0B,OAAO,EAAEG,WAAW,CAAC;EAEpD,IAAIE,UAAU;IACVC,YAAY;IACZC,KAAK,GAAG,CAAC;IACTC,WAAW,GAAGX,OAAO,CAACW,WAAW,IAAInB,SAAS;IAC9CoB,MAAM,GAAG,UAAU;;EAEvB;EACA,IAAIC,YAAY,GAAGC,MAAM,CACvB,CAACd,OAAO,CAACe,MAAM,IAAIvB,SAAS,EAAEoB,MAAM,GAAG,GAAG,GAC1CD,WAAW,CAACC,MAAM,GAAG,GAAG,GACxB,CAACD,WAAW,KAAK5B,aAAa,GAAGQ,YAAY,GAAGC,SAAS,EAAEoB,MAAM,GAAG,GAAG,GACvE,CAACZ,OAAO,CAACgB,QAAQ,IAAIxB,SAAS,EAAEoB,MAAM,GAAG,IAAI,EAC7C,GAAG,CAAC;;EAEN;EACA;EACA;EACA;EACA,IAAIK,SAAS,GAAGpB,cAAc,CAACqB,IAAI,CAAClB,OAAO,EAAE,WAAW,CAAC,GACpD,gBAAgB,GAChB,CAACA,OAAO,CAACiB,SAAS,GAAG,EAAE,EAAEE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAC5C,IAAI,GACL,EAAE;EAENpB,MAAM,CAACoB,OAAO,CAACN,YAAY,EAAE,UAASO,KAAK,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,MAAM,EAAE;IAClHH,gBAAgB,KAAKA,gBAAgB,GAAGC,eAAe,CAAC;;IAExD;IACAX,MAAM,IAAIb,MAAM,CAAC2B,KAAK,CAAChB,KAAK,EAAEe,MAAM,CAAC,CAACN,OAAO,CAAC1B,iBAAiB,EAAEd,gBAAgB,CAAC;;IAElF;IACA,IAAI0C,WAAW,EAAE;MACfb,UAAU,GAAG,IAAI;MACjBI,MAAM,IAAI,WAAW,GAAGS,WAAW,GAAG,QAAQ;IAChD;IACA,IAAIG,aAAa,EAAE;MACjBf,YAAY,GAAG,IAAI;MACnBG,MAAM,IAAI,MAAM,GAAGY,aAAa,GAAG,aAAa;IAClD;IACA,IAAIF,gBAAgB,EAAE;MACpBV,MAAM,IAAI,gBAAgB,GAAGU,gBAAgB,GAAG,6BAA6B;IAC/E;IACAZ,KAAK,GAAGe,MAAM,GAAGL,KAAK,CAACO,MAAM;;IAE7B;IACA;IACA,OAAOP,KAAK;EACd,CAAC,CAAC;EAEFR,MAAM,IAAI,MAAM;;EAEhB;EACA;EACA,IAAIgB,QAAQ,GAAG/B,cAAc,CAACqB,IAAI,CAAClB,OAAO,EAAE,UAAU,CAAC,IAAIA,OAAO,CAAC4B,QAAQ;EAC3E,IAAI,CAACA,QAAQ,EAAE;IACbhB,MAAM,GAAG,gBAAgB,GAAGA,MAAM,GAAG,OAAO;EAC9C;EACA;EACA;EAAA,KACK,IAAItB,0BAA0B,CAACuC,IAAI,CAACD,QAAQ,CAAC,EAAE;IAClD,MAAM,IAAIE,KAAK,CAAC5C,4BAA4B,CAAC;EAC/C;;EAEA;EACA0B,MAAM,GAAG,CAACH,YAAY,GAAGG,MAAM,CAACO,OAAO,CAAChC,oBAAoB,EAAE,EAAE,CAAC,GAAGyB,MAAM,EACvEO,OAAO,CAAC/B,mBAAmB,EAAE,IAAI,CAAC,CAClC+B,OAAO,CAAC9B,qBAAqB,EAAE,KAAK,CAAC;;EAExC;EACAuB,MAAM,GAAG,WAAW,IAAIgB,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,IACjDA,QAAQ,GACL,EAAE,GACF,sBAAsB,CACzB,GACD,mBAAmB,IAClBpB,UAAU,GACN,kBAAkB,GAClB,EAAE,CACN,IACAC,YAAY,GACT,iCAAiC,GACjC,uDAAuD,GACvD,KAAK,CACR,GACDG,MAAM,GACN,eAAe;EAEjB,IAAImB,MAAM,GAAGvD,OAAO,CAAC,YAAW;IAC9B,OAAOwD,QAAQ,CAAC1B,WAAW,EAAEW,SAAS,GAAG,SAAS,GAAGL,MAAM,CAAC,CACzDqB,KAAK,CAAC5B,SAAS,EAAEE,aAAa,CAAC;EACpC,CAAC,CAAC;;EAEF;EACA;EACAwB,MAAM,CAACnB,MAAM,GAAGA,MAAM;EACtB,IAAIhC,OAAO,CAACmD,MAAM,CAAC,EAAE;IACnB,MAAMA,MAAM;EACd;EACA,OAAOA,MAAM;AACf;AAEA,eAAejC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}