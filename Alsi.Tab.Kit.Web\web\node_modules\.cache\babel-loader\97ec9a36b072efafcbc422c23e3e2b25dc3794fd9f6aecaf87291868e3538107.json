{"ast": null, "code": "import Stack from './_Stack.js';\nimport assignMergeValue from './_assignMergeValue.js';\nimport baseFor from './_baseFor.js';\nimport baseMergeDeep from './_baseMergeDeep.js';\nimport isObject from './isObject.js';\nimport keysIn from './keysIn.js';\nimport safeGet from './_safeGet.js';\n\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMerge(object, source, srcIndex, customizer, stack) {\n  if (object === source) {\n    return;\n  }\n  baseFor(source, function (srcValue, key) {\n    stack || (stack = new Stack());\n    if (isObject(srcValue)) {\n      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n    } else {\n      var newValue = customizer ? customizer(safeGet(object, key), srcValue, key + '', object, source, stack) : undefined;\n      if (newValue === undefined) {\n        newValue = srcValue;\n      }\n      assignMergeValue(object, key, newValue);\n    }\n  }, keysIn);\n}\nexport default baseMerge;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "assignMergeValue", "baseFor", "baseMergeDeep", "isObject", "keysIn", "safeGet", "baseMerge", "object", "source", "srcIndex", "customizer", "stack", "srcValue", "key", "newValue", "undefined"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_baseMerge.js"], "sourcesContent": ["import Stack from './_Stack.js';\nimport assignMergeValue from './_assignMergeValue.js';\nimport baseFor from './_baseFor.js';\nimport baseMergeDeep from './_baseMergeDeep.js';\nimport isObject from './isObject.js';\nimport keysIn from './keysIn.js';\nimport safeGet from './_safeGet.js';\n\n/**\n * The base implementation of `_.merge` without support for multiple sources.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} [customizer] The function to customize merged values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMerge(object, source, srcIndex, customizer, stack) {\n  if (object === source) {\n    return;\n  }\n  baseFor(source, function(srcValue, key) {\n    stack || (stack = new Stack);\n    if (isObject(srcValue)) {\n      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);\n    }\n    else {\n      var newValue = customizer\n        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)\n        : undefined;\n\n      if (newValue === undefined) {\n        newValue = srcValue;\n      }\n      assignMergeValue(object, key, newValue);\n    }\n  }, keysIn);\n}\n\nexport default baseMerge;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAE;EAC9D,IAAIJ,MAAM,KAAKC,MAAM,EAAE;IACrB;EACF;EACAP,OAAO,CAACO,MAAM,EAAE,UAASI,QAAQ,EAAEC,GAAG,EAAE;IACtCF,KAAK,KAAKA,KAAK,GAAG,IAAIZ,KAAK,CAAD,CAAC,CAAC;IAC5B,IAAII,QAAQ,CAACS,QAAQ,CAAC,EAAE;MACtBV,aAAa,CAACK,MAAM,EAAEC,MAAM,EAAEK,GAAG,EAAEJ,QAAQ,EAAEH,SAAS,EAAEI,UAAU,EAAEC,KAAK,CAAC;IAC5E,CAAC,MACI;MACH,IAAIG,QAAQ,GAAGJ,UAAU,GACrBA,UAAU,CAACL,OAAO,CAACE,MAAM,EAAEM,GAAG,CAAC,EAAED,QAAQ,EAAGC,GAAG,GAAG,EAAE,EAAGN,MAAM,EAAEC,MAAM,EAAEG,KAAK,CAAC,GAC7EI,SAAS;MAEb,IAAID,QAAQ,KAAKC,SAAS,EAAE;QAC1BD,QAAQ,GAAGF,QAAQ;MACrB;MACAZ,gBAAgB,CAACO,MAAM,EAAEM,GAAG,EAAEC,QAAQ,CAAC;IACzC;EACF,CAAC,EAAEV,MAAM,CAAC;AACZ;AAEA,eAAeE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}