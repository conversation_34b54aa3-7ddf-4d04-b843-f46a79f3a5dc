{"ast": null, "code": "import { timePanelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst panelTimeRangeProps = buildProps({\n  ...timePanelSharedProps,\n  parsedValue: {\n    type: definePropType(Array)\n  }\n});\nexport { panelTimeRangeProps };", "map": {"version": 3, "names": ["panelTimeRangeProps", "buildProps", "timePanelSharedProps", "parsedValue", "type", "definePropType", "Array"], "sources": ["../../../../../../../packages/components/time-picker/src/props/panel-time-range.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { timePanelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelTimeRangeProps = buildProps({\n  ...timePanelSharedProps,\n  parsedValue: {\n    type: definePropType<[Dayjs, Dayjs]>(Array),\n  },\n} as const)\n\nexport type PanelTimeRangeProps = ExtractPropTypes<typeof panelTimeRangeProps>\n"], "mappings": ";;AAEY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5C,GAAGC,oBAAoB;EACvBC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAACC,KAAK;EAC9B;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}