{"ast": null, "code": "import { defineComponent, renderSlot, openBlock, create<PERSON>lock, Teleport as Teleport$1 } from 'vue';\nimport { teleportProps } from './teleport.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"teleport\",\n  props: teleportProps,\n  setup(__props) {\n    return (_ctx, _cache) => {\n      return _ctx.disabled ? renderSlot(_ctx.$slots, \"default\", {\n        key: 0\n      }) : (openBlock(), createBlock(Teleport$1, {\n        key: 1,\n        to: _ctx.to\n      }, [renderSlot(_ctx.$slots, \"default\")], 8, [\"to\"]));\n    };\n  }\n});\nvar Teleport = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"teleport.vue\"]]);\nexport { Teleport as default };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import { defineComponent, renderSlot, openBlock, createBlock, Teleport as Teleport$1 } from 'vue';\nimport { teleportProps } from './teleport.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\n\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  __name: \"teleport\",\n  props: teleportProps,\n  setup(__props) {\n    return (_ctx, _cache) => {\n      return _ctx.disabled ? renderSlot(_ctx.$slots, \"default\", { key: 0 }) : (openBlock(), createBlock(Teleport$1, {\n        key: 1,\n        to: _ctx.to\n      }, [\n        renderSlot(_ctx.$slots, \"default\")\n      ], 8, [\"to\"]));\n    };\n  }\n});\nvar Teleport = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"__file\", \"teleport.vue\"]]);\n\nexport { Teleport as default };\n//# sourceMappingURL=teleport2.mjs.map\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}