{"ast": null, "code": "import baseFill from './_baseFill.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Fills elements of `array` with `value` from `start` up to, but not\n * including, `end`.\n *\n * **Note:** This method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Array\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _.fill(array, 'a');\n * console.log(array);\n * // => ['a', 'a', 'a']\n *\n * _.fill(Array(3), 2);\n * // => [2, 2, 2]\n *\n * _.fill([4, 6, 8, 10], '*', 1, 3);\n * // => [4, '*', '*', 10]\n */\nfunction fill(array, value, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (start && typeof start != 'number' && isIterateeCall(array, value, start)) {\n    start = 0;\n    end = length;\n  }\n  return baseFill(array, value, start, end);\n}\nexport default fill;", "map": {"version": 3, "names": ["baseFill", "isIterateeCall", "fill", "array", "value", "start", "end", "length"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/fill.js"], "sourcesContent": ["import baseFill from './_baseFill.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Fills elements of `array` with `value` from `start` up to, but not\n * including, `end`.\n *\n * **Note:** This method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Array\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _.fill(array, 'a');\n * console.log(array);\n * // => ['a', 'a', 'a']\n *\n * _.fill(Array(3), 2);\n * // => [2, 2, 2]\n *\n * _.fill([4, 6, 8, 10], '*', 1, 3);\n * // => [4, '*', '*', 10]\n */\nfunction fill(array, value, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (start && typeof start != 'number' && isIterateeCall(array, value, start)) {\n    start = 0;\n    end = length;\n  }\n  return baseFill(array, value, start, end);\n}\n\nexport default fill;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACtC,IAAIC,MAAM,GAAGJ,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACI,MAAM;EAC7C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EACA,IAAIF,KAAK,IAAI,OAAOA,KAAK,IAAI,QAAQ,IAAIJ,cAAc,CAACE,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC,EAAE;IAC5EA,KAAK,GAAG,CAAC;IACTC,GAAG,GAAGC,MAAM;EACd;EACA,OAAOP,QAAQ,CAACG,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC;AAC3C;AAEA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}