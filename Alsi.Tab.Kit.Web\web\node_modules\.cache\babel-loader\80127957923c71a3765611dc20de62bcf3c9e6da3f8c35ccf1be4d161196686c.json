{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { reactive } from 'vue';\nimport { markNodeData, NODE_KEY } from './util.mjs';\nimport { hasOwn, isArray, isFunction, isString } from '@vue/shared';\nimport { isBoolean, isUndefined } from '../../../../utils/types.mjs';\nconst getChildState = node => {\n  let all = true;\n  let none = true;\n  let allWithoutDisable = true;\n  for (let i = 0, j = node.length; i < j; i++) {\n    const n = node[i];\n    if (n.checked !== true || n.indeterminate) {\n      all = false;\n      if (!n.disabled) {\n        allWithoutDisable = false;\n      }\n    }\n    if (n.checked !== false || n.indeterminate) {\n      none = false;\n    }\n  }\n  return {\n    all,\n    none,\n    allWithoutDisable,\n    half: !all && !none\n  };\n};\nconst reInitChecked = function (node) {\n  if (node.childNodes.length === 0 || node.loading) return;\n  const {\n    all,\n    none,\n    half\n  } = getChildState(node.childNodes);\n  if (all) {\n    node.checked = true;\n    node.indeterminate = false;\n  } else if (half) {\n    node.checked = false;\n    node.indeterminate = true;\n  } else if (none) {\n    node.checked = false;\n    node.indeterminate = false;\n  }\n  const parent = node.parent;\n  if (!parent || parent.level === 0) return;\n  if (!node.store.checkStrictly) {\n    reInitChecked(parent);\n  }\n};\nconst getPropertyFromData = function (node, prop) {\n  const props = node.store.props;\n  const data = node.data || {};\n  const config = props[prop];\n  if (isFunction(config)) {\n    return config(data, node);\n  } else if (isString(config)) {\n    return data[config];\n  } else if (isUndefined(config)) {\n    const dataProp = data[prop];\n    return isUndefined(dataProp) ? \"\" : dataProp;\n  }\n};\nlet nodeIdSeed = 0;\nclass Node {\n  constructor(options) {\n    this.id = nodeIdSeed++;\n    this.text = null;\n    this.checked = false;\n    this.indeterminate = false;\n    this.data = null;\n    this.expanded = false;\n    this.parent = null;\n    this.visible = true;\n    this.isCurrent = false;\n    this.canFocus = false;\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        this[name] = options[name];\n      }\n    }\n    this.level = 0;\n    this.loaded = false;\n    this.childNodes = [];\n    this.loading = false;\n    if (this.parent) {\n      this.level = this.parent.level + 1;\n    }\n  }\n  initialize() {\n    const store = this.store;\n    if (!store) {\n      throw new Error(\"[Node]store is required!\");\n    }\n    store.registerNode(this);\n    const props = store.props;\n    if (props && typeof props.isLeaf !== \"undefined\") {\n      const isLeaf = getPropertyFromData(this, \"isLeaf\");\n      if (isBoolean(isLeaf)) {\n        this.isLeafByUser = isLeaf;\n      }\n    }\n    if (store.lazy !== true && this.data) {\n      this.setData(this.data);\n      if (store.defaultExpandAll) {\n        this.expanded = true;\n        this.canFocus = true;\n      }\n    } else if (this.level > 0 && store.lazy && store.defaultExpandAll && !this.isLeafByUser) {\n      this.expand();\n    }\n    if (!isArray(this.data)) {\n      markNodeData(this, this.data);\n    }\n    if (!this.data) return;\n    const defaultExpandedKeys = store.defaultExpandedKeys;\n    const key = store.key;\n    if (key && defaultExpandedKeys && defaultExpandedKeys.includes(this.key)) {\n      this.expand(null, store.autoExpandParent);\n    }\n    if (key && store.currentNodeKey !== void 0 && this.key === store.currentNodeKey) {\n      store.currentNode = this;\n      store.currentNode.isCurrent = true;\n    }\n    if (store.lazy) {\n      store._initDefaultCheckedNode(this);\n    }\n    this.updateLeafState();\n    if (this.parent && (this.level === 1 || this.parent.expanded === true)) this.canFocus = true;\n  }\n  setData(data) {\n    if (!isArray(data)) {\n      markNodeData(this, data);\n    }\n    this.data = data;\n    this.childNodes = [];\n    let children;\n    if (this.level === 0 && isArray(this.data)) {\n      children = this.data;\n    } else {\n      children = getPropertyFromData(this, \"children\") || [];\n    }\n    for (let i = 0, j = children.length; i < j; i++) {\n      this.insertChild({\n        data: children[i]\n      });\n    }\n  }\n  get label() {\n    return getPropertyFromData(this, \"label\");\n  }\n  get key() {\n    const nodeKey = this.store.key;\n    if (this.data) return this.data[nodeKey];\n    return null;\n  }\n  get disabled() {\n    return getPropertyFromData(this, \"disabled\");\n  }\n  get nextSibling() {\n    const parent = this.parent;\n    if (parent) {\n      const index = parent.childNodes.indexOf(this);\n      if (index > -1) {\n        return parent.childNodes[index + 1];\n      }\n    }\n    return null;\n  }\n  get previousSibling() {\n    const parent = this.parent;\n    if (parent) {\n      const index = parent.childNodes.indexOf(this);\n      if (index > -1) {\n        return index > 0 ? parent.childNodes[index - 1] : null;\n      }\n    }\n    return null;\n  }\n  contains(target, deep = true) {\n    return (this.childNodes || []).some(child => child === target || deep && child.contains(target));\n  }\n  remove() {\n    const parent = this.parent;\n    if (parent) {\n      parent.removeChild(this);\n    }\n  }\n  insertChild(child, index, batch) {\n    if (!child) throw new Error(\"InsertChild error: child is required.\");\n    if (!(child instanceof Node)) {\n      if (!batch) {\n        const children = this.getChildren(true);\n        if (!children.includes(child.data)) {\n          if (isUndefined(index) || index < 0) {\n            children.push(child.data);\n          } else {\n            children.splice(index, 0, child.data);\n          }\n        }\n      }\n      Object.assign(child, {\n        parent: this,\n        store: this.store\n      });\n      child = reactive(new Node(child));\n      if (child instanceof Node) {\n        child.initialize();\n      }\n    }\n    child.level = this.level + 1;\n    if (isUndefined(index) || index < 0) {\n      this.childNodes.push(child);\n    } else {\n      this.childNodes.splice(index, 0, child);\n    }\n    this.updateLeafState();\n  }\n  insertBefore(child, ref) {\n    let index;\n    if (ref) {\n      index = this.childNodes.indexOf(ref);\n    }\n    this.insertChild(child, index);\n  }\n  insertAfter(child, ref) {\n    let index;\n    if (ref) {\n      index = this.childNodes.indexOf(ref);\n      if (index !== -1) index += 1;\n    }\n    this.insertChild(child, index);\n  }\n  removeChild(child) {\n    const children = this.getChildren() || [];\n    const dataIndex = children.indexOf(child.data);\n    if (dataIndex > -1) {\n      children.splice(dataIndex, 1);\n    }\n    const index = this.childNodes.indexOf(child);\n    if (index > -1) {\n      this.store && this.store.deregisterNode(child);\n      child.parent = null;\n      this.childNodes.splice(index, 1);\n    }\n    this.updateLeafState();\n  }\n  removeChildByData(data) {\n    let targetNode = null;\n    for (let i = 0; i < this.childNodes.length; i++) {\n      if (this.childNodes[i].data === data) {\n        targetNode = this.childNodes[i];\n        break;\n      }\n    }\n    if (targetNode) {\n      this.removeChild(targetNode);\n    }\n  }\n  expand(callback, expandParent) {\n    const done = () => {\n      if (expandParent) {\n        let parent = this.parent;\n        while (parent.level > 0) {\n          parent.expanded = true;\n          parent = parent.parent;\n        }\n      }\n      this.expanded = true;\n      if (callback) callback();\n      this.childNodes.forEach(item => {\n        item.canFocus = true;\n      });\n    };\n    if (this.shouldLoadData()) {\n      this.loadData(data => {\n        if (isArray(data)) {\n          if (this.checked) {\n            this.setChecked(true, true);\n          } else if (!this.store.checkStrictly) {\n            reInitChecked(this);\n          }\n          done();\n        }\n      });\n    } else {\n      done();\n    }\n  }\n  doCreateChildren(array, defaultProps = {}) {\n    array.forEach(item => {\n      this.insertChild(Object.assign({\n        data: item\n      }, defaultProps), void 0, true);\n    });\n  }\n  collapse() {\n    this.expanded = false;\n    this.childNodes.forEach(item => {\n      item.canFocus = false;\n    });\n  }\n  shouldLoadData() {\n    return this.store.lazy === true && this.store.load && !this.loaded;\n  }\n  updateLeafState() {\n    if (this.store.lazy === true && this.loaded !== true && typeof this.isLeafByUser !== \"undefined\") {\n      this.isLeaf = this.isLeafByUser;\n      return;\n    }\n    const childNodes = this.childNodes;\n    if (!this.store.lazy || this.store.lazy === true && this.loaded === true) {\n      this.isLeaf = !childNodes || childNodes.length === 0;\n      return;\n    }\n    this.isLeaf = false;\n  }\n  setChecked(value, deep, recursion, passValue) {\n    this.indeterminate = value === \"half\";\n    this.checked = value === true;\n    if (this.store.checkStrictly) return;\n    if (!(this.shouldLoadData() && !this.store.checkDescendants)) {\n      const {\n        all,\n        allWithoutDisable\n      } = getChildState(this.childNodes);\n      if (!this.isLeaf && !all && allWithoutDisable) {\n        this.checked = false;\n        value = false;\n      }\n      const handleDescendants = () => {\n        if (deep) {\n          const childNodes = this.childNodes;\n          for (let i = 0, j = childNodes.length; i < j; i++) {\n            const child = childNodes[i];\n            passValue = passValue || value !== false;\n            const isCheck = child.disabled ? child.checked : passValue;\n            child.setChecked(isCheck, deep, true, passValue);\n          }\n          const {\n            half,\n            all: all2\n          } = getChildState(childNodes);\n          if (!all2) {\n            this.checked = all2;\n            this.indeterminate = half;\n          }\n        }\n      };\n      if (this.shouldLoadData()) {\n        this.loadData(() => {\n          handleDescendants();\n          reInitChecked(this);\n        }, {\n          checked: value !== false\n        });\n        return;\n      } else {\n        handleDescendants();\n      }\n    }\n    const parent = this.parent;\n    if (!parent || parent.level === 0) return;\n    if (!recursion) {\n      reInitChecked(parent);\n    }\n  }\n  getChildren(forceInit = false) {\n    if (this.level === 0) return this.data;\n    const data = this.data;\n    if (!data) return null;\n    const props = this.store.props;\n    let children = \"children\";\n    if (props) {\n      children = props.children || \"children\";\n    }\n    if (isUndefined(data[children])) {\n      data[children] = null;\n    }\n    if (forceInit && !data[children]) {\n      data[children] = [];\n    }\n    return data[children];\n  }\n  updateChildren() {\n    const newData = this.getChildren() || [];\n    const oldData = this.childNodes.map(node => node.data);\n    const newDataMap = {};\n    const newNodes = [];\n    newData.forEach((item, index) => {\n      const key = item[NODE_KEY];\n      const isNodeExists = !!key && oldData.findIndex(data => data[NODE_KEY] === key) >= 0;\n      if (isNodeExists) {\n        newDataMap[key] = {\n          index,\n          data: item\n        };\n      } else {\n        newNodes.push({\n          index,\n          data: item\n        });\n      }\n    });\n    if (!this.store.lazy) {\n      oldData.forEach(item => {\n        if (!newDataMap[item[NODE_KEY]]) this.removeChildByData(item);\n      });\n    }\n    newNodes.forEach(({\n      index,\n      data\n    }) => {\n      this.insertChild({\n        data\n      }, index);\n    });\n    this.updateLeafState();\n  }\n  loadData(callback, defaultProps = {}) {\n    if (this.store.lazy === true && this.store.load && !this.loaded && (!this.loading || Object.keys(defaultProps).length)) {\n      this.loading = true;\n      const resolve = children => {\n        this.childNodes = [];\n        this.doCreateChildren(children, defaultProps);\n        this.loaded = true;\n        this.loading = false;\n        this.updateLeafState();\n        if (callback) {\n          callback.call(this, children);\n        }\n      };\n      const reject = () => {\n        this.loading = false;\n      };\n      this.store.load(this, resolve, reject);\n    } else {\n      if (callback) {\n        callback.call(this);\n      }\n    }\n  }\n  eachNode(callback) {\n    const arr = [this];\n    while (arr.length) {\n      const node = arr.shift();\n      arr.unshift(...node.childNodes);\n      callback(node);\n    }\n  }\n  reInitChecked() {\n    if (this.store.checkStrictly) return;\n    reInitChecked(this);\n  }\n}\nexport { Node as default, getChildState };", "map": {"version": 3, "names": ["getChildState", "node", "all", "none", "allWithoutDisable", "i", "j", "length", "n", "checked", "indeterminate", "disabled", "half", "reInitChecked", "childNodes", "loading", "parent", "level", "store", "checkStrictly", "getPropertyFromData", "prop", "props", "data", "config", "isFunction", "isString", "isUndefined", "dataProp", "nodeIdSeed", "Node", "constructor", "options", "id", "text", "expanded", "visible", "isCurrent", "canFocus", "name", "hasOwn", "loaded", "initialize", "Error", "registerNode", "<PERSON><PERSON><PERSON><PERSON>", "isBoolean", "isLeafByUser", "lazy", "setData", "defaultExpandAll", "expand", "isArray", "markNodeData", "defaultExpandedKeys", "key", "includes", "autoExpandParent", "currentNodeKey", "currentNode", "_initDefaultCheckedNode", "updateLeafState", "children", "<PERSON><PERSON><PERSON><PERSON>", "label", "nodeKey", "nextS<PERSON>ling", "index", "indexOf", "previousSibling", "contains", "target", "deep", "some", "child", "remove", "<PERSON><PERSON><PERSON><PERSON>", "batch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "splice", "Object", "assign", "reactive", "insertBefore", "ref", "insertAfter", "dataIndex", "deregisterNode", "removeChildByData", "targetNode", "callback", "expandParent", "done", "for<PERSON>ach", "item", "shouldLoadData", "loadData", "setChecked", "doCreate<PERSON><PERSON><PERSON>n", "array", "defaultProps", "collapse", "load", "value", "recursion", "passValue", "checkDescendants", "handleDescendants", "is<PERSON><PERSON><PERSON>", "all2", "forceInit", "update<PERSON><PERSON><PERSON>n", "newData", "oldData", "map", "newDataMap", "newNodes", "NODE_KEY", "isNodeExists", "findIndex", "keys", "resolve", "call", "reject", "eachNode", "arr", "shift", "unshift"], "sources": ["../../../../../../../packages/components/tree/src/model/node.ts"], "sourcesContent": ["// @ts-nocheck\nimport { reactive } from 'vue'\nimport {\n  hasOwn,\n  isArray,\n  isBoolean,\n  isFunction,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport { NODE_KEY, markNodeData } from './util'\nimport type TreeStore from './tree-store'\n\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  FakeNode,\n  TreeKey,\n  TreeNodeChildState,\n  TreeNodeData,\n  TreeNodeLoadedDefaultProps,\n  TreeNodeOptions,\n} from '../tree.type'\n\nexport const getChildState = (node: Node[]): TreeNodeChildState => {\n  let all = true\n  let none = true\n  let allWithoutDisable = true\n  for (let i = 0, j = node.length; i < j; i++) {\n    const n = node[i]\n    if (n.checked !== true || n.indeterminate) {\n      all = false\n      if (!n.disabled) {\n        allWithoutDisable = false\n      }\n    }\n    if (n.checked !== false || n.indeterminate) {\n      none = false\n    }\n  }\n\n  return { all, none, allWithoutDisable, half: !all && !none }\n}\n\nconst reInitChecked = function (node: Node): void {\n  if (node.childNodes.length === 0 || node.loading) return\n\n  const { all, none, half } = getChildState(node.childNodes)\n  if (all) {\n    node.checked = true\n    node.indeterminate = false\n  } else if (half) {\n    node.checked = false\n    node.indeterminate = true\n  } else if (none) {\n    node.checked = false\n    node.indeterminate = false\n  }\n\n  const parent = node.parent\n  if (!parent || parent.level === 0) return\n\n  if (!node.store.checkStrictly) {\n    reInitChecked(parent)\n  }\n}\n\nconst getPropertyFromData = function (node: Node, prop: string): any {\n  const props = node.store.props\n  const data = node.data || {}\n  const config = props[prop]\n\n  if (isFunction(config)) {\n    return config(data, node)\n  } else if (isString(config)) {\n    return data[config]\n  } else if (isUndefined(config)) {\n    const dataProp = data[prop]\n    return isUndefined(dataProp) ? '' : dataProp\n  }\n}\n\nlet nodeIdSeed = 0\n\nclass Node {\n  id: number\n  text: string\n  checked: boolean\n  indeterminate: boolean\n  data: TreeNodeData\n  expanded: boolean\n  parent: Node\n  visible: boolean\n  isCurrent: boolean\n  store: TreeStore\n  isLeafByUser: boolean\n  isLeaf: boolean\n  canFocus: boolean\n\n  level: number\n  loaded: boolean\n  childNodes: Node[]\n  loading: boolean\n\n  constructor(options: TreeNodeOptions) {\n    this.id = nodeIdSeed++\n    this.text = null\n    this.checked = false\n    this.indeterminate = false\n    this.data = null\n    this.expanded = false\n    this.parent = null\n    this.visible = true\n    this.isCurrent = false\n    this.canFocus = false\n\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        this[name] = options[name]\n      }\n    }\n\n    // internal\n    this.level = 0\n    this.loaded = false\n    this.childNodes = []\n    this.loading = false\n\n    if (this.parent) {\n      this.level = this.parent.level + 1\n    }\n  }\n\n  initialize() {\n    const store = this.store\n    if (!store) {\n      throw new Error('[Node]store is required!')\n    }\n    store.registerNode(this)\n\n    const props = store.props\n    if (props && typeof props.isLeaf !== 'undefined') {\n      const isLeaf = getPropertyFromData(this, 'isLeaf')\n      if (isBoolean(isLeaf)) {\n        this.isLeafByUser = isLeaf\n      }\n    }\n\n    if (store.lazy !== true && this.data) {\n      this.setData(this.data)\n\n      if (store.defaultExpandAll) {\n        this.expanded = true\n        this.canFocus = true\n      }\n    } else if (\n      this.level > 0 &&\n      store.lazy &&\n      store.defaultExpandAll &&\n      !this.isLeafByUser\n    ) {\n      this.expand()\n    }\n    if (!isArray(this.data)) {\n      markNodeData(this, this.data)\n    }\n    if (!this.data) return\n\n    const defaultExpandedKeys = store.defaultExpandedKeys\n    const key = store.key\n\n    if (key && defaultExpandedKeys && defaultExpandedKeys.includes(this.key)) {\n      this.expand(null, store.autoExpandParent)\n    }\n\n    if (\n      key &&\n      store.currentNodeKey !== undefined &&\n      this.key === store.currentNodeKey\n    ) {\n      store.currentNode = this\n      store.currentNode.isCurrent = true\n    }\n\n    if (store.lazy) {\n      store._initDefaultCheckedNode(this)\n    }\n\n    this.updateLeafState()\n    if (this.parent && (this.level === 1 || this.parent.expanded === true))\n      this.canFocus = true\n  }\n\n  setData(data: TreeNodeData): void {\n    if (!isArray(data)) {\n      markNodeData(this, data)\n    }\n\n    this.data = data\n    this.childNodes = []\n\n    let children\n    if (this.level === 0 && isArray(this.data)) {\n      children = this.data\n    } else {\n      children = getPropertyFromData(this, 'children') || []\n    }\n\n    for (let i = 0, j = children.length; i < j; i++) {\n      this.insertChild({ data: children[i] })\n    }\n  }\n\n  get label(): string {\n    return getPropertyFromData(this, 'label')\n  }\n\n  get key(): TreeKey {\n    const nodeKey = this.store.key\n    if (this.data) return this.data[nodeKey]\n    return null\n  }\n\n  get disabled(): boolean {\n    return getPropertyFromData(this, 'disabled')\n  }\n\n  get nextSibling(): Nullable<Node> {\n    const parent = this.parent\n    if (parent) {\n      const index = parent.childNodes.indexOf(this)\n      if (index > -1) {\n        return parent.childNodes[index + 1]\n      }\n    }\n    return null\n  }\n\n  get previousSibling(): Nullable<Node> {\n    const parent = this.parent\n    if (parent) {\n      const index = parent.childNodes.indexOf(this)\n      if (index > -1) {\n        return index > 0 ? parent.childNodes[index - 1] : null\n      }\n    }\n    return null\n  }\n\n  contains(target: Node, deep = true): boolean {\n    return (this.childNodes || []).some(\n      (child) => child === target || (deep && child.contains(target))\n    )\n  }\n\n  remove(): void {\n    const parent = this.parent\n    if (parent) {\n      parent.removeChild(this)\n    }\n  }\n\n  insertChild(child?: FakeNode | Node, index?: number, batch?: boolean): void {\n    if (!child) throw new Error('InsertChild error: child is required.')\n\n    if (!(child instanceof Node)) {\n      if (!batch) {\n        const children = this.getChildren(true)\n        if (!children.includes(child.data)) {\n          if (isUndefined(index) || index < 0) {\n            children.push(child.data)\n          } else {\n            children.splice(index, 0, child.data)\n          }\n        }\n      }\n      Object.assign(child, {\n        parent: this,\n        store: this.store,\n      })\n      child = reactive(new Node(child as TreeNodeOptions))\n      if (child instanceof Node) {\n        child.initialize()\n      }\n    }\n\n    ;(child as Node).level = this.level + 1\n\n    if (isUndefined(index) || index < 0) {\n      this.childNodes.push(child as Node)\n    } else {\n      this.childNodes.splice(index, 0, child as Node)\n    }\n\n    this.updateLeafState()\n  }\n\n  insertBefore(child: FakeNode | Node, ref: Node): void {\n    let index\n    if (ref) {\n      index = this.childNodes.indexOf(ref)\n    }\n    this.insertChild(child, index)\n  }\n\n  insertAfter(child: FakeNode | Node, ref: Node): void {\n    let index\n    if (ref) {\n      index = this.childNodes.indexOf(ref)\n      if (index !== -1) index += 1\n    }\n    this.insertChild(child, index)\n  }\n\n  removeChild(child: Node): void {\n    const children = this.getChildren() || []\n    const dataIndex = children.indexOf(child.data)\n    if (dataIndex > -1) {\n      children.splice(dataIndex, 1)\n    }\n\n    const index = this.childNodes.indexOf(child)\n\n    if (index > -1) {\n      this.store && this.store.deregisterNode(child)\n      child.parent = null\n      this.childNodes.splice(index, 1)\n    }\n\n    this.updateLeafState()\n  }\n\n  removeChildByData(data: TreeNodeData): void {\n    let targetNode: Node = null\n\n    for (let i = 0; i < this.childNodes.length; i++) {\n      if (this.childNodes[i].data === data) {\n        targetNode = this.childNodes[i]\n        break\n      }\n    }\n\n    if (targetNode) {\n      this.removeChild(targetNode)\n    }\n  }\n\n  expand(callback?: () => void, expandParent?: boolean): void {\n    const done = (): void => {\n      if (expandParent) {\n        let parent = this.parent\n        while (parent.level > 0) {\n          parent.expanded = true\n          parent = parent.parent\n        }\n      }\n      this.expanded = true\n      if (callback) callback()\n      this.childNodes.forEach((item) => {\n        item.canFocus = true\n      })\n    }\n\n    if (this.shouldLoadData()) {\n      this.loadData((data) => {\n        if (isArray(data)) {\n          if (this.checked) {\n            this.setChecked(true, true)\n          } else if (!this.store.checkStrictly) {\n            reInitChecked(this)\n          }\n          done()\n        }\n      })\n    } else {\n      done()\n    }\n  }\n\n  doCreateChildren(\n    array: TreeNodeData[],\n    defaultProps: TreeNodeLoadedDefaultProps = {}\n  ): void {\n    array.forEach((item) => {\n      this.insertChild(\n        Object.assign({ data: item }, defaultProps),\n        undefined,\n        true\n      )\n    })\n  }\n\n  collapse(): void {\n    this.expanded = false\n    this.childNodes.forEach((item) => {\n      item.canFocus = false\n    })\n  }\n\n  shouldLoadData(): boolean {\n    return this.store.lazy === true && this.store.load && !this.loaded\n  }\n\n  updateLeafState(): void {\n    if (\n      this.store.lazy === true &&\n      this.loaded !== true &&\n      typeof this.isLeafByUser !== 'undefined'\n    ) {\n      this.isLeaf = this.isLeafByUser\n      return\n    }\n    const childNodes = this.childNodes\n    if (\n      !this.store.lazy ||\n      (this.store.lazy === true && this.loaded === true)\n    ) {\n      this.isLeaf = !childNodes || childNodes.length === 0\n      return\n    }\n    this.isLeaf = false\n  }\n\n  setChecked(\n    value?: boolean | string,\n    deep?: boolean,\n    recursion?: boolean,\n    passValue?: boolean\n  ) {\n    this.indeterminate = value === 'half'\n    this.checked = value === true\n\n    if (this.store.checkStrictly) return\n\n    if (!(this.shouldLoadData() && !this.store.checkDescendants)) {\n      const { all, allWithoutDisable } = getChildState(this.childNodes)\n\n      if (!this.isLeaf && !all && allWithoutDisable) {\n        this.checked = false\n        value = false\n      }\n\n      const handleDescendants = (): void => {\n        if (deep) {\n          const childNodes = this.childNodes\n          for (let i = 0, j = childNodes.length; i < j; i++) {\n            const child = childNodes[i]\n            passValue = passValue || value !== false\n            const isCheck = child.disabled ? child.checked : passValue\n            child.setChecked(isCheck, deep, true, passValue)\n          }\n          const { half, all } = getChildState(childNodes)\n          if (!all) {\n            this.checked = all\n            this.indeterminate = half\n          }\n        }\n      }\n\n      if (this.shouldLoadData()) {\n        // Only work on lazy load data.\n        this.loadData(\n          () => {\n            handleDescendants()\n            reInitChecked(this)\n          },\n          {\n            checked: value !== false,\n          }\n        )\n        return\n      } else {\n        handleDescendants()\n      }\n    }\n\n    const parent = this.parent\n    if (!parent || parent.level === 0) return\n\n    if (!recursion) {\n      reInitChecked(parent)\n    }\n  }\n\n  getChildren(forceInit = false): TreeNodeData | TreeNodeData[] {\n    // this is data\n    if (this.level === 0) return this.data\n    const data = this.data\n    if (!data) return null\n\n    const props = this.store.props\n    let children = 'children'\n    if (props) {\n      children = props.children || 'children'\n    }\n\n    if (isUndefined(data[children])) {\n      data[children] = null\n    }\n\n    if (forceInit && !data[children]) {\n      data[children] = []\n    }\n\n    return data[children]\n  }\n\n  updateChildren(): void {\n    const newData = (this.getChildren() || []) as TreeNodeData[]\n    const oldData = this.childNodes.map((node) => node.data)\n\n    const newDataMap = {}\n    const newNodes = []\n\n    newData.forEach((item, index) => {\n      const key = item[NODE_KEY]\n      const isNodeExists =\n        !!key && oldData.findIndex((data) => data[NODE_KEY] === key) >= 0\n      if (isNodeExists) {\n        newDataMap[key] = { index, data: item }\n      } else {\n        newNodes.push({ index, data: item })\n      }\n    })\n\n    if (!this.store.lazy) {\n      oldData.forEach((item) => {\n        if (!newDataMap[item[NODE_KEY]]) this.removeChildByData(item)\n      })\n    }\n\n    newNodes.forEach(({ index, data }) => {\n      this.insertChild({ data }, index)\n    })\n\n    this.updateLeafState()\n  }\n\n  loadData(\n    callback: (node: Node) => void,\n    defaultProps: TreeNodeLoadedDefaultProps = {}\n  ) {\n    if (\n      this.store.lazy === true &&\n      this.store.load &&\n      !this.loaded &&\n      (!this.loading || Object.keys(defaultProps).length)\n    ) {\n      this.loading = true\n\n      const resolve = (children) => {\n        this.childNodes = []\n\n        this.doCreateChildren(children, defaultProps)\n        this.loaded = true\n        this.loading = false\n\n        this.updateLeafState()\n        if (callback) {\n          callback.call(this, children)\n        }\n      }\n      const reject = () => {\n        this.loading = false\n      }\n\n      this.store.load(this, resolve, reject)\n    } else {\n      if (callback) {\n        callback.call(this)\n      }\n    }\n  }\n\n  eachNode(callback: (node: Node) => void) {\n    const arr: Node[] = [this]\n    while (arr.length) {\n      const node = arr.shift()!\n      arr.unshift(...node.childNodes)\n      callback(node)\n    }\n  }\n\n  reInitChecked() {\n    if (this.store.checkStrictly) return\n    reInitChecked(this)\n  }\n}\n\nexport default Node\n"], "mappings": ";;;;;;;;;AAUY,MAACA,aAAa,GAAIC,IAAI,IAAK;EACrC,IAAIC,GAAG,GAAG,IAAI;EACd,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,iBAAiB,GAAG,IAAI;EAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGL,IAAI,CAACM,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC3C,MAAMG,CAAC,GAAGP,IAAI,CAACI,CAAC,CAAC;IACjB,IAAIG,CAAC,CAACC,OAAO,KAAK,IAAI,IAAID,CAAC,CAACE,aAAa,EAAE;MACzCR,GAAG,GAAG,KAAK;MACX,IAAI,CAACM,CAAC,CAACG,QAAQ,EAAE;QACfP,iBAAiB,GAAG,KAAK;MACjC;IACA;IACI,IAAII,CAAC,CAACC,OAAO,KAAK,KAAK,IAAID,CAAC,CAACE,aAAa,EAAE;MAC1CP,IAAI,GAAG,KAAK;IAClB;EACA;EACE,OAAO;IAAED,GAAG;IAAEC,IAAI;IAAEC,iBAAiB;IAAEQ,IAAI,EAAE,CAACV,GAAG,IAAI,CAACC;EAAI,CAAE;AAC9D;AACA,MAAMU,aAAa,GAAG,SAAAA,CAASZ,IAAI,EAAE;EACnC,IAAIA,IAAI,CAACa,UAAU,CAACP,MAAM,KAAK,CAAC,IAAIN,IAAI,CAACc,OAAO,EAC9C;EACF,MAAM;IAAEb,GAAG;IAAEC,IAAI;IAAES;EAAI,CAAE,GAAGZ,aAAa,CAACC,IAAI,CAACa,UAAU,CAAC;EAC1D,IAAIZ,GAAG,EAAE;IACPD,IAAI,CAACQ,OAAO,GAAG,IAAI;IACnBR,IAAI,CAACS,aAAa,GAAG,KAAK;EAC9B,CAAG,MAAM,IAAIE,IAAI,EAAE;IACfX,IAAI,CAACQ,OAAO,GAAG,KAAK;IACpBR,IAAI,CAACS,aAAa,GAAG,IAAI;EAC7B,CAAG,MAAM,IAAIP,IAAI,EAAE;IACfF,IAAI,CAACQ,OAAO,GAAG,KAAK;IACpBR,IAAI,CAACS,aAAa,GAAG,KAAK;EAC9B;EACE,MAAMM,MAAM,GAAGf,IAAI,CAACe,MAAM;EAC1B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAK,CAAC,EAC/B;EACF,IAAI,CAAChB,IAAI,CAACiB,KAAK,CAACC,aAAa,EAAE;IAC7BN,aAAa,CAACG,MAAM,CAAC;EACzB;AACA,CAAC;AACD,MAAMI,mBAAmB,GAAG,SAAAA,CAASnB,IAAI,EAAEoB,IAAI,EAAE;EAC/C,MAAMC,KAAK,GAAGrB,IAAI,CAACiB,KAAK,CAACI,KAAK;EAC9B,MAAMC,IAAI,GAAGtB,IAAI,CAACsB,IAAI,IAAI,EAAE;EAC5B,MAAMC,MAAM,GAAGF,KAAK,CAACD,IAAI,CAAC;EAC1B,IAAII,UAAU,CAACD,MAAM,CAAC,EAAE;IACtB,OAAOA,MAAM,CAACD,IAAI,EAAEtB,IAAI,CAAC;EAC7B,CAAG,MAAM,IAAIyB,QAAQ,CAACF,MAAM,CAAC,EAAE;IAC3B,OAAOD,IAAI,CAACC,MAAM,CAAC;EACvB,CAAG,MAAM,IAAIG,WAAW,CAACH,MAAM,CAAC,EAAE;IAC9B,MAAMI,QAAQ,GAAGL,IAAI,CAACF,IAAI,CAAC;IAC3B,OAAOM,WAAW,CAACC,QAAQ,CAAC,GAAG,EAAE,GAAGA,QAAQ;EAChD;AACA,CAAC;AACD,IAAIC,UAAU,GAAG,CAAC;AAClB,MAAMC,IAAI,CAAC;EACTC,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACC,EAAE,GAAGJ,UAAU,EAAE;IACtB,IAAI,CAACK,IAAI,GAAG,IAAI;IAChB,IAAI,CAACzB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACa,IAAI,GAAG,IAAI;IAChB,IAAI,CAACY,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACnB,MAAM,GAAG,IAAI;IAClB,IAAI,CAACoB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,KAAK,MAAMC,IAAI,IAAIP,OAAO,EAAE;MAC1B,IAAIQ,MAAM,CAACR,OAAO,EAAEO,IAAI,CAAC,EAAE;QACzB,IAAI,CAACA,IAAI,CAAC,GAAGP,OAAO,CAACO,IAAI,CAAC;MAClC;IACA;IACI,IAAI,CAACtB,KAAK,GAAG,CAAC;IACd,IAAI,CAACwB,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC3B,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACC,KAAK,GAAG,IAAI,CAACD,MAAM,CAACC,KAAK,GAAG,CAAC;IACxC;EACA;EACEyB,UAAUA,CAAA,EAAG;IACX,MAAMxB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACA,KAAK,EAAE;MACV,MAAM,IAAIyB,KAAK,CAAC,0BAA0B,CAAC;IACjD;IACIzB,KAAK,CAAC0B,YAAY,CAAC,IAAI,CAAC;IACxB,MAAMtB,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACzB,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACuB,MAAM,KAAK,WAAW,EAAE;MAChD,MAAMA,MAAM,GAAGzB,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC;MAClD,IAAI0B,SAAS,CAACD,MAAM,CAAC,EAAE;QACrB,IAAI,CAACE,YAAY,GAAGF,MAAM;MAClC;IACA;IACI,IAAI3B,KAAK,CAAC8B,IAAI,KAAK,IAAI,IAAI,IAAI,CAACzB,IAAI,EAAE;MACpC,IAAI,CAAC0B,OAAO,CAAC,IAAI,CAAC1B,IAAI,CAAC;MACvB,IAAIL,KAAK,CAACgC,gBAAgB,EAAE;QAC1B,IAAI,CAACf,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACG,QAAQ,GAAG,IAAI;MAC5B;IACA,CAAK,MAAM,IAAI,IAAI,CAACrB,KAAK,GAAG,CAAC,IAAIC,KAAK,CAAC8B,IAAI,IAAI9B,KAAK,CAACgC,gBAAgB,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;MACvF,IAAI,CAACI,MAAM,EAAE;IACnB;IACI,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC7B,IAAI,CAAC,EAAE;MACvB8B,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC9B,IAAI,CAAC;IACnC;IACI,IAAI,CAAC,IAAI,CAACA,IAAI,EACZ;IACF,MAAM+B,mBAAmB,GAAGpC,KAAK,CAACoC,mBAAmB;IACrD,MAAMC,GAAG,GAAGrC,KAAK,CAACqC,GAAG;IACrB,IAAIA,GAAG,IAAID,mBAAmB,IAAIA,mBAAmB,CAACE,QAAQ,CAAC,IAAI,CAACD,GAAG,CAAC,EAAE;MACxE,IAAI,CAACJ,MAAM,CAAC,IAAI,EAAEjC,KAAK,CAACuC,gBAAgB,CAAC;IAC/C;IACI,IAAIF,GAAG,IAAIrC,KAAK,CAACwC,cAAc,KAAK,KAAK,CAAC,IAAI,IAAI,CAACH,GAAG,KAAKrC,KAAK,CAACwC,cAAc,EAAE;MAC/ExC,KAAK,CAACyC,WAAW,GAAG,IAAI;MACxBzC,KAAK,CAACyC,WAAW,CAACtB,SAAS,GAAG,IAAI;IACxC;IACI,IAAInB,KAAK,CAAC8B,IAAI,EAAE;MACd9B,KAAK,CAAC0C,uBAAuB,CAAC,IAAI,CAAC;IACzC;IACI,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,IAAI,CAAC7C,MAAM,KAAK,IAAI,CAACC,KAAK,KAAK,CAAC,IAAI,IAAI,CAACD,MAAM,CAACmB,QAAQ,KAAK,IAAI,CAAC,EACpE,IAAI,CAACG,QAAQ,GAAG,IAAI;EAC1B;EACEW,OAAOA,CAAC1B,IAAI,EAAE;IACZ,IAAI,CAAC6B,OAAO,CAAC7B,IAAI,CAAC,EAAE;MAClB8B,YAAY,CAAC,IAAI,EAAE9B,IAAI,CAAC;IAC9B;IACI,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACT,UAAU,GAAG,EAAE;IACpB,IAAIgD,QAAQ;IACZ,IAAI,IAAI,CAAC7C,KAAK,KAAK,CAAC,IAAImC,OAAO,CAAC,IAAI,CAAC7B,IAAI,CAAC,EAAE;MAC1CuC,QAAQ,GAAG,IAAI,CAACvC,IAAI;IAC1B,CAAK,MAAM;MACLuC,QAAQ,GAAG1C,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;IAC5D;IACI,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGwD,QAAQ,CAACvD,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC/C,IAAI,CAAC0D,WAAW,CAAC;QAAExC,IAAI,EAAEuC,QAAQ,CAACzD,CAAC;MAAC,CAAE,CAAC;IAC7C;EACA;EACE,IAAI2D,KAAKA,CAAA,EAAG;IACV,OAAO5C,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC;EAC7C;EACE,IAAImC,GAAGA,CAAA,EAAG;IACR,MAAMU,OAAO,GAAG,IAAI,CAAC/C,KAAK,CAACqC,GAAG;IAC9B,IAAI,IAAI,CAAChC,IAAI,EACX,OAAO,IAAI,CAACA,IAAI,CAAC0C,OAAO,CAAC;IAC3B,OAAO,IAAI;EACf;EACE,IAAItD,QAAQA,CAAA,EAAG;IACb,OAAOS,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC;EAChD;EACE,IAAI8C,WAAWA,CAAA,EAAG;IAChB,MAAMlD,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACV,MAAMmD,KAAK,GAAGnD,MAAM,CAACF,UAAU,CAACsD,OAAO,CAAC,IAAI,CAAC;MAC7C,IAAID,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,OAAOnD,MAAM,CAACF,UAAU,CAACqD,KAAK,GAAG,CAAC,CAAC;MAC3C;IACA;IACI,OAAO,IAAI;EACf;EACE,IAAIE,eAAeA,CAAA,EAAG;IACpB,MAAMrD,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACV,MAAMmD,KAAK,GAAGnD,MAAM,CAACF,UAAU,CAACsD,OAAO,CAAC,IAAI,CAAC;MAC7C,IAAID,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,OAAOA,KAAK,GAAG,CAAC,GAAGnD,MAAM,CAACF,UAAU,CAACqD,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;MAC9D;IACA;IACI,OAAO,IAAI;EACf;EACEG,QAAQA,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAI,EAAE;IAC5B,OAAO,CAAC,IAAI,CAAC1D,UAAU,IAAI,EAAE,EAAE2D,IAAI,CAAEC,KAAK,IAAKA,KAAK,KAAKH,MAAM,IAAIC,IAAI,IAAIE,KAAK,CAACJ,QAAQ,CAACC,MAAM,CAAC,CAAC;EACtG;EACEI,MAAMA,CAAA,EAAG;IACP,MAAM3D,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACVA,MAAM,CAAC4D,WAAW,CAAC,IAAI,CAAC;IAC9B;EACA;EACEb,WAAWA,CAACW,KAAK,EAAEP,KAAK,EAAEU,KAAK,EAAE;IAC/B,IAAI,CAACH,KAAK,EACR,MAAM,IAAI/B,KAAK,CAAC,uCAAuC,CAAC;IAC1D,IAAI,EAAE+B,KAAK,YAAY5C,IAAI,CAAC,EAAE;MAC5B,IAAI,CAAC+C,KAAK,EAAE;QACV,MAAMf,QAAQ,GAAG,IAAI,CAACgB,WAAW,CAAC,IAAI,CAAC;QACvC,IAAI,CAAChB,QAAQ,CAACN,QAAQ,CAACkB,KAAK,CAACnD,IAAI,CAAC,EAAE;UAClC,IAAII,WAAW,CAACwC,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;YACnCL,QAAQ,CAACiB,IAAI,CAACL,KAAK,CAACnD,IAAI,CAAC;UACrC,CAAW,MAAM;YACLuC,QAAQ,CAACkB,MAAM,CAACb,KAAK,EAAE,CAAC,EAAEO,KAAK,CAACnD,IAAI,CAAC;UACjD;QACA;MACA;MACM0D,MAAM,CAACC,MAAM,CAACR,KAAK,EAAE;QACnB1D,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE,IAAI,CAACA;MACpB,CAAO,CAAC;MACFwD,KAAK,GAAGS,QAAQ,CAAC,IAAIrD,IAAI,CAAC4C,KAAK,CAAC,CAAC;MACjC,IAAIA,KAAK,YAAY5C,IAAI,EAAE;QACzB4C,KAAK,CAAChC,UAAU,EAAE;MAC1B;IACA;IAEIgC,KAAK,CAACzD,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC;IAC5B,IAAIU,WAAW,CAACwC,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACnC,IAAI,CAACrD,UAAU,CAACiE,IAAI,CAACL,KAAK,CAAC;IACjC,CAAK,MAAM;MACL,IAAI,CAAC5D,UAAU,CAACkE,MAAM,CAACb,KAAK,EAAE,CAAC,EAAEO,KAAK,CAAC;IAC7C;IACI,IAAI,CAACb,eAAe,EAAE;EAC1B;EACEuB,YAAYA,CAACV,KAAK,EAAEW,GAAG,EAAE;IACvB,IAAIlB,KAAK;IACT,IAAIkB,GAAG,EAAE;MACPlB,KAAK,GAAG,IAAI,CAACrD,UAAU,CAACsD,OAAO,CAACiB,GAAG,CAAC;IAC1C;IACI,IAAI,CAACtB,WAAW,CAACW,KAAK,EAAEP,KAAK,CAAC;EAClC;EACEmB,WAAWA,CAACZ,KAAK,EAAEW,GAAG,EAAE;IACtB,IAAIlB,KAAK;IACT,IAAIkB,GAAG,EAAE;MACPlB,KAAK,GAAG,IAAI,CAACrD,UAAU,CAACsD,OAAO,CAACiB,GAAG,CAAC;MACpC,IAAIlB,KAAK,KAAK,CAAC,CAAC,EACdA,KAAK,IAAI,CAAC;IAClB;IACI,IAAI,CAACJ,WAAW,CAACW,KAAK,EAAEP,KAAK,CAAC;EAClC;EACES,WAAWA,CAACF,KAAK,EAAE;IACjB,MAAMZ,QAAQ,GAAG,IAAI,CAACgB,WAAW,EAAE,IAAI,EAAE;IACzC,MAAMS,SAAS,GAAGzB,QAAQ,CAACM,OAAO,CAACM,KAAK,CAACnD,IAAI,CAAC;IAC9C,IAAIgE,SAAS,GAAG,CAAC,CAAC,EAAE;MAClBzB,QAAQ,CAACkB,MAAM,CAACO,SAAS,EAAE,CAAC,CAAC;IACnC;IACI,MAAMpB,KAAK,GAAG,IAAI,CAACrD,UAAU,CAACsD,OAAO,CAACM,KAAK,CAAC;IAC5C,IAAIP,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjD,KAAK,IAAI,IAAI,CAACA,KAAK,CAACsE,cAAc,CAACd,KAAK,CAAC;MAC9CA,KAAK,CAAC1D,MAAM,GAAG,IAAI;MACnB,IAAI,CAACF,UAAU,CAACkE,MAAM,CAACb,KAAK,EAAE,CAAC,CAAC;IACtC;IACI,IAAI,CAACN,eAAe,EAAE;EAC1B;EACE4B,iBAAiBA,CAAClE,IAAI,EAAE;IACtB,IAAImE,UAAU,GAAG,IAAI;IACrB,KAAK,IAAIrF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACS,UAAU,CAACP,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/C,IAAI,IAAI,CAACS,UAAU,CAACT,CAAC,CAAC,CAACkB,IAAI,KAAKA,IAAI,EAAE;QACpCmE,UAAU,GAAG,IAAI,CAAC5E,UAAU,CAACT,CAAC,CAAC;QAC/B;MACR;IACA;IACI,IAAIqF,UAAU,EAAE;MACd,IAAI,CAACd,WAAW,CAACc,UAAU,CAAC;IAClC;EACA;EACEvC,MAAMA,CAACwC,QAAQ,EAAEC,YAAY,EAAE;IAC7B,MAAMC,IAAI,GAAGA,CAAA,KAAM;MACjB,IAAID,YAAY,EAAE;QAChB,IAAI5E,MAAM,GAAG,IAAI,CAACA,MAAM;QACxB,OAAOA,MAAM,CAACC,KAAK,GAAG,CAAC,EAAE;UACvBD,MAAM,CAACmB,QAAQ,GAAG,IAAI;UACtBnB,MAAM,GAAGA,MAAM,CAACA,MAAM;QAChC;MACA;MACM,IAAI,CAACmB,QAAQ,GAAG,IAAI;MACpB,IAAIwD,QAAQ,EACVA,QAAQ,EAAE;MACZ,IAAI,CAAC7E,UAAU,CAACgF,OAAO,CAAEC,IAAI,IAAK;QAChCA,IAAI,CAACzD,QAAQ,GAAG,IAAI;MAC5B,CAAO,CAAC;IACR,CAAK;IACD,IAAI,IAAI,CAAC0D,cAAc,EAAE,EAAE;MACzB,IAAI,CAACC,QAAQ,CAAE1E,IAAI,IAAK;QACtB,IAAI6B,OAAO,CAAC7B,IAAI,CAAC,EAAE;UACjB,IAAI,IAAI,CAACd,OAAO,EAAE;YAChB,IAAI,CAACyF,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;UACvC,CAAW,MAAM,IAAI,CAAC,IAAI,CAAChF,KAAK,CAACC,aAAa,EAAE;YACpCN,aAAa,CAAC,IAAI,CAAC;UAC/B;UACUgF,IAAI,EAAE;QAChB;MACA,CAAO,CAAC;IACR,CAAK,MAAM;MACLA,IAAI,EAAE;IACZ;EACA;EACEM,gBAAgBA,CAACC,KAAK,EAAEC,YAAY,GAAG,EAAE,EAAE;IACzCD,KAAK,CAACN,OAAO,CAAEC,IAAI,IAAK;MACtB,IAAI,CAAChC,WAAW,CAACkB,MAAM,CAACC,MAAM,CAAC;QAAE3D,IAAI,EAAEwE;MAAI,CAAE,EAAEM,YAAY,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;IACjF,CAAK,CAAC;EACN;EACEC,QAAQA,CAAA,EAAG;IACT,IAAI,CAACnE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACrB,UAAU,CAACgF,OAAO,CAAEC,IAAI,IAAK;MAChCA,IAAI,CAACzD,QAAQ,GAAG,KAAK;IAC3B,CAAK,CAAC;EACN;EACE0D,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC9E,KAAK,CAAC8B,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC9B,KAAK,CAACqF,IAAI,IAAI,CAAC,IAAI,CAAC9D,MAAM;EACtE;EACEoB,eAAeA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC3C,KAAK,CAAC8B,IAAI,KAAK,IAAI,IAAI,IAAI,CAACP,MAAM,KAAK,IAAI,IAAI,OAAO,IAAI,CAACM,YAAY,KAAK,WAAW,EAAE;MAChG,IAAI,CAACF,MAAM,GAAG,IAAI,CAACE,YAAY;MAC/B;IACN;IACI,MAAMjC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC,IAAI,CAACI,KAAK,CAAC8B,IAAI,IAAI,IAAI,CAAC9B,KAAK,CAAC8B,IAAI,KAAK,IAAI,IAAI,IAAI,CAACP,MAAM,KAAK,IAAI,EAAE;MACxE,IAAI,CAACI,MAAM,GAAG,CAAC/B,UAAU,IAAIA,UAAU,CAACP,MAAM,KAAK,CAAC;MACpD;IACN;IACI,IAAI,CAACsC,MAAM,GAAG,KAAK;EACvB;EACEqD,UAAUA,CAACM,KAAK,EAAEhC,IAAI,EAAEiC,SAAS,EAAEC,SAAS,EAAE;IAC5C,IAAI,CAAChG,aAAa,GAAG8F,KAAK,KAAK,MAAM;IACrC,IAAI,CAAC/F,OAAO,GAAG+F,KAAK,KAAK,IAAI;IAC7B,IAAI,IAAI,CAACtF,KAAK,CAACC,aAAa,EAC1B;IACF,IAAI,EAAE,IAAI,CAAC6E,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC9E,KAAK,CAACyF,gBAAgB,CAAC,EAAE;MAC5D,MAAM;QAAEzG,GAAG;QAAEE;MAAiB,CAAE,GAAGJ,aAAa,CAAC,IAAI,CAACc,UAAU,CAAC;MACjE,IAAI,CAAC,IAAI,CAAC+B,MAAM,IAAI,CAAC3C,GAAG,IAAIE,iBAAiB,EAAE;QAC7C,IAAI,CAACK,OAAO,GAAG,KAAK;QACpB+F,KAAK,GAAG,KAAK;MACrB;MACM,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;QAC9B,IAAIpC,IAAI,EAAE;UACR,MAAM1D,UAAU,GAAG,IAAI,CAACA,UAAU;UAClC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGQ,UAAU,CAACP,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;YACjD,MAAMqE,KAAK,GAAG5D,UAAU,CAACT,CAAC,CAAC;YAC3BqG,SAAS,GAAGA,SAAS,IAAIF,KAAK,KAAK,KAAK;YACxC,MAAMK,OAAO,GAAGnC,KAAK,CAAC/D,QAAQ,GAAG+D,KAAK,CAACjE,OAAO,GAAGiG,SAAS;YAC1DhC,KAAK,CAACwB,UAAU,CAACW,OAAO,EAAErC,IAAI,EAAE,IAAI,EAAEkC,SAAS,CAAC;UAC5D;UACU,MAAM;YAAE9F,IAAI;YAAEV,GAAG,EAAE4G;UAAI,CAAE,GAAG9G,aAAa,CAACc,UAAU,CAAC;UACrD,IAAI,CAACgG,IAAI,EAAE;YACT,IAAI,CAACrG,OAAO,GAAGqG,IAAI;YACnB,IAAI,CAACpG,aAAa,GAAGE,IAAI;UACrC;QACA;MACA,CAAO;MACD,IAAI,IAAI,CAACoF,cAAc,EAAE,EAAE;QACzB,IAAI,CAACC,QAAQ,CAAC,MAAM;UAClBW,iBAAiB,EAAE;UACnB/F,aAAa,CAAC,IAAI,CAAC;QAC7B,CAAS,EAAE;UACDJ,OAAO,EAAE+F,KAAK,KAAK;QAC7B,CAAS,CAAC;QACF;MACR,CAAO,MAAM;QACLI,iBAAiB,EAAE;MAC3B;IACA;IACI,MAAM5F,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAK,CAAC,EAC/B;IACF,IAAI,CAACwF,SAAS,EAAE;MACd5F,aAAa,CAACG,MAAM,CAAC;IAC3B;EACA;EACE8D,WAAWA,CAACiC,SAAS,GAAG,KAAK,EAAE;IAC7B,IAAI,IAAI,CAAC9F,KAAK,KAAK,CAAC,EAClB,OAAO,IAAI,CAACM,IAAI;IAClB,MAAMA,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,EACP,OAAO,IAAI;IACb,MAAMD,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACI,KAAK;IAC9B,IAAIwC,QAAQ,GAAG,UAAU;IACzB,IAAIxC,KAAK,EAAE;MACTwC,QAAQ,GAAGxC,KAAK,CAACwC,QAAQ,IAAI,UAAU;IAC7C;IACI,IAAInC,WAAW,CAACJ,IAAI,CAACuC,QAAQ,CAAC,CAAC,EAAE;MAC/BvC,IAAI,CAACuC,QAAQ,CAAC,GAAG,IAAI;IAC3B;IACI,IAAIiD,SAAS,IAAI,CAACxF,IAAI,CAACuC,QAAQ,CAAC,EAAE;MAChCvC,IAAI,CAACuC,QAAQ,CAAC,GAAG,EAAE;IACzB;IACI,OAAOvC,IAAI,CAACuC,QAAQ,CAAC;EACzB;EACEkD,cAAcA,CAAA,EAAG;IACf,MAAMC,OAAO,GAAG,IAAI,CAACnC,WAAW,EAAE,IAAI,EAAE;IACxC,MAAMoC,OAAO,GAAG,IAAI,CAACpG,UAAU,CAACqG,GAAG,CAAElH,IAAI,IAAKA,IAAI,CAACsB,IAAI,CAAC;IACxD,MAAM6F,UAAU,GAAG,EAAE;IACrB,MAAMC,QAAQ,GAAG,EAAE;IACnBJ,OAAO,CAACnB,OAAO,CAAC,CAACC,IAAI,EAAE5B,KAAK,KAAK;MAC/B,MAAMZ,GAAG,GAAGwC,IAAI,CAACuB,QAAQ,CAAC;MAC1B,MAAMC,YAAY,GAAG,CAAC,CAAChE,GAAG,IAAI2D,OAAO,CAACM,SAAS,CAAEjG,IAAI,IAAKA,IAAI,CAAC+F,QAAQ,CAAC,KAAK/D,GAAG,CAAC,IAAI,CAAC;MACtF,IAAIgE,YAAY,EAAE;QAChBH,UAAU,CAAC7D,GAAG,CAAC,GAAG;UAAEY,KAAK;UAAE5C,IAAI,EAAEwE;QAAI,CAAE;MAC/C,CAAO,MAAM;QACLsB,QAAQ,CAACtC,IAAI,CAAC;UAAEZ,KAAK;UAAE5C,IAAI,EAAEwE;QAAI,CAAE,CAAC;MAC5C;IACA,CAAK,CAAC;IACF,IAAI,CAAC,IAAI,CAAC7E,KAAK,CAAC8B,IAAI,EAAE;MACpBkE,OAAO,CAACpB,OAAO,CAAEC,IAAI,IAAK;QACxB,IAAI,CAACqB,UAAU,CAACrB,IAAI,CAACuB,QAAQ,CAAC,CAAC,EAC7B,IAAI,CAAC7B,iBAAiB,CAACM,IAAI,CAAC;MACtC,CAAO,CAAC;IACR;IACIsB,QAAQ,CAACvB,OAAO,CAAC,CAAC;MAAE3B,KAAK;MAAE5C;IAAI,CAAE,KAAK;MACpC,IAAI,CAACwC,WAAW,CAAC;QAAExC;MAAI,CAAE,EAAE4C,KAAK,CAAC;IACvC,CAAK,CAAC;IACF,IAAI,CAACN,eAAe,EAAE;EAC1B;EACEoC,QAAQA,CAACN,QAAQ,EAAEU,YAAY,GAAG,EAAE,EAAE;IACpC,IAAI,IAAI,CAACnF,KAAK,CAAC8B,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC9B,KAAK,CAACqF,IAAI,IAAI,CAAC,IAAI,CAAC9D,MAAM,KAAK,CAAC,IAAI,CAAC1B,OAAO,IAAIkE,MAAM,CAACwC,IAAI,CAACpB,YAAY,CAAC,CAAC9F,MAAM,CAAC,EAAE;MACtH,IAAI,CAACQ,OAAO,GAAG,IAAI;MACnB,MAAM2G,OAAO,GAAI5D,QAAQ,IAAK;QAC5B,IAAI,CAAChD,UAAU,GAAG,EAAE;QACpB,IAAI,CAACqF,gBAAgB,CAACrC,QAAQ,EAAEuC,YAAY,CAAC;QAC7C,IAAI,CAAC5D,MAAM,GAAG,IAAI;QAClB,IAAI,CAAC1B,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC8C,eAAe,EAAE;QACtB,IAAI8B,QAAQ,EAAE;UACZA,QAAQ,CAACgC,IAAI,CAAC,IAAI,EAAE7D,QAAQ,CAAC;QACvC;MACA,CAAO;MACD,MAAM8D,MAAM,GAAGA,CAAA,KAAM;QACnB,IAAI,CAAC7G,OAAO,GAAG,KAAK;MAC5B,CAAO;MACD,IAAI,CAACG,KAAK,CAACqF,IAAI,CAAC,IAAI,EAAEmB,OAAO,EAAEE,MAAM,CAAC;IAC5C,CAAK,MAAM;MACL,IAAIjC,QAAQ,EAAE;QACZA,QAAQ,CAACgC,IAAI,CAAC,IAAI,CAAC;MAC3B;IACA;EACA;EACEE,QAAQA,CAAClC,QAAQ,EAAE;IACjB,MAAMmC,GAAG,GAAG,CAAC,IAAI,CAAC;IAClB,OAAOA,GAAG,CAACvH,MAAM,EAAE;MACjB,MAAMN,IAAI,GAAG6H,GAAG,CAACC,KAAK,EAAE;MACxBD,GAAG,CAACE,OAAO,CAAC,GAAG/H,IAAI,CAACa,UAAU,CAAC;MAC/B6E,QAAQ,CAAC1F,IAAI,CAAC;IACpB;EACA;EACEY,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACK,KAAK,CAACC,aAAa,EAC1B;IACFN,aAAa,CAAC,IAAI,CAAC;EACvB;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}