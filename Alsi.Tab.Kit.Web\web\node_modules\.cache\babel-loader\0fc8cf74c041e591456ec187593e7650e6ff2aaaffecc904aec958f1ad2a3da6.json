{"ast": null, "code": "import { defineComponent, inject, ref, computed, onBeforeUnmount, toRef, openBlock, createBlock, Transition, unref, withCtx, withDirectives, createElementVNode, normalizeClass, withModifiers, normalizeStyle, vShow } from 'vue';\nimport { useEventListener, isClient } from '@vueuse/core';\nimport { scrollbarContextKey } from './constants.mjs';\nimport { BAR_MAP, renderThumbStyle } from './util.mjs';\nimport { thumbProps } from './thumb.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { throwError } from '../../../utils/error.mjs';\nconst COMPONENT_NAME = \"Thumb\";\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"thumb\",\n  props: thumbProps,\n  setup(__props) {\n    const props = __props;\n    const scrollbar = inject(scrollbarContextKey);\n    const ns = useNamespace(\"scrollbar\");\n    if (!scrollbar) throwError(COMPONENT_NAME, \"can not inject scrollbar context\");\n    const instance = ref();\n    const thumb = ref();\n    const thumbState = ref({});\n    const visible = ref(false);\n    let cursorDown = false;\n    let cursorLeave = false;\n    let baseScrollHeight = 0;\n    let baseScrollWidth = 0;\n    let originalOnSelectStart = isClient ? document.onselectstart : null;\n    const bar = computed(() => BAR_MAP[props.vertical ? \"vertical\" : \"horizontal\"]);\n    const thumbStyle = computed(() => renderThumbStyle({\n      size: props.size,\n      move: props.move,\n      bar: bar.value\n    }));\n    const offsetRatio = computed(() => instance.value[bar.value.offset] ** 2 / scrollbar.wrapElement[bar.value.scrollSize] / props.ratio / thumb.value[bar.value.offset]);\n    const clickThumbHandler = e => {\n      var _a;\n      e.stopPropagation();\n      if (e.ctrlKey || [1, 2].includes(e.button)) return;\n      (_a = window.getSelection()) == null ? void 0 : _a.removeAllRanges();\n      startDrag(e);\n      const el = e.currentTarget;\n      if (!el) return;\n      thumbState.value[bar.value.axis] = el[bar.value.offset] - (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction]);\n    };\n    const clickTrackHandler = e => {\n      if (!thumb.value || !instance.value || !scrollbar.wrapElement) return;\n      const offset = Math.abs(e.target.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]);\n      const thumbHalf = thumb.value[bar.value.offset] / 2;\n      const thumbPositionPercentage = (offset - thumbHalf) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize] / 100;\n    };\n    const startDrag = e => {\n      e.stopImmediatePropagation();\n      cursorDown = true;\n      baseScrollHeight = scrollbar.wrapElement.scrollHeight;\n      baseScrollWidth = scrollbar.wrapElement.scrollWidth;\n      document.addEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.addEventListener(\"mouseup\", mouseUpDocumentHandler);\n      originalOnSelectStart = document.onselectstart;\n      document.onselectstart = () => false;\n    };\n    const mouseMoveDocumentHandler = e => {\n      if (!instance.value || !thumb.value) return;\n      if (cursorDown === false) return;\n      const prevPage = thumbState.value[bar.value.axis];\n      if (!prevPage) return;\n      const offset = (instance.value.getBoundingClientRect()[bar.value.direction] - e[bar.value.client]) * -1;\n      const thumbClickPosition = thumb.value[bar.value.offset] - prevPage;\n      const thumbPositionPercentage = (offset - thumbClickPosition) * 100 * offsetRatio.value / instance.value[bar.value.offset];\n      if (bar.value.scroll === \"scrollLeft\") {\n        scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * baseScrollWidth / 100;\n      } else {\n        scrollbar.wrapElement[bar.value.scroll] = thumbPositionPercentage * baseScrollHeight / 100;\n      }\n    };\n    const mouseUpDocumentHandler = () => {\n      cursorDown = false;\n      thumbState.value[bar.value.axis] = 0;\n      document.removeEventListener(\"mousemove\", mouseMoveDocumentHandler);\n      document.removeEventListener(\"mouseup\", mouseUpDocumentHandler);\n      restoreOnselectstart();\n      if (cursorLeave) visible.value = false;\n    };\n    const mouseMoveScrollbarHandler = () => {\n      cursorLeave = false;\n      visible.value = !!props.size;\n    };\n    const mouseLeaveScrollbarHandler = () => {\n      cursorLeave = true;\n      visible.value = cursorDown;\n    };\n    onBeforeUnmount(() => {\n      restoreOnselectstart();\n      document.removeEventListener(\"mouseup\", mouseUpDocumentHandler);\n    });\n    const restoreOnselectstart = () => {\n      if (document.onselectstart !== originalOnSelectStart) document.onselectstart = originalOnSelectStart;\n    };\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mousemove\", mouseMoveScrollbarHandler);\n    useEventListener(toRef(scrollbar, \"scrollbarElement\"), \"mouseleave\", mouseLeaveScrollbarHandler);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(Transition, {\n        name: unref(ns).b(\"fade\"),\n        persisted: \"\"\n      }, {\n        default: withCtx(() => [withDirectives(createElementVNode(\"div\", {\n          ref_key: \"instance\",\n          ref: instance,\n          class: normalizeClass([unref(ns).e(\"bar\"), unref(ns).is(unref(bar).key)]),\n          onMousedown: clickTrackHandler,\n          onClick: withModifiers(() => {}, [\"stop\"])\n        }, [createElementVNode(\"div\", {\n          ref_key: \"thumb\",\n          ref: thumb,\n          class: normalizeClass(unref(ns).e(\"thumb\")),\n          style: normalizeStyle(unref(thumbStyle)),\n          onMousedown: clickThumbHandler\n        }, null, 38)], 42, [\"onClick\"]), [[vShow, _ctx.always || visible.value]])]),\n        _: 1\n      }, 8, [\"name\"]);\n    };\n  }\n});\nvar Thumb = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"thumb.vue\"]]);\nexport { Thumb as default };", "map": {"version": 3, "names": ["scrollbar", "inject", "scrollbarContextKey", "ns", "useNamespace", "throwError", "COMPONENT_NAME", "instance", "ref", "thumb", "thumbState", "visible", "cursorDown", "cursorLeave", "baseScrollHeight", "baseScrollWidth", "originalOnSelectStart", "isClient", "document", "onselectstart", "bar", "computed", "BAR_MAP", "props", "vertical", "thumbStyle", "renderThumbStyle", "size", "move", "value", "offsetRatio", "offset", "wrapElement", "scrollSize", "ratio", "clickThumbHandler", "e", "_a", "stopPropagation", "ctrl<PERSON>ey", "includes", "button", "window", "getSelection", "removeAllRanges", "startDrag", "el", "currentTarget", "axis", "client", "getBoundingClientRect", "direction", "clickTrackHandler", "Math", "abs", "target", "<PERSON><PERSON><PERSON>f", "thumbPositionPercentage", "scroll", "stopImmediatePropagation", "scrollHeight", "scrollWidth", "addEventListener", "mouseMoveDocumentHandler", "mouseUpDocumentHandler", "prevPage", "thumbClickPosition", "removeEventListener", "restoreOnselectstart", "mouseMoveScrollbarHandler", "mouseLeaveScrollbarHandler", "onBeforeUnmount", "useEventListener", "toRef", "_ctx", "_cache", "openBlock", "createBlock", "Transition", "name", "unref", "b", "persisted", "default", "withCtx", "withDirectives", "createElementVNode", "ref_key", "class", "normalizeClass", "is", "key"], "sources": ["../../../../../../packages/components/scrollbar/src/thumb.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"always || visible\"\n      ref=\"instance\"\n      :class=\"[ns.e('bar'), ns.is(bar.key)]\"\n      @mousedown=\"clickTrackHandler\"\n      @click.stop\n    >\n      <div\n        ref=\"thumb\"\n        :class=\"ns.e('thumb')\"\n        :style=\"thumbStyle\"\n        @mousedown=\"clickThumbHandler\"\n      />\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, toRef } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isClient, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { scrollbarContextKey } from './constants'\nimport { BAR_MAP, renderThumbStyle } from './util'\nimport { thumbProps } from './thumb'\n\nconst COMPONENT_NAME = 'Thumb'\nconst props = defineProps(thumbProps)\n\nconst scrollbar = inject(scrollbarContextKey)\nconst ns = useNamespace('scrollbar')\n\nif (!scrollbar) throwError(COMPONENT_NAME, 'can not inject scrollbar context')\n\nconst instance = ref<HTMLDivElement>()\nconst thumb = ref<HTMLDivElement>()\n\nconst thumbState = ref<Partial<Record<'X' | 'Y', number>>>({})\nconst visible = ref(false)\n\nlet cursorDown = false\nlet cursorLeave = false\nlet baseScrollHeight = 0\nlet baseScrollWidth = 0\nlet originalOnSelectStart:\n  | ((this: GlobalEventHandlers, ev: Event) => any)\n  | null = isClient ? document.onselectstart : null\n\nconst bar = computed(() => BAR_MAP[props.vertical ? 'vertical' : 'horizontal'])\n\nconst thumbStyle = computed(() =>\n  renderThumbStyle({\n    size: props.size,\n    move: props.move,\n    bar: bar.value,\n  })\n)\n\nconst offsetRatio = computed(\n  () =>\n    // offsetRatioX = original width of thumb / current width of thumb / ratioX\n    // offsetRatioY = original height of thumb / current height of thumb / ratioY\n    // instance height = wrap height - GAP\n    instance.value![bar.value.offset] ** 2 /\n    scrollbar.wrapElement![bar.value.scrollSize] /\n    props.ratio /\n    thumb.value![bar.value.offset]\n)\n\nconst clickThumbHandler = (e: MouseEvent) => {\n  // prevent click event of middle and right button\n  e.stopPropagation()\n  if (e.ctrlKey || [1, 2].includes(e.button)) return\n\n  window.getSelection()?.removeAllRanges()\n  startDrag(e)\n\n  const el = e.currentTarget as HTMLDivElement\n  if (!el) return\n  thumbState.value[bar.value.axis] =\n    el[bar.value.offset] -\n    (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction])\n}\n\nconst clickTrackHandler = (e: MouseEvent) => {\n  if (!thumb.value || !instance.value || !scrollbar.wrapElement) return\n\n  const offset = Math.abs(\n    (e.target as HTMLElement).getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]\n  )\n  const thumbHalf = thumb.value[bar.value.offset] / 2\n  const thumbPositionPercentage =\n    ((offset - thumbHalf) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst startDrag = (e: MouseEvent) => {\n  e.stopImmediatePropagation()\n  cursorDown = true\n  baseScrollHeight = scrollbar.wrapElement.scrollHeight\n  baseScrollWidth = scrollbar.wrapElement.scrollWidth\n  document.addEventListener('mousemove', mouseMoveDocumentHandler)\n  document.addEventListener('mouseup', mouseUpDocumentHandler)\n  originalOnSelectStart = document.onselectstart\n  document.onselectstart = () => false\n}\n\nconst mouseMoveDocumentHandler = (e: MouseEvent) => {\n  if (!instance.value || !thumb.value) return\n  if (cursorDown === false) return\n\n  const prevPage = thumbState.value[bar.value.axis]\n  if (!prevPage) return\n\n  const offset =\n    (instance.value.getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]) *\n    -1\n  const thumbClickPosition = thumb.value[bar.value.offset] - prevPage\n  const thumbPositionPercentage =\n    ((offset - thumbClickPosition) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n\n  if (bar.value.scroll === 'scrollLeft') {\n    scrollbar.wrapElement[bar.value.scroll] =\n      (thumbPositionPercentage * baseScrollWidth) / 100\n  } else {\n    scrollbar.wrapElement[bar.value.scroll] =\n      (thumbPositionPercentage * baseScrollHeight) / 100\n  }\n}\n\nconst mouseUpDocumentHandler = () => {\n  cursorDown = false\n  thumbState.value[bar.value.axis] = 0\n  document.removeEventListener('mousemove', mouseMoveDocumentHandler)\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n  restoreOnselectstart()\n  if (cursorLeave) visible.value = false\n}\n\nconst mouseMoveScrollbarHandler = () => {\n  cursorLeave = false\n  visible.value = !!props.size\n}\n\nconst mouseLeaveScrollbarHandler = () => {\n  cursorLeave = true\n  visible.value = cursorDown\n}\n\nonBeforeUnmount(() => {\n  restoreOnselectstart()\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n})\n\nconst restoreOnselectstart = () => {\n  if (document.onselectstart !== originalOnSelectStart)\n    document.onselectstart = originalOnSelectStart\n}\n\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mousemove',\n  mouseMoveScrollbarHandler\n)\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mouseleave',\n  mouseLeaveScrollbarHandler\n)\n</script>\n"], "mappings": ";;;;;;;;;;;;;;IA+BM,MAAAA,SAAA,GAAYC,MAAA,CAAOC,mBAAmB;IACtC,MAAAC,EAAA,GAAKC,YAAA,CAAa,WAAW;IAEnC,IAAI,CAACJ,SAAA,EAELK,UAAA,CAAAC,cAAqC;IACrC,MAAMC,QAAQ,GAAoBC,GAAA;IAE5B,MAAAC,KAAA,GAAAD,GAAA,EAAa;IACb,MAAAE,UAAU,GAAAF,GAAS;IAEzB,MAAiBG,OAAA,GAAAH,GAAA;IACjB,IAAII,UAAc;IAClB,IAAIC,WAAmB;IACvB,IAAIC,gBAAkB;IAClB,IAAAC,eAAA;IAIE,IAAAC,qBAAqB,GAAAC,QAAA,GAAAC,QAAyB,CAAAC,aAAA;IAEpD,MAAMC,GAAa,GAAAC,QAAA,OAAAC,OAAA,CAAAC,KAAA,CAAAC,QAAA;IAAA,MAAAC,UACA,GAAAJ,QAAA,OAAAK,gBAAA;MAAAC,IAAA,EAAAJ,KACH,CAAAI,IAAA;MAAAC,IAAA,EAAAL,KACA,CAAAK,IAAA;MAAAR,GAAA,EAAAA,GACH,CAAAS;IAAA,EACX,CAAC;IACH,MAAAC,WAAA,GAAAT,QAAA,OAAAd,QAAA,CAAAsB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA,SAAA/B,SAAA,CAAAgC,WAAA,CAAAZ,GAAA,CAAAS,KAAA,CAAAI,UAAA,IAAAV,KAAA,CAAAW,KAAA,GAAAzB,KAAA,CAAAoB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;IAEA,MAAMI,iBAAc,GAAAC,CAAA;MAClB,IAAAC,EAAA;MAAAD,CAAA,CAAAE,eAAA;MAAA,IAAAF,CAAA,CAAAG,OAAA,WAAAC,QAAA,CAAAJ,CAAA,CAAAK,MAAA;MAAA,CAAAJ,EAAA,GAAAK,MAIkB,CAAAC,YAAA,EAAU,KAAM,YACtB,IAAAN,EAAA,CAAAO,eAAA,EAAiB;MAEEC,SAAA,CAAAT,CAAA;MACjC,MAAAU,EAAA,GAAAV,CAAA,CAAAW,aAAA;MAEM,KAAAD,EAAA,EAEF;MACEpC,UAAA,CAAAmB,KAAA,CAAaT,GAAC,CAAGS,KAAG,CAAAmB,IAAA,IAAWF,EAAA,CAAA1B,GAAM,CAAGS,KAAA,CAAAE,MAAA,KAAAK,CAAA,CAAAhB,GAAA,CAAAS,KAAA,CAAAoB,MAAA,IAAAH,EAAA,CAAAI,qBAAA,GAAA9B,GAAA,CAAAS,KAAA,CAAAsB,SAAA;IAE5C,CAAO;IACP,MAAAC,iBAAW,GAAAhB,CAAA;MAEX,KAAA3B,KAAA,CAAWoB,KAAE,KAAAtB,QAAA,CAAAsB,KAAA,KAAA7B,SAAA,CAAAgC,WAAA,EACb;MACW,MAAAD,MAAA,GAAAsB,IAAA,CAAAC,GAAgB,CAAAlB,CAAA,CAAAmB,MAAA,CAAAL,qBACN,GAAA9B,GAAA,CAChBS,KAAI,CAAAsB,SAAA,CAAY,GAAIf,CAAA,CAAAhB,GAAA,CAAAS,KAAA,CAAAoB,MAAA;MAC3B,MAAAO,SAAA,GAAA/C,KAAA,CAAAoB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;MAEM,MAAA0B,uBAAuC,IAAA1B,MAAA,GAAAyB,SAAA,UAAA1B,WAAA,CAAAD,KAAA,GAAAtB,QAAA,CAAAsB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;MACvC/B,SAAA,CAAAgC,WAAiB,CAAAZ,GAAA,CAAAS,KAAkB,CAAA6B,MAAA,IAAAD,uBAAwB,GAAAzD,SAAA,CAAAgC,WAAA,CAAAZ,GAAA,CAAAS,KAAA,CAAAI,UAAA;IAE/D;IACG,MAAEY,SAAuB,GAAAT,CAAA;MAE5BA,CAAA,CAAAuB,wBAAA;MACA/C,UAAA;MACME,gBAAA,GAAAd,SAAA,CAAAgC,WACO,CAAA4B,YAAA;MAGH7C,eAAA,GAAAf,SAAgB,CAAAgC,WAAY,CAAA6B,WACT;MAE/B3C,QAAA,CAAA4C,gBAAA,cAAAC,wBAAA;MAEM7C,QAAA,CAAA4C,gBAA+B,YAAAE,sBAAA;MACnChD,qBAA2B,GAAAE,QAAA,CAAAC,aAAA;MACdD,QAAA,CAAAC,aAAA;IACb;IACA,MAAA4C,wBAA4B,GAAY3B,CAAA;MAC/B,KAAA7B,QAAA,CAAAsB,KAAA,KAAApB,KAAA,CAAAoB,KAAA,EACA;MACT,IAAAjB,UAAA,UAAwB,EACxB;MACF,MAAAqD,QAAA,GAAAvD,UAAA,CAAAmB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAmB,IAAA;MAEM,KAAAiB,QAAA,EACJ;MACA,MAAAlC,MAAA,IAAAxB,QAA0B,CAAAsB,KAAA,CAAAqB,qBAAA,GAAA9B,GAAA,CAAAS,KAAA,CAAAsB,SAAA,IAAAf,CAAA,CAAAhB,GAAA,CAAAS,KAAA,CAAAoB,MAAA;MAE1B,MAAMiB,kBAAW,GAAAzD,KAAiB,CAAAoB,KAAI,CAAAT,GAAA,CAAAS,KAAU,CAAAE,MAAA,IAAAkC,QAAA;MAChD,MAAeR,uBAAA,IAAA1B,MAAA,GAAAmC,kBAAA,UAAApC,WAAA,CAAAD,KAAA,GAAAtB,QAAA,CAAAsB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAE,MAAA;MAEf,IAAAX,GACG,CAAAS,KAAA,CAAA6B,MAAA,KAAS,YAAM;QAGlB1D,SAAA,CAAAgC,WAAA,CAAAZ,GAA2B,CAAMS,KAAA,CAAA6B,MAAU,IAAAD,uBAAgB,GAAA1C,eAAA;MAC3D,CAAM;QAIFf,SAAU,CAAAgC,WAAA,CAAAZ,GAAyB,CAAAS,KAAA,CAAA6B,MAAA,IAAAD,uBAAA,GAAA3C,gBAAA;MACrC;IACgD;IAEhD,MAAAkD,sBAA0B,GAAAA,CAAA,KAAY;MAExCpD,UAAA;MACFF,UAAA,CAAAmB,KAAA,CAAAT,GAAA,CAAAS,KAAA,CAAAmB,IAAA;MAEA9B,QAAA,CAAAiD,mBAAA,CAA+B,WAAM,EAAAJ,wBAAA;MACtB7C,QAAA,CAAAiD,mBAAA,YAAAH,sBAAA;MACbI,oBAAiB,EAAU;MAClB,IAAAvD,WAAA,EACAF,OAAA,CAAAkB,KAAA;IACT,CAAqB;IACjB,MAAAwC,yBAA6B,GAAAA,CAAA;MACnCxD,WAAA;MAEAF,OAAA,CAAAkB,KAAA,KAAAN,KAAA,CAAAI,IAAA;IACE,CAAc;IACN,MAAA2C,0BAAgB,GAAAA,CAAA;MAC1BzD,WAAA;MAEAF,OAAA,CAAAkB,KAAA,GAAAjB,UAAA;IACE,CAAc;IACd2D,eAAgB;MAClBH,oBAAA;MAEAlD,QAAA,CAAAiD,mBAAsB,YAAAH,sBAAA;IACpB,CAAqB;IACZ,MAAAI,oBAAA,GAAAA,CAAA;MACV,IAAAlD,QAAA,CAAAC,aAAA,KAAAH,qBAAA,EAEDE,QAAA,CAAAC,aAAA,GAA6BH,qBAAM;IACjC;IACEwD,gBAAyB,CAAAC,KAAA,CAAAzE,SAAA,oCAAAqE,yBAAA;IAC7BG,gBAAA,CAAAC,KAAA,CAAAzE,SAAA,qCAAAsE,0BAAA;IAEA,QAAAI,IAAA,EAAAC,MAAA;MACE,OAAAC,SAAiB,EAAkB,EAAAC,WAAA,CAAAC,UAAA;QACnCC,IAAA,EAAAC,KAAA,CAAA7E,EAAA,EAAA8E,CAAA;QACAC,SAAA;MAAA,CACF;QACAC,OAAA,EAAAC,OAAA,QACEC,cAAmC,CAAAC,kBAAA;UACnCC,OAAA;UACA/E,GAAA,EAAAD,QAAA;UACFiF,KAAA,EAAAC,cAAA,EAAAT,KAAA,CAAA7E,EAAA,EAAAiC,CAAA,SAAA4C,KAAA,CAAA7E,EAAA,EAAAuF,EAAA,CAAAV,KAAA,CAAA5D,GAAA,EAAAuE,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}