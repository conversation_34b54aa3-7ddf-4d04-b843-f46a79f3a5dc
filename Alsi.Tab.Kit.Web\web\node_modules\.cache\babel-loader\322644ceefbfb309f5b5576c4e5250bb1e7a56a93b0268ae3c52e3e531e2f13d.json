{"ast": null, "code": "/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduce(array, iteratee, accumulator, initAccum) {\n  var index = -1,\n    length = array == null ? 0 : array.length;\n  if (initAccum && length) {\n    accumulator = array[++index];\n  }\n  while (++index < length) {\n    accumulator = iteratee(accumulator, array[index], index, array);\n  }\n  return accumulator;\n}\nexport default arrayReduce;", "map": {"version": 3, "names": ["arrayReduce", "array", "iteratee", "accumulator", "initAccum", "index", "length"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_arrayReduce.js"], "sourcesContent": ["/**\n * A specialized version of `_.reduce` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the first element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduce(array, iteratee, accumulator, initAccum) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  if (initAccum && length) {\n    accumulator = array[++index];\n  }\n  while (++index < length) {\n    accumulator = iteratee(accumulator, array[index], index, array);\n  }\n  return accumulator;\n}\n\nexport default arrayReduce;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAE;EAC5D,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGL,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACK,MAAM;EAE7C,IAAIF,SAAS,IAAIE,MAAM,EAAE;IACvBH,WAAW,GAAGF,KAAK,CAAC,EAAEI,KAAK,CAAC;EAC9B;EACA,OAAO,EAAEA,KAAK,GAAGC,MAAM,EAAE;IACvBH,WAAW,GAAGD,QAAQ,CAACC,WAAW,EAAEF,KAAK,CAACI,KAAK,CAAC,EAAEA,KAAK,EAAEJ,KAAK,CAAC;EACjE;EACA,OAAOE,WAAW;AACpB;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}