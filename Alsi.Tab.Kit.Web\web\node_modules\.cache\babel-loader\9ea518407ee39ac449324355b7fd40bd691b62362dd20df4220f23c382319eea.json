{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { useAriaProps } from '../../../hooks/use-aria/index.mjs';\nconst tooltipV2Strategies = [\"absolute\", \"fixed\"];\nconst tooltipV2Placements = [\"top-start\", \"top-end\", \"top\", \"bottom-start\", \"bottom-end\", \"bottom\", \"left-start\", \"left-end\", \"left\", \"right-start\", \"right-end\", \"right\"];\nconst tooltipV2ContentProps = buildProps({\n  arrowPadding: {\n    type: definePropType(Number),\n    default: 5\n  },\n  effect: {\n    type: definePropType(String),\n    default: \"light\"\n  },\n  contentClass: String,\n  placement: {\n    type: definePropType(String),\n    values: tooltipV2Placements,\n    default: \"bottom\"\n  },\n  reference: {\n    type: definePropType(Object),\n    default: null\n  },\n  offset: {\n    type: Number,\n    default: 8\n  },\n  strategy: {\n    type: definePropType(String),\n    values: tooltipV2Strategies,\n    default: \"absolute\"\n  },\n  showArrow: Boolean,\n  ...useAriaProps([\"ariaLabel\"])\n});\nexport { tooltipV2ContentProps };", "map": {"version": 3, "names": ["tooltipV2Strategies", "tooltipV2Placements", "tooltipV2ContentProps", "buildProps", "arrowPadding", "type", "definePropType", "Number", "default", "effect", "String", "contentClass", "placement", "values", "reference", "Object", "offset", "strategy", "showArrow", "Boolean", "useAriaProps"], "sources": ["../../../../../../packages/components/tooltip-v2/src/content.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { useAriaProps } from '@element-plus/hooks'\n\nimport type { PopperEffect } from '@element-plus/components/popper'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Placement, Strategy, VirtualElement } from '@floating-ui/dom'\n\nconst tooltipV2Strategies = ['absolute', 'fixed'] as const\n\nconst tooltipV2Placements = [\n  'top-start',\n  'top-end',\n  'top',\n  'bottom-start',\n  'bottom-end',\n  'bottom',\n  'left-start',\n  'left-end',\n  'left',\n  'right-start',\n  'right-end',\n  'right',\n] as const\n\nexport const tooltipV2ContentProps = buildProps({\n  arrowPadding: {\n    type: definePropType<number>(Number),\n    default: 5,\n  },\n  effect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'light',\n  },\n  contentClass: String,\n  /**\n   * Placement of tooltip content relative to reference element (when absent it refers to trigger)\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: tooltipV2Placements,\n    default: 'bottom',\n  },\n  /**\n   * Reference element for tooltip content to set its position\n   */\n  reference: {\n    type: definePropType<HTMLElement | VirtualElement | null>(Object),\n    default: null,\n  },\n  offset: {\n    type: Number,\n    default: 8,\n  },\n  strategy: {\n    type: definePropType<Strategy>(String),\n    values: tooltipV2Strategies,\n    default: 'absolute',\n  },\n  showArrow: Boolean,\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport type TooltipV2ContentProps = ExtractPropTypes<\n  typeof tooltipV2ContentProps\n>\n"], "mappings": ";;AAEA,MAAMA,mBAAmB,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC;AACjD,MAAMC,mBAAmB,GAAG,CAC1B,WAAW,EACX,SAAS,EACT,KAAK,EACL,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,MAAM,EACN,aAAa,EACb,WAAW,EACX,OAAO,CACR;AACW,MAACC,qBAAqB,GAAGC,UAAU,CAAC;EAC9CC,YAAY,EAAE;IACZC,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,OAAO,EAAE;EACb,CAAG;EACDC,MAAM,EAAE;IACNJ,IAAI,EAAEC,cAAc,CAACI,MAAM,CAAC;IAC5BF,OAAO,EAAE;EACb,CAAG;EACDG,YAAY,EAAED,MAAM;EACpBE,SAAS,EAAE;IACTP,IAAI,EAAEC,cAAc,CAACI,MAAM,CAAC;IAC5BG,MAAM,EAAEZ,mBAAmB;IAC3BO,OAAO,EAAE;EACb,CAAG;EACDM,SAAS,EAAE;IACTT,IAAI,EAAEC,cAAc,CAACS,MAAM,CAAC;IAC5BP,OAAO,EAAE;EACb,CAAG;EACDQ,MAAM,EAAE;IACNX,IAAI,EAAEE,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDS,QAAQ,EAAE;IACRZ,IAAI,EAAEC,cAAc,CAACI,MAAM,CAAC;IAC5BG,MAAM,EAAEb,mBAAmB;IAC3BQ,OAAO,EAAE;EACb,CAAG;EACDU,SAAS,EAAEC,OAAO;EAClB,GAAGC,YAAY,CAAC,CAAC,WAAW,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}