{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport basePickBy from './_basePickBy.js';\nimport getAllKeysIn from './_getAllKeysIn.js';\n\n/**\n * Creates an object composed of the `object` properties `predicate` returns\n * truthy for. The predicate is invoked with two arguments: (value, key).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The source object.\n * @param {Function} [predicate=_.identity] The function invoked per property.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pickBy(object, _.isNumber);\n * // => { 'a': 1, 'c': 3 }\n */\nfunction pickBy(object, predicate) {\n  if (object == null) {\n    return {};\n  }\n  var props = arrayMap(getAllKeysIn(object), function (prop) {\n    return [prop];\n  });\n  predicate = baseIteratee(predicate);\n  return basePickBy(object, props, function (value, path) {\n    return predicate(value, path[0]);\n  });\n}\nexport default pickBy;", "map": {"version": 3, "names": ["arrayMap", "baseIteratee", "basePickBy", "getAllKeysIn", "pickBy", "object", "predicate", "props", "prop", "value", "path"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/pickBy.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport basePickBy from './_basePickBy.js';\nimport getAllKeysIn from './_getAllKeysIn.js';\n\n/**\n * Creates an object composed of the `object` properties `predicate` returns\n * truthy for. The predicate is invoked with two arguments: (value, key).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Object\n * @param {Object} object The source object.\n * @param {Function} [predicate=_.identity] The function invoked per property.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pickBy(object, _.isNumber);\n * // => { 'a': 1, 'c': 3 }\n */\nfunction pickBy(object, predicate) {\n  if (object == null) {\n    return {};\n  }\n  var props = arrayMap(getAllKeysIn(object), function(prop) {\n    return [prop];\n  });\n  predicate = baseIteratee(predicate);\n  return basePickBy(object, props, function(value, path) {\n    return predicate(value, path[0]);\n  });\n}\n\nexport default pickBy;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACjC,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,CAAC,CAAC;EACX;EACA,IAAIE,KAAK,GAAGP,QAAQ,CAACG,YAAY,CAACE,MAAM,CAAC,EAAE,UAASG,IAAI,EAAE;IACxD,OAAO,CAACA,IAAI,CAAC;EACf,CAAC,CAAC;EACFF,SAAS,GAAGL,YAAY,CAACK,SAAS,CAAC;EACnC,OAAOJ,UAAU,CAACG,MAAM,EAAEE,KAAK,EAAE,UAASE,KAAK,EAAEC,IAAI,EAAE;IACrD,OAAOJ,SAAS,CAACG,KAAK,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC;AACJ;AAEA,eAAeN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}