{"ast": null, "code": "import { panelRangeSharedProps } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst panelMonthRangeProps = buildProps({\n  ...panelRangeSharedProps\n});\nconst panelMonthRangeEmits = [\"pick\", \"set-picker-option\", \"calendar-change\"];\nexport { panelMonthRangeEmits, panelMonthRangeProps };", "map": {"version": 3, "names": ["panelMonthRangeProps", "buildProps", "panelRangeSharedProps", "panelMonthRangeEmits"], "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-month-range.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { panelRangeSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const panelMonthRangeProps = buildProps({\n  ...panelRangeSharedProps,\n} as const)\n\nexport const panelMonthRangeEmits = [\n  'pick',\n  'set-picker-option',\n  'calendar-change',\n]\n\nexport type PanelMonthRangeProps = ExtractPropTypes<typeof panelMonthRangeProps>\n"], "mappings": ";;AAEY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7C,GAAGC;AACL,CAAC;AACW,MAACC,oBAAoB,GAAG,CAClC,MAAM,EACN,mBAAmB,EACnB,iBAAiB,CACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}