{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { defineComponent, ref, computed, watch, nextTick, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, Fragment, renderList, withKeys, withModifiers, createVNode } from 'vue';\nimport dayjs from 'dayjs';\nimport { basicYearTableProps } from '../props/basic-year-table.mjs';\nimport { getValidDateOfYear } from '../utils.mjs';\nimport ElDatePickerCell from './basic-cell-render.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { castArray } from '../../../../utils/arrays.mjs';\nimport { rangeArr } from '../../../time-picker/src/utils.mjs';\nimport { hasClass } from '../../../../utils/dom/style.mjs';\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  __name: \"basic-year-table\",\n  props: basicYearTableProps,\n  emits: [\"changerange\", \"pick\", \"select\"],\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const props = __props;\n    const datesInYear = (year, lang2) => {\n      const firstDay = dayjs(String(year)).locale(lang2).startOf(\"year\");\n      const lastDay = firstDay.endOf(\"year\");\n      const numOfDays = lastDay.dayOfYear();\n      return rangeArr(numOfDays).map(n => firstDay.add(n, \"day\").toDate());\n    };\n    const ns = useNamespace(\"year-table\");\n    const {\n      t,\n      lang\n    } = useLocale();\n    const tbodyRef = ref();\n    const currentCellRef = ref();\n    const startYear = computed(() => {\n      return Math.floor(props.date.year() / 10) * 10;\n    });\n    const tableRows = ref([[], [], []]);\n    const lastRow = ref();\n    const lastColumn = ref();\n    const rows = computed(() => {\n      var _a;\n      const rows2 = tableRows.value;\n      const now = dayjs().locale(lang.value).startOf(\"year\");\n      for (let i = 0; i < 3; i++) {\n        const row = rows2[i];\n        for (let j = 0; j < 4; j++) {\n          if (i * 4 + j >= 10) {\n            break;\n          }\n          let cell = row[j];\n          if (!cell) {\n            cell = {\n              row: i,\n              column: j,\n              type: \"normal\",\n              inRange: false,\n              start: false,\n              end: false,\n              text: -1,\n              disabled: false\n            };\n          }\n          cell.type = \"normal\";\n          const index = i * 4 + j + startYear.value;\n          const calTime = dayjs().year(index);\n          const calEndDate = props.rangeState.endDate || props.maxDate || props.rangeState.selecting && props.minDate || null;\n          cell.inRange = !!(props.minDate && calTime.isSameOrAfter(props.minDate, \"year\") && calEndDate && calTime.isSameOrBefore(calEndDate, \"year\")) || !!(props.minDate && calTime.isSameOrBefore(props.minDate, \"year\") && calEndDate && calTime.isSameOrAfter(calEndDate, \"year\"));\n          if ((_a = props.minDate) == null ? void 0 : _a.isSameOrAfter(calEndDate)) {\n            cell.start = !!(calEndDate && calTime.isSame(calEndDate, \"year\"));\n            cell.end = !!(props.minDate && calTime.isSame(props.minDate, \"year\"));\n          } else {\n            cell.start = !!(props.minDate && calTime.isSame(props.minDate, \"year\"));\n            cell.end = !!(calEndDate && calTime.isSame(calEndDate, \"year\"));\n          }\n          const isToday = now.isSame(calTime);\n          if (isToday) {\n            cell.type = \"today\";\n          }\n          cell.text = index;\n          const cellDate = calTime.toDate();\n          cell.disabled = props.disabledDate && props.disabledDate(cellDate) || false;\n          row[j] = cell;\n        }\n      }\n      return rows2;\n    });\n    const focus = () => {\n      var _a;\n      (_a = currentCellRef.value) == null ? void 0 : _a.focus();\n    };\n    const getCellKls = cell => {\n      const kls = {};\n      const today = dayjs().locale(lang.value);\n      const year = cell.text;\n      kls.disabled = props.disabledDate ? datesInYear(year, lang.value).every(props.disabledDate) : false;\n      kls.today = today.year() === year;\n      kls.current = castArray(props.parsedValue).findIndex(d => d.year() === year) >= 0;\n      if (cell.inRange) {\n        kls[\"in-range\"] = true;\n        if (cell.start) {\n          kls[\"start-date\"] = true;\n        }\n        if (cell.end) {\n          kls[\"end-date\"] = true;\n        }\n      }\n      return kls;\n    };\n    const isSelectedCell = cell => {\n      const year = cell.text;\n      return castArray(props.date).findIndex(date => date.year() === year) >= 0;\n    };\n    const handleYearTableClick = event => {\n      var _a;\n      const target = (_a = event.target) == null ? void 0 : _a.closest(\"td\");\n      if (!target || !target.textContent || hasClass(target, \"disabled\")) return;\n      const column = target.cellIndex;\n      const row = target.parentNode.rowIndex;\n      const selectedYear = row * 4 + column + startYear.value;\n      const newDate = dayjs().year(selectedYear);\n      if (props.selectionMode === \"range\") {\n        if (!props.rangeState.selecting) {\n          emit(\"pick\", {\n            minDate: newDate,\n            maxDate: null\n          });\n          emit(\"select\", true);\n        } else {\n          if (props.minDate && newDate >= props.minDate) {\n            emit(\"pick\", {\n              minDate: props.minDate,\n              maxDate: newDate\n            });\n          } else {\n            emit(\"pick\", {\n              minDate: newDate,\n              maxDate: props.minDate\n            });\n          }\n          emit(\"select\", false);\n        }\n      } else if (props.selectionMode === \"years\") {\n        if (event.type === \"keydown\") {\n          emit(\"pick\", castArray(props.parsedValue), false);\n          return;\n        }\n        const vaildYear = getValidDateOfYear(newDate.startOf(\"year\"), lang.value, props.disabledDate);\n        const newValue = hasClass(target, \"current\") ? castArray(props.parsedValue).filter(d => (d == null ? void 0 : d.year()) !== selectedYear) : castArray(props.parsedValue).concat([vaildYear]);\n        emit(\"pick\", newValue);\n      } else {\n        emit(\"pick\", selectedYear);\n      }\n    };\n    const handleMouseMove = event => {\n      var _a;\n      if (!props.rangeState.selecting) return;\n      const target = (_a = event.target) == null ? void 0 : _a.closest(\"td\");\n      if (!target) return;\n      const row = target.parentNode.rowIndex;\n      const column = target.cellIndex;\n      if (rows.value[row][column].disabled) return;\n      if (row !== lastRow.value || column !== lastColumn.value) {\n        lastRow.value = row;\n        lastColumn.value = column;\n        emit(\"changerange\", {\n          selecting: true,\n          endDate: dayjs().year(startYear.value).add(row * 4 + column, \"year\")\n        });\n      }\n    };\n    watch(() => props.date, async () => {\n      var _a, _b;\n      if ((_a = tbodyRef.value) == null ? void 0 : _a.contains(document.activeElement)) {\n        await nextTick();\n        (_b = currentCellRef.value) == null ? void 0 : _b.focus();\n      }\n    });\n    expose({\n      focus\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"table\", {\n        role: \"grid\",\n        \"aria-label\": unref(t)(\"el.datepicker.yearTablePrompt\"),\n        class: normalizeClass(unref(ns).b()),\n        onClick: handleYearTableClick,\n        onMousemove: handleMouseMove\n      }, [createElementVNode(\"tbody\", {\n        ref_key: \"tbodyRef\",\n        ref: tbodyRef\n      }, [(openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), (row, rowKey) => {\n        return openBlock(), createElementBlock(\"tr\", {\n          key: rowKey\n        }, [(openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, cellKey) => {\n          return openBlock(), createElementBlock(\"td\", {\n            key: `${rowKey}_${cellKey}`,\n            ref_for: true,\n            ref: el => isSelectedCell(cell) && (currentCellRef.value = el),\n            class: normalizeClass([\"available\", getCellKls(cell)]),\n            \"aria-selected\": isSelectedCell(cell),\n            \"aria-label\": String(cell.text),\n            tabindex: isSelectedCell(cell) ? 0 : -1,\n            onKeydown: [withKeys(withModifiers(handleYearTableClick, [\"prevent\", \"stop\"]), [\"space\"]), withKeys(withModifiers(handleYearTableClick, [\"prevent\", \"stop\"]), [\"enter\"])]\n          }, [createVNode(unref(ElDatePickerCell), {\n            cell\n          }, null, 8, [\"cell\"])], 42, [\"aria-selected\", \"aria-label\", \"tabindex\", \"onKeydown\"]);\n        }), 128))]);\n      }), 128))], 512)], 42, [\"aria-label\"]);\n    };\n  }\n});\nvar YearTable = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"basic-year-table.vue\"]]);\nexport { YearTable as default };", "map": {"version": 3, "names": ["datesInYear", "year", "lang2", "firstDay", "dayjs", "String", "locale", "startOf", "lastDay", "endOf", "numOfDays", "dayOfYear", "rangeArr", "map", "n", "add", "toDate", "ns", "useNamespace", "t", "lang", "useLocale", "tbodyRef", "ref", "currentCellRef", "startYear", "computed", "Math", "floor", "props", "date", "tableRows", "lastRow", "lastColumn", "rows", "_a", "rows2", "value", "now", "i", "row", "j", "cell", "column", "type", "inRange", "start", "end", "text", "disabled", "index", "calTime", "calEndDate", "rangeState", "endDate", "maxDate", "selecting", "minDate", "isSameOrAfter", "isSameOrBefore", "isSame", "isToday", "cellDate", "disabledDate", "focus", "getCellKls", "kls", "today", "every", "current", "<PERSON><PERSON><PERSON><PERSON>", "parsedValue", "findIndex", "d", "isSelectedCell", "handleYearTableClick", "event", "target", "closest", "textContent", "hasClass", "cellIndex", "parentNode", "rowIndex", "selected<PERSON>ear", "newDate", "selectionMode", "emit", "vaildYear", "getValidDateOfYear", "newValue", "filter", "concat", "handleMouseMove", "watch", "_b", "contains", "document", "activeElement", "nextTick", "expose", "_ctx", "_cache", "openBlock", "createElementBlock", "role", "unref", "class", "normalizeClass", "b"], "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/basic-year-table.vue"], "sourcesContent": ["<template>\n  <table\n    role=\"grid\"\n    :aria-label=\"t('el.datepicker.yearTablePrompt')\"\n    :class=\"ns.b()\"\n    @click=\"handleYearTableClick\"\n    @mousemove=\"handleMouseMove\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr v-for=\"(row, rowKey) in rows\" :key=\"rowKey\">\n        <td\n          v-for=\"(cell, cellKey) in row\"\n          :key=\"`${rowKey}_${cellKey}`\"\n          :ref=\"(el) => isSelectedCell(cell) && (currentCellRef = el as HTMLElement)\"\n          class=\"available\"\n          :class=\"getCellKls(cell)\"\n          :aria-selected=\"isSelectedCell(cell)\"\n          :aria-label=\"String(cell.text)\"\n          :tabindex=\"isSelectedCell(cell) ? 0 : -1\"\n          @keydown.space.prevent.stop=\"handleYearTableClick\"\n          @keydown.enter.prevent.stop=\"handleYearTableClick\"\n        >\n          <el-date-picker-cell :cell=\"cell\" />\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, ref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { rangeArr } from '@element-plus/components/time-picker'\nimport { castArray, hasClass } from '@element-plus/utils'\nimport { basicYearTableProps } from '../props/basic-year-table'\nimport { getValidDateOfYear } from '../utils'\nimport ElDatePickerCell from './basic-cell-render'\n\ntype YearCell = {\n  column: number\n  row: number\n  disabled: boolean\n  start: boolean\n  end: boolean\n  text: number\n  type: 'normal' | 'today'\n  inRange: boolean\n}\n\nconst datesInYear = (year: number, lang: string) => {\n  const firstDay = dayjs(String(year)).locale(lang).startOf('year')\n  const lastDay = firstDay.endOf('year')\n  const numOfDays = lastDay.dayOfYear()\n  return rangeArr(numOfDays).map((n) => firstDay.add(n, 'day').toDate())\n}\n\nconst props = defineProps(basicYearTableProps)\nconst emit = defineEmits(['changerange', 'pick', 'select'])\n\nconst ns = useNamespace('year-table')\n\nconst { t, lang } = useLocale()\nconst tbodyRef = ref<HTMLElement>()\nconst currentCellRef = ref<HTMLElement>()\nconst startYear = computed(() => {\n  return Math.floor(props.date.year() / 10) * 10\n})\n\nconst tableRows = ref<YearCell[][]>([[], [], []])\nconst lastRow = ref<number>()\nconst lastColumn = ref<number>()\nconst rows = computed(() => {\n  const rows = tableRows.value\n  const now = dayjs().locale(lang.value).startOf('year')\n\n  for (let i = 0; i < 3; i++) {\n    const row = rows[i]\n    for (let j = 0; j < 4; j++) {\n      if (i * 4 + j >= 10) {\n        break\n      }\n      let cell = row[j]\n      if (!cell) {\n        cell = {\n          row: i,\n          column: j,\n          type: 'normal',\n          inRange: false,\n          start: false,\n          end: false,\n          text: -1,\n          disabled: false,\n        }\n      }\n      cell.type = 'normal'\n      const index = i * 4 + j + startYear.value\n      const calTime = dayjs().year(index)\n\n      const calEndDate =\n        props.rangeState.endDate ||\n        props.maxDate ||\n        (props.rangeState.selecting && props.minDate) ||\n        null\n\n      cell.inRange =\n        !!(\n          props.minDate &&\n          calTime.isSameOrAfter(props.minDate, 'year') &&\n          calEndDate &&\n          calTime.isSameOrBefore(calEndDate, 'year')\n        ) ||\n        !!(\n          props.minDate &&\n          calTime.isSameOrBefore(props.minDate, 'year') &&\n          calEndDate &&\n          calTime.isSameOrAfter(calEndDate, 'year')\n        )\n\n      if (props.minDate?.isSameOrAfter(calEndDate)) {\n        cell.start = !!(calEndDate && calTime.isSame(calEndDate, 'year'))\n        cell.end = !!(props.minDate && calTime.isSame(props.minDate, 'year'))\n      } else {\n        cell.start = !!(props.minDate && calTime.isSame(props.minDate, 'year'))\n        cell.end = !!(calEndDate && calTime.isSame(calEndDate, 'year'))\n      }\n\n      const isToday = now.isSame(calTime)\n      if (isToday) {\n        cell.type = 'today'\n      }\n      cell.text = index\n      const cellDate = calTime.toDate()\n      cell.disabled =\n        (props.disabledDate && props.disabledDate(cellDate)) || false\n      row[j] = cell\n    }\n  }\n  return rows\n})\n\nconst focus = () => {\n  currentCellRef.value?.focus()\n}\n\nconst getCellKls = (cell: YearCell) => {\n  const kls: Record<string, boolean> = {}\n  const today = dayjs().locale(lang.value)\n  const year = cell.text\n\n  kls.disabled = props.disabledDate\n    ? datesInYear(year, lang.value).every(props.disabledDate)\n    : false\n\n  kls.today = today.year() === year\n  kls.current =\n    castArray(props.parsedValue).findIndex((d) => d!.year() === year) >= 0\n\n  if (cell.inRange) {\n    kls['in-range'] = true\n\n    if (cell.start) {\n      kls['start-date'] = true\n    }\n\n    if (cell.end) {\n      kls['end-date'] = true\n    }\n  }\n  return kls\n}\n\nconst isSelectedCell = (cell: YearCell) => {\n  const year = cell.text\n  return castArray(props.date).findIndex((date) => date.year() === year) >= 0\n}\n\nconst handleYearTableClick = (event: MouseEvent | KeyboardEvent) => {\n  const target = (event.target as HTMLElement)?.closest(\n    'td'\n  ) as HTMLTableCellElement\n  if (!target || !target.textContent || hasClass(target, 'disabled')) return\n\n  const column = target.cellIndex\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const selectedYear = row * 4 + column + startYear.value\n  const newDate = dayjs().year(selectedYear)\n  if (props.selectionMode === 'range') {\n    if (!props.rangeState.selecting) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (props.minDate && newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  } else if (props.selectionMode === 'years') {\n    if (event.type === 'keydown') {\n      emit('pick', castArray(props.parsedValue), false)\n      return\n    }\n    const vaildYear = getValidDateOfYear(\n      newDate.startOf('year'),\n      lang.value,\n      props.disabledDate\n    )\n    const newValue = hasClass(target, 'current')\n      ? castArray(props.parsedValue).filter((d) => d?.year() !== selectedYear)\n      : castArray(props.parsedValue).concat([vaildYear])\n    emit('pick', newValue)\n  } else {\n    emit('pick', selectedYear)\n  }\n}\n\nconst handleMouseMove = (event: MouseEvent) => {\n  if (!props.rangeState.selecting) return\n  const target = (event.target as HTMLElement)?.closest(\n    'td'\n  ) as HTMLTableCellElement\n  if (!target) return\n\n  const row = (target.parentNode as HTMLTableRowElement).rowIndex\n  const column = (target as HTMLTableCellElement).cellIndex\n\n  // can not select disabled date\n  if (rows.value[row][column].disabled) return\n\n  // only update rangeState when mouse moves to a new cell\n  // this avoids frequent Date object creation and improves performance\n  if (row !== lastRow.value || column !== lastColumn.value) {\n    lastRow.value = row\n    lastColumn.value = column\n    emit('changerange', {\n      selecting: true,\n      endDate: dayjs()\n        .year(startYear.value)\n        .add(row * 4 + column, 'year'),\n    })\n  }\n}\n\nwatch(\n  () => props.date,\n  async () => {\n    if (tbodyRef.value?.contains(document.activeElement)) {\n      await nextTick()\n      currentCellRef.value?.focus()\n    }\n  }\n)\n\ndefineExpose({\n  /**\n   * @description focus on the current cell\n   */\n  focus,\n})\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAkDM,MAAAA,WAAA,GAAcA,CAACC,IAAA,EAAcC,KAAiB;MAC5C,MAAAC,QAAA,GAAWC,KAAM,CAAAC,MAAA,CAAOJ,IAAI,CAAC,EAAEK,MAAO,CAAAJ,KAAI,CAAE,CAAAK,OAAA,CAAQ,MAAM;MAC1D,MAAAC,OAAA,GAAUL,QAAS,CAAAM,KAAA,CAAM,MAAM;MAC/B,MAAAC,SAAA,GAAYF,OAAA,CAAQG,SAAU;MACpC,OAAOC,QAAS,CAAAF,SAAS,CAAE,CAAAG,GAAA,CAAKC,CAAA,IAAMX,QAAS,CAAAY,GAAA,CAAID,CAAG,OAAK,CAAE,CAAAE,MAAA,EAAQ;IAAA,CACvE;IAKM,MAAAC,EAAA,GAAKC,YAAA,CAAa,YAAY;IAEpC,MAAM;MAAEC,CAAA;MAAGC;IAAK,IAAIC,SAAU;IAC9B,MAAMC,QAAA,GAAWC,GAAiB;IAClC,MAAMC,cAAA,GAAiBD,GAAiB;IAClC,MAAAE,SAAA,GAAYC,QAAA,CAAS,MAAM;MAC/B,OAAOC,IAAA,CAAKC,KAAM,CAAAC,KAAA,CAAMC,IAAA,CAAK7B,IAAK,KAAI,EAAE,CAAI;IAAA,CAC7C;IAEK,MAAA8B,SAAA,GAAYR,GAAA,CAAkB,CAAC,IAAI,EAAC,EAAG,EAAE,CAAC;IAChD,MAAMS,OAAA,GAAUT,GAAY;IAC5B,MAAMU,UAAA,GAAaV,GAAY;IACzB,MAAAW,IAAA,GAAOR,QAAA,CAAS,MAAM;MAC1B,IAAAS,EAAM;MACA,MAAAC,KAAA,GAAAL,SAAc,CAAAM,KAAA;MAEpB,MAAAC,GAAS,GAAIlC,KAAO,GAAAE,MAAQ,CAAAc,IAAA,CAAAiB,KAAA,EAAA9B,OAAA;MACpB,SAAAgC,CAAA,IAAM,EAAAA,CAAA,GAAK,CAAC,EAAAA,CAAA;QAClB,MAAAC,GAAS,GAAIJ,KAAO,CAAAG,CAAA;QACd,SAAAE,CAAI,GAAI,GAAAA,CAAA,GAAS,GAAAA,CAAA;UACnB,IAAAF,CAAA,OAAAE,CAAA;YACF;UACA;UACA,IAAIC,IAAO,GAAAF,GAAA,CAAAC,CAAA;UACF,KAAAC,IAAA;YAAAA,IACA;cACLF,GAAQ,EAAAD,CAAA;cACRI,MAAM,EAAAF,CAAA;cACNG,IAAS;cACTC,OAAO;cACPC,KAAK;cACLC,GAAM;cACNC,IAAU;cACZC,QAAA;YAAA,CACF;UACA;UACAP,IAAA,CAAAE,IAAc,WAAQ;UACtB,MAAMM,KAAU,GAAAX,CAAA,OAAME,CAAE,GAAAhB,SAAU,CAAAY,KAAA;UAE5B,MAAAc,OAAA,GAAA/C,KACE,GAAAH,IAAA,CAAAiD,KAAA;UAKR,MACEE,UAAC,GACCvB,KAAM,CACNwB,UAAA,CAAAC,OAAQ,IAAczB,KAAA,CAAA0B,OAAA,IAAe1B,KAAA,CAAAwB,UACrC,CAAAG,SAAA,IAAA3B,KAAA,CAAA4B,OACuB;UAS3Bf,IAAI,CAAMG,OAAA,MAAAhB,KAAuB,CAAA4B,OAAA,IAAAN,OAAA,CAAUO,aAAG,CAAA7B,KAAA,CAAA4B,OAAA,aAAAL,UAAA,IAAAD,OAAA,CAAAQ,cAAA,CAAAP,UAAA,iBAAAvB,KAAA,CAAA4B,OAAA,IAAAN,OAAA,CAAAQ,cAAA,CAAA9B,KAAA,CAAA4B,OAAA,aAAAL,UAAA,IAAAD,OAAA,CAAAO,aAAA,CAAAN,UAAA;UAC5C,KAAAjB,EAAK,GAAAN,KAAQ,CAAC4B,OAAA,SAAgB,GAAQ,SAAAtB,EAAA,CAAOuB,aAAkB,CAAAN,UAAA;YAC1DV,IAAA,CAAAI,KAAA,GAAS,GAAAM,UAAA,IAAAD,OAAyB,CAAAS,MAAA,CAAAR,UAAa,QAAe;YAC9DV,IAAA,CAAAK,GAAA,MAAAlB,KAAA,CAAA4B,OAAA,IAAAN,OAAA,CAAAS,MAAA,CAAA/B,KAAA,CAAA4B,OAAA;UACL,CAAK;YACLf,IAAA,CAAKI,KAAA,GAAO,CAAE,EAAAjB,KAAA,CAAA4B,OAAsB,IAAAN,OAAA,CAAAS,MAAA,CAAA/B,KAAA,CAAA4B,OAAyB;YAC/Df,IAAA,CAAAK,GAAA,MAAAK,UAAA,IAAAD,OAAA,CAAAS,MAAA,CAAAR,UAAA;UAEA;UACA,MAAaS,OAAA,GAAAvB,GAAA,CAAAsB,MAAA,CAAAT,OAAA;UACX,IAAAU,OAAY;YACdnB,IAAA,CAAAE,IAAA;UACA;UACMF,IAAA,CAAAM,IAAA,GAAAE,KAAW;UACjB,MAAAY,QAAA,GACSX,OAAA,CAAAnC,MAAA;UACT0B,IAAI,CAACO,QAAI,GAAApB,KAAA,CAAAkC,YAAA,IAAAlC,KAAA,CAAAkC,YAAA,CAAAD,QAAA;UACXtB,GAAA,CAAAC,CAAA,IAAAC,IAAA;QAAA;MAEF;MACD,OAAAN,KAAA;IAED;IACE,MAAA4B,KAAA,GAAAA,CAAA;MACF,IAAA7B,EAAA;MAEM,CAAAA,EAAA,GAAAX,cAAiC,CAAAa,KAAA,qBAAAF,EAAA,CAAA6B,KAAA;IACrC;IACA,MAAAC,UAAc,GAAAvB,IAAQ;MACtB,MAAMwB,GAAA,KAAY;MAEd,MAAAC,KAAA,GAAA/D,KAAiB,GAAAE,MAAA,CAAAc,IAAA,CAAAiB,KACL;MAGZ,MAAApC,IAAA,GAAcyC,IAAA,CAAAM,IAAA;MAClBkB,GAAA,CAAIjB,QACF,GAAApB,KAAA,CAAAkC,YAAgB,GAAA/D,WAAa,CAAAC,IAAA,EAAWmB,IAAA,CAAAiB,KAAS,EAAA+B,KAAW,CAAAvC,KAAI,CAAKkC,YAAA;MAEvEG,GAAA,CAAIC,KAAK,GAASA,KAAA,CAAAlE,IAAA,OAAAA,IAAA;MAChBiE,GAAA,CAAAG,OAAA,GAAAC,SAAkB,CAAAzC,KAAA,CAAA0C,WAAA,EAAAC,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAxE,IAAA,OAAAA,IAAA;MAElB,IAAAyC,IAAA,CAAAG,OAAgB;QACdqB,GAAA,eAAgB,IAAI;QACtB,IAAAxB,IAAA,CAAAI,KAAA;UAEAoB,GAAA,aAAc;QACZ;QACF,IAAAxB,IAAA,CAAAK,GAAA;UACFmB,GAAA;QACA;MAAO;MAGH,OAAAA,GAAA;IACJ;IACO,MAAAQ,cAAgB,GAAAhC,IAAA,IAAM;MAC/B,MAAAzC,IAAA,GAAAyC,IAAA,CAAAM,IAAA;MAEM,OAAAsB,SAAA,CAAAzC,KAAA,CAAAC,IAAuB,CAAC,CAAsC0C,SAAA,CAAA1C,IAAA,IAAAA,IAAA,CAAA7B,IAAA,OAAAA,IAAA;IAClE,CAAM;IACJ,MAAA0E,oBAAA,GAAAC,KAAA;MACF,IAAAzC,EAAA;MACI,MAAA0C,MAAA,GAAW,CAAC1C,EAAA,GAAAyC,KAAA,CAAAC,MAAA,KAAsB,IAAS,YAAA1C,EAAA,CAAA2C,OAAA,CAAkB,IAAG;MAEpE,KAAAD,MAAA,IAAe,CAAOA,MAAA,CAAAE,WAAA,IAAAC,QAAA,CAAAH,MAAA,eAChB;MACN,MAAMlC,MAAe,GAAAkC,MAAA,CAAAI,SAAU;MAC/B,MAAMzC,GAAU,GAAAqC,MAAA,CAAAK,UAAa,CAAYC,QAAA;MACrC,MAAAC,YAAA,GAAA5C,GAAA,IAAwB,GAASG,MAAA,GAAAlB,SAAA,CAAAY,KAAA;MAC/B,MAAAgD,OAAO,GAAAjF,KAAA,GAAWH,IAAW,CAAAmF,YAAA;MAC/B,IAAAvD,KAAK,CAAAyD,aAAU,KAAS,OAAS;QACjC,KAAAzD,KAAA,CAAAwB,UAAmB,CAAAG,SAAA;UACd+B,IAAA;YAAA9B,OAAA,EAAA4B,OAAA;YAAA9B,OAAA;UAAA;UACLgC,IAAI,CAAM;QACR;UACF,IAAO1D,KAAA,CAAA4B,OAAA,IAAA4B,OAAA,IAAAxD,KAAA,CAAA4B,OAAA;YACL8B,IAAA,CAAK,QAAQ;cAAE9B,OAAA,EAAS5B,KAAA,CAAA4B,OAAkB;cAAAF,OAAA,EAAM8B;YAAA,CAAS;UAAA,CAC3D;YACAE,IAAA,SAAe;cAAK9B,OAAA,EAAA4B,OAAA;cAAA9B,OAAA,EAAA1B,KAAA,CAAA4B;YAAA;UAAA;UAExB8B,IAAA,SAAiB;QACf;MACE,WAAa1D,KAAA,CAAAyD,aAAgB,cAAW;QACxC,IAAAV,KAAA,CAAAhC,IAAA;UACF2C,IAAA,SAAAjB,SAAA,CAAAzC,KAAA,CAAA0C,WAAA;UACA;QAAkB;QACM,MACjBiB,SAAA,GAAAC,kBAAA,CAAAJ,OAAA,CAAA9E,OAAA,UAAAa,IAAA,CAAAiB,KAAA,EAAAR,KAAA,CAAAkC,YAAA;QAAA,MACC2B,QAAA,GAAAV,QAAA,CAAAH,MAAA,eAAAP,SAAA,CAAAzC,KAAA,CAAA0C,WAAA,EAAAoB,MAAA,CAAAlB,CAAA,KAAAA,CAAA,oBAAAA,CAAA,CAAAxE,IAAA,QAAAmF,YAAA,IAAAd,SAAA,CAAAzC,KAAA,CAAA0C,WAAA,EAAAqB,MAAA,EAAAJ,SAAA;QACRD,IAAA,SAAAG,QAAA;MACA,CAAM;QAGNH,IAAA,CAAK,QAAQH,YAAQ;MAAA;IAErB;IACF,MAAAS,eAAA,GAAAjB,KAAA;MACF,IAAAzC,EAAA;MAEM,KAAAN,KAAA,CAAAwB,UAAA,CAAkBG,SAAuB,EACzC;MACE,MAAAqB,MAAA,GAAU,CAAA1C,EAAA,GAAMyC,KAAwB,CAAAC,MAAA,qBAAA1C,EAAA,CAAA2C,OAAA;MAC5C,KAAAD,MAAA,EACF;MACA,MAAarC,GAAA,GAAAqC,MAAA,CAAAK,UAAA,CAAAC,QAAA;MAEP,MAAAxC,MAAO,GAAAkC,MAA0C,CAAAI,SAAA;MACvD,IAAA/C,IAAA,CAAAG,KAAA,CAAgBG,GAAgC,EAAAG,MAAA,EAAAM,QAAA,EAGhD;MAIA,IAAIT,GAAQ,KAAAR,OAAA,CAAQK,KAAS,IAAAM,MAAA,KAAWV,UAAA,CAAWI,KAAO;QACxDL,OAAA,CAAQK,KAAQ,GAAAG,GAAA;QAChBP,UAAA,CAAWI,KAAQ,GAAAM,MAAA;QACnB4C,IAAA,CAAK,aAAe;UAClB/B,SAAW;UACXF,OAAA,EAASlD,KAAM,GACZH,IAAK,CAAAwB,SAAA,CAAUY,KAAK,EACpBtB,GAAI,CAAAyB,GAAA,GAAM,CAAI,GAAAG,MAAA,EAAQ,MAAM;QAAA,CAChC;MAAA;IACH,CACF;IAEAmD,KAAA,OAAAjE,KAAA,CAAAC,IAAA;MACE,IAAAK,EAAM,EAAM4D,EAAA;MACZ,IAAY,CAAA5D,EAAA,GAAAb,QAAA,CAAAe,KAAA,qBAAAF,EAAA,CAAA6D,QAAA,CAAAC,QAAA,CAAAC,aAAA;QACV,MAAaC,QAAA;QACX,CAAAJ,EAAA,GAAAvE,cAAe,CAAAa,KAAA,qBAAA0D,EAAA,CAAA/B,KAAA;MACf;IAA4B,CAC9B;IACFoC,MAAA;MACFpC;IAEA,CAAa;IAAA,QAAAqC,IAAA,EAAAC,MAAA;MAAA,OAAAC,SAAA,IAAAC,kBAAA;QAAAC,IAAA;QAIX,cAAAC,KAAA,CAAAvF,CAAA;QACDwF,KAAA,EAAAC,cAAA,CAAAF,KAAA,CAAAzF,EAAA,EAAA4F,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}