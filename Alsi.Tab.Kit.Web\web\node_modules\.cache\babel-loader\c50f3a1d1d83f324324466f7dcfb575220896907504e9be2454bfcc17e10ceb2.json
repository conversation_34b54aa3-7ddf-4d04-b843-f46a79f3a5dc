{"ast": null, "code": "import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string` to\n * [kebab case](https://en.wikipedia.org/wiki/Letter_case#Special_case_styles).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the kebab cased string.\n * @example\n *\n * _.kebabCase('Foo Bar');\n * // => 'foo-bar'\n *\n * _.kebabCase('fooBar');\n * // => 'foo-bar'\n *\n * _.kebabCase('__FOO_BAR__');\n * // => 'foo-bar'\n */\nvar kebabCase = createCompounder(function (result, word, index) {\n  return result + (index ? '-' : '') + word.toLowerCase();\n});\nexport default kebabCase;", "map": {"version": 3, "names": ["createCompounder", "kebabCase", "result", "word", "index", "toLowerCase"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/kebabCase.js"], "sourcesContent": ["import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string` to\n * [kebab case](https://en.wikipedia.org/wiki/Letter_case#Special_case_styles).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the kebab cased string.\n * @example\n *\n * _.kebabCase('Foo Bar');\n * // => 'foo-bar'\n *\n * _.kebabCase('fooBar');\n * // => 'foo-bar'\n *\n * _.kebabCase('__FOO_BAR__');\n * // => 'foo-bar'\n */\nvar kebabCase = createCompounder(function(result, word, index) {\n  return result + (index ? '-' : '') + word.toLowerCase();\n});\n\nexport default kebabCase;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGD,gBAAgB,CAAC,UAASE,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC7D,OAAOF,MAAM,IAAIE,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}