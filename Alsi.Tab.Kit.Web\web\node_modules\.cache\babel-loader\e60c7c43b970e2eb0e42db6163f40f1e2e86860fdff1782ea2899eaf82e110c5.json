{"ast": null, "code": "import { defineComponent, computed, ref, openBlock, createElement<PERSON><PERSON>, normalizeClass, unref, normalizeStyle, renderSlot, createBlock, resolveDynamicComponent, createCommentVNode, createElementVNode } from 'vue';\nimport { <PERSON>Left, ArrowUp, ArrowRight, ArrowDown } from '@element-plus/icons-vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSplitterBar\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    index: {\n      type: Number,\n      required: true\n    },\n    layout: {\n      type: String,\n      values: [\"horizontal\", \"vertical\"],\n      default: \"horizontal\"\n    },\n    resizable: {\n      type: Boolean,\n      default: true\n    },\n    startCollapsible: {\n      type: <PERSON>olean\n    },\n    endCollapsible: {\n      type: <PERSON><PERSON><PERSON>\n    }\n  },\n  emits: [\"moveStart\", \"moving\", \"moveEnd\", \"collapse\"],\n  setup(__props, {\n    emit\n  }) {\n    const props = __props;\n    const ns = useNamespace(\"splitter-bar\");\n    const isHorizontal = computed(() => props.layout === \"horizontal\");\n    const barWrapStyles = computed(() => {\n      if (isHorizontal.value) {\n        return {\n          width: 0\n        };\n      }\n      return {\n        height: 0\n      };\n    });\n    const draggerStyles = computed(() => {\n      return {\n        width: isHorizontal.value ? \"16px\" : \"100%\",\n        height: isHorizontal.value ? \"100%\" : \"16px\",\n        cursor: isHorizontal.value ? \"col-resize\" : \"row-resize\",\n        touchAction: \"none\"\n      };\n    });\n    const draggerPseudoClass = computed(() => {\n      const prefix = ns.e(\"dragger\");\n      let className = isHorizontal.value ? `${prefix}-horizontal` : `${prefix}-vertical`;\n      if (startPos.value) className += ` ${prefix}-active`;\n      return className;\n    });\n    const startPos = ref(null);\n    const onMousedown = e => {\n      if (!props.resizable) return;\n      startPos.value = [e.pageX, e.pageY];\n      emit(\"moveStart\", props.index);\n      window.addEventListener(\"mouseup\", onMouseUp);\n      window.addEventListener(\"mousemove\", onMouseMove);\n    };\n    const onTouchStart = e => {\n      if (props.resizable && e.touches.length === 1) {\n        e.preventDefault();\n        const touch = e.touches[0];\n        startPos.value = [touch.pageX, touch.pageY];\n        emit(\"moveStart\", props.index);\n        window.addEventListener(\"touchend\", onTouchEnd);\n        window.addEventListener(\"touchmove\", onTouchMove);\n      }\n    };\n    const onMouseMove = e => {\n      const {\n        pageX,\n        pageY\n      } = e;\n      const offsetX = pageX - startPos.value[0];\n      const offsetY = pageY - startPos.value[1];\n      const offset = isHorizontal.value ? offsetX : offsetY;\n      emit(\"moving\", props.index, offset);\n    };\n    const onTouchMove = e => {\n      if (e.touches.length === 1) {\n        e.preventDefault();\n        const touch = e.touches[0];\n        const offsetX = touch.pageX - startPos.value[0];\n        const offsetY = touch.pageY - startPos.value[1];\n        const offset = isHorizontal.value ? offsetX : offsetY;\n        emit(\"moving\", props.index, offset);\n      }\n    };\n    const onMouseUp = () => {\n      startPos.value = null;\n      window.removeEventListener(\"mouseup\", onMouseUp);\n      window.removeEventListener(\"mousemove\", onMouseMove);\n      emit(\"moveEnd\", props.index);\n    };\n    const onTouchEnd = () => {\n      startPos.value = null;\n      window.removeEventListener(\"touchend\", onTouchEnd);\n      window.removeEventListener(\"touchmove\", onTouchMove);\n      emit(\"moveEnd\", props.index);\n    };\n    const StartIcon = computed(() => isHorizontal.value ? ArrowLeft : ArrowUp);\n    const EndIcon = computed(() => isHorizontal.value ? ArrowRight : ArrowDown);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).b()]),\n        style: normalizeStyle(unref(barWrapStyles))\n      }, [__props.startCollapsible ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass([unref(ns).e(\"collapse-icon\"), unref(ns).e(`${__props.layout}-collapse-icon-start`)]),\n        onClick: $event => emit(\"collapse\", __props.index, \"start\")\n      }, [renderSlot(_ctx.$slots, \"start-collapsible\", {}, () => [(openBlock(), createBlock(resolveDynamicComponent(unref(StartIcon)), {\n        style: {\n          \"width\": \"12px\",\n          \"height\": \"12px\"\n        }\n      }))])], 10, [\"onClick\"])) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"dragger\"), unref(draggerPseudoClass), __props.resizable ? \"\" : unref(ns).e(\"disable\")]),\n        style: normalizeStyle(unref(draggerStyles)),\n        onMousedown,\n        onTouchstart: onTouchStart\n      }, null, 38), __props.endCollapsible ? (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass([unref(ns).e(\"collapse-icon\"), unref(ns).e(`${__props.layout}-collapse-icon-end`)]),\n        onClick: $event => emit(\"collapse\", __props.index, \"end\")\n      }, [renderSlot(_ctx.$slots, \"end-collapsible\", {}, () => [(openBlock(), createBlock(resolveDynamicComponent(unref(EndIcon)), {\n        style: {\n          \"width\": \"12px\",\n          \"height\": \"12px\"\n        }\n      }))])], 10, [\"onClick\"])) : createCommentVNode(\"v-if\", true)], 6);\n    };\n  }\n});\nvar SplitBar = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"split-bar.vue\"]]);\nexport { SplitBar as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "isHorizontal", "computed", "props", "layout", "barWrapStyles", "value", "width", "height", "dragger<PERSON><PERSON>les", "cursor", "touchAction", "dragger<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix", "e", "className", "startPos", "ref", "onMousedown", "resizable", "pageX", "pageY", "emit", "index", "window", "addEventListener", "onMouseUp", "onMouseMove", "onTouchStart", "touches", "length", "preventDefault", "touch", "onTouchEnd", "onTouchMove", "offsetX", "offsetY", "offset", "removeEventListener"], "sources": ["../../../../../../packages/components/splitter/src/split-bar.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { computed, ref } from 'vue'\nimport {\n  ArrowDown,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUp,\n} from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nconst ns = useNamespace('splitter-bar')\n\ndefineOptions({\n  name: 'ElSplitterBar',\n})\n\nconst props = defineProps({\n  index: {\n    type: Number,\n    required: true,\n  },\n  layout: {\n    type: String,\n    values: ['horizontal', 'vertical'] as const,\n    default: 'horizontal',\n  },\n  resizable: {\n    type: Boolean,\n    default: true,\n  },\n  startCollapsible: {\n    type: Boolean,\n  },\n  endCollapsible: {\n    type: <PERSON><PERSON>an,\n  },\n})\n\nconst emit = defineEmits(['moveStart', 'moving', 'moveEnd', 'collapse'])\n\nconst isHorizontal = computed(() => props.layout === 'horizontal')\n\nconst barWrapStyles = computed(() => {\n  if (isHorizontal.value) {\n    return { width: 0 }\n  }\n  return { height: 0 }\n})\n\nconst draggerStyles = computed(() => {\n  return {\n    width: isHorizontal.value ? '16px' : '100%',\n    height: isHorizontal.value ? '100%' : '16px',\n    cursor: isHorizontal.value ? 'col-resize' : 'row-resize',\n    touchAction: 'none',\n  }\n})\n\nconst draggerPseudoClass = computed(() => {\n  const prefix = ns.e('dragger')\n  let className = isHorizontal.value\n    ? `${prefix}-horizontal`\n    : `${prefix}-vertical`\n  if (startPos.value) className += ` ${prefix}-active`\n  return className\n})\n\nconst startPos = ref<[x: number, y: number] | null>(null)\n\n// Start dragging\nconst onMousedown = (e: MouseEvent) => {\n  if (!props.resizable) return\n  startPos.value = [e.pageX, e.pageY]\n  emit('moveStart', props.index)\n  window.addEventListener('mouseup', onMouseUp)\n  window.addEventListener('mousemove', onMouseMove)\n}\n\nconst onTouchStart = (e: TouchEvent) => {\n  if (props.resizable && e.touches.length === 1) {\n    e.preventDefault()\n    const touch = e.touches[0]\n    startPos.value = [touch.pageX, touch.pageY]\n    emit('moveStart', props.index)\n    window.addEventListener('touchend', onTouchEnd)\n    window.addEventListener('touchmove', onTouchMove)\n  }\n}\n\n// During dragging\nconst onMouseMove = (e: MouseEvent) => {\n  const { pageX, pageY } = e\n  const offsetX = pageX - startPos.value![0]\n  const offsetY = pageY - startPos.value![1]\n  const offset = isHorizontal.value ? offsetX : offsetY\n  emit('moving', props.index, offset)\n}\n\nconst onTouchMove = (e: TouchEvent) => {\n  if (e.touches.length === 1) {\n    e.preventDefault()\n    const touch = e.touches[0]\n    const offsetX = touch.pageX - startPos.value![0]\n    const offsetY = touch.pageY - startPos.value![1]\n    const offset = isHorizontal.value ? offsetX : offsetY\n    emit('moving', props.index, offset)\n  }\n}\n\n// End dragging\nconst onMouseUp = () => {\n  startPos.value = null\n  window.removeEventListener('mouseup', onMouseUp)\n  window.removeEventListener('mousemove', onMouseMove)\n  emit('moveEnd', props.index)\n}\n\nconst onTouchEnd = () => {\n  startPos.value = null\n  window.removeEventListener('touchend', onTouchEnd)\n  window.removeEventListener('touchmove', onTouchMove)\n  emit('moveEnd', props.index)\n}\n\nconst StartIcon = computed(() => (isHorizontal.value ? ArrowLeft : ArrowUp))\nconst EndIcon = computed(() => (isHorizontal.value ? ArrowRight : ArrowDown))\n</script>\n\n<template>\n  <div :class=\"[ns.b()]\" :style=\"barWrapStyles\">\n    <div\n      v-if=\"startCollapsible\"\n      :class=\"[ns.e('collapse-icon'), ns.e(`${layout}-collapse-icon-start`)]\"\n      @click=\"emit('collapse', index, 'start')\"\n    >\n      <slot name=\"start-collapsible\">\n        <component :is=\"StartIcon\" style=\"width: 12px; height: 12px\" />\n      </slot>\n    </div>\n\n    <div\n      :class=\"[\n        ns.e('dragger'),\n        draggerPseudoClass,\n        resizable ? '' : ns.e('disable'),\n      ]\"\n      :style=\"draggerStyles\"\n      @mousedown=\"onMousedown\"\n      @touchstart=\"onTouchStart\"\n    />\n    <div\n      v-if=\"endCollapsible\"\n      :class=\"[ns.e('collapse-icon'), ns.e(`${layout}-collapse-icon-end`)]\"\n      @click=\"emit('collapse', index, 'end')\"\n    >\n      <slot name=\"end-collapsible\">\n        <component :is=\"EndIcon\" style=\"width: 12px; height: 12px\" />\n      </slot>\n    </div>\n  </div>\n</template>\n"], "mappings": ";;;;mCAYc;EACZA,IAAM;AACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAJM,MAAAC,EAAA,GAAKC,YAAA,CAAa,cAAc;IA8BtC,MAAMC,YAAe,GAAAC,QAAA,CAAS,MAAMC,KAAA,CAAMC,MAAA,KAAW,YAAY;IAE3D,MAAAC,aAAA,GAAgBH,QAAA,CAAS,MAAM;MACnC,IAAID,YAAA,CAAaK,KAAO;QACf;UAAEC,KAAA,EAAO;QAAE;MAAA;MAEb;QAAEC,MAAA,EAAQ;MAAE;IAAA,CACpB;IAEK,MAAAC,aAAA,GAAgBP,QAAA,CAAS,MAAM;MAC5B;QACLK,KAAA,EAAON,YAAa,CAAAK,KAAA,GAAQ,MAAS;QACrCE,MAAA,EAAQP,YAAa,CAAAK,KAAA,GAAQ,MAAS;QACtCI,MAAA,EAAQT,YAAa,CAAAK,KAAA,GAAQ,YAAe;QAC5CK,WAAa;MAAA,CACf;IAAA,CACD;IAEK,MAAAC,kBAAA,GAAqBV,QAAA,CAAS,MAAM;MAClC,MAAAW,MAAA,GAASd,EAAG,CAAAe,CAAA,CAAE,SAAS;MAC7B,IAAIC,SAAA,GAAYd,YAAa,CAAAK,KAAA,GACzB,GAAGO,MAAM,gBACT,GAAGA,MAAM;MACb,IAAIG,QAAS,CAAAV,KAAA,EACNS,SAAA,QAAAF,MAAA;MACR,OAAAE,SAAA;IAED,CAAM;IAGA,MAAAC,QAAA,GAAAC,GAAc,CAAC,IAAkB;IACjC,MAAAC,WAAkB,GAAAJ,CAAA;MACtB,KAAAX,KAAiB,CAAAgB,SAAG,EACf;MACEH,QAAA,CAAAV,KAAA,IAAAQ,CAAA,CAAAM,KAAA,EAAAN,CAAA,CAAAO,KAAA,CAA4B;MAC5BC,IAAA,cAAAnB,KAAA,CAAiBoB,KAAA;MAC1BC,MAAA,CAAAC,gBAAA,YAAAC,SAAA;MAEMF,MAAA,CAAAC,gBAAkC,cAAAE,WAAA;IACtC;IACE,MAAEC,YAAe,GAAAd,CAAA;MACX,IAAAX,KAAA,CAAAgB,SAAU,IAAAL,CAAA,CAAAe,OAAS,CAAAC,MAAA;QACzBhB,CAAA,CAAAiB,cAAiB,EAAC;QACb,MAAAC,KAAA,GAAAlB,CAAA,CAAAe,OAAA,CAAmB,CAAK;QACtBb,QAAA,CAAAV,KAAA,IAAA0B,KAAA,CAAiBZ,KAAA,EAAAY,KAAY,CAAUX,KAAA;QACvCC,IAAA,cAAAnB,KAAA,CAAiBoB,KAAA;QAC1BC,MAAA,CAAAC,gBAAA,aAAAQ,UAAA;QACFT,MAAA,CAAAC,gBAAA,cAAAS,WAAA;MAGA;IACE,CAAM;IACN,MAAAP,WAAgB,GAAAb,CAAA,IAAiB;MACjC,MAAM;QAAUM,KAAA;QAAAC;MAAA,IAAiBP,CAAA;MAC3B,MAAAqB,OAAA,GAAsBf,KAAA,GAAAJ,QAAA,CAAAV,KAAkB;MACzC,MAAA8B,OAAA,GAAgBf,KAAA,GAAAL,QAAa,CAAAV,KAAA;MACpC,MAAA+B,MAAA,GAAApC,YAAA,CAAAK,KAAA,GAAA6B,OAAA,GAAAC,OAAA;MAEMd,IAAA,WAAAnB,KAAiC,CAAAoB,KAAA,EAAAc,MAAA;IACrC,CAAI;IACF,MAAEH,WAAe,GAAApB,CAAA;MACX,IAAAA,CAAA,CAAAe,OAAA,CAAAC,MAAU,MAAQ,EAAC;QACzBhB,CAAA,CAAAiB,cAAgB;QAChB,MAAMC,KAAU,GAAAlB,CAAA,CAAAe,OAAc;QACxB,MAAAM,OAAA,GAAsBH,KAAA,CAAAZ,KAAA,GAAAJ,QAAkB,CAAAV,KAAA;QACzC,MAAA8B,OAAA,GAAgBJ,KAAA,CAAAX,KAAA,GAAaL,QAAA,CAAAV,KAAA;QACpC,MAAA+B,MAAA,GAAApC,YAAA,CAAAK,KAAA,GAAA6B,OAAA,GAAAC,OAAA;QACFd,IAAA,WAAAnB,KAAA,CAAAoB,KAAA,EAAAc,MAAA;MAGA;IACE;IACO,MAAAX,SAAA,GAAAA,CAAA;MACAV,QAAA,CAAAV,KAAA;MACFkB,MAAA,CAAAc,mBAAsB,YAAAZ,SAAA;MAC7BF,MAAA,CAAAc,mBAAA,cAAAX,WAAA;MAEAL,IAAM,YAAAnB,KAAmB,CAAAoB,KAAA;IACvB;IACO,MAAAU,UAAA,GAAAA,CAAA;MACAjB,QAAA,CAAAV,KAAA;MACFkB,MAAA,CAAAc,mBAAsB,aAAAL,UAAA;MAC7BT,MAAA,CAAAc,mBAAA,cAAAJ,WAAA;MAEAZ,IAAM,YAAYnB,KAAS,CAAAoB,KAAA;IAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}