{"ast": null, "code": "import baseDifference from './_baseDifference.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.difference` except that it accepts `comparator`\n * which is invoked to compare elements of `array` to `values`. The order and\n * references of result values are determined by the first array. The comparator\n * is invoked with two arguments: (arrVal, othVal).\n *\n * **Note:** Unlike `_.pullAllWith`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...Array} [values] The values to exclude.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n *\n * _.differenceWith(objects, [{ 'x': 1, 'y': 2 }], _.isEqual);\n * // => [{ 'x': 2, 'y': 1 }]\n */\nvar differenceWith = baseRest(function (array, values) {\n  var comparator = last(values);\n  if (isArrayLikeObject(comparator)) {\n    comparator = undefined;\n  }\n  return isArrayLikeObject(array) ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true), undefined, comparator) : [];\n});\nexport default differenceWith;", "map": {"version": 3, "names": ["baseDifference", "baseFlatten", "baseRest", "isArrayLikeObject", "last", "differenceWith", "array", "values", "comparator", "undefined"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/differenceWith.js"], "sourcesContent": ["import baseDifference from './_baseDifference.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseRest from './_baseRest.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.difference` except that it accepts `comparator`\n * which is invoked to compare elements of `array` to `values`. The order and\n * references of result values are determined by the first array. The comparator\n * is invoked with two arguments: (arrVal, othVal).\n *\n * **Note:** Unlike `_.pullAllWith`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...Array} [values] The values to exclude.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n *\n * _.differenceWith(objects, [{ 'x': 1, 'y': 2 }], _.isEqual);\n * // => [{ 'x': 2, 'y': 1 }]\n */\nvar differenceWith = baseRest(function(array, values) {\n  var comparator = last(values);\n  if (isArrayLikeObject(comparator)) {\n    comparator = undefined;\n  }\n  return isArrayLikeObject(array)\n    ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true), undefined, comparator)\n    : [];\n});\n\nexport default differenceWith;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAGH,QAAQ,CAAC,UAASI,KAAK,EAAEC,MAAM,EAAE;EACpD,IAAIC,UAAU,GAAGJ,IAAI,CAACG,MAAM,CAAC;EAC7B,IAAIJ,iBAAiB,CAACK,UAAU,CAAC,EAAE;IACjCA,UAAU,GAAGC,SAAS;EACxB;EACA,OAAON,iBAAiB,CAACG,KAAK,CAAC,GAC3BN,cAAc,CAACM,KAAK,EAAEL,WAAW,CAACM,MAAM,EAAE,CAAC,EAAEJ,iBAAiB,EAAE,IAAI,CAAC,EAAEM,SAAS,EAAED,UAAU,CAAC,GAC7F,EAAE;AACR,CAAC,CAAC;AAEF,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}