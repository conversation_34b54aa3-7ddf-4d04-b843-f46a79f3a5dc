{"ast": null, "code": "import { inject, computed } from 'vue';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { isUndefined } from '../../../../utils/types.mjs';\nimport { useFormDisabled } from '../../../form/src/hooks/use-form-common-props.mjs';\nconst useCheckboxDisabled = ({\n  model,\n  isChecked\n}) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const isLimitDisabled = computed(() => {\n    var _a, _b;\n    const max = (_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value;\n    const min = (_b = checkboxGroup == null ? void 0 : checkboxGroup.min) == null ? void 0 : _b.value;\n    return !isUndefined(max) && model.value.length >= max && !isChecked.value || !isUndefined(min) && model.value.length <= min && isChecked.value;\n  });\n  const isDisabled = useFormDisabled(computed(() => (checkboxGroup == null ? void 0 : checkboxGroup.disabled.value) || isLimitDisabled.value));\n  return {\n    isDisabled,\n    isLimitDisabled\n  };\n};\nexport { useCheckboxDisabled };", "map": {"version": 3, "names": ["useCheckboxDisabled", "model", "isChecked", "checkboxGroup", "inject", "checkboxGroupContextKey", "isLimitDisabled", "computed", "_a", "_b", "max", "value", "min", "isUndefined", "length", "isDisabled", "useFormDisabled", "disabled"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-disabled.ts"], "sourcesContent": ["import { computed, inject } from 'vue'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { isUndefined } from '@element-plus/utils'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { CheckboxModel, CheckboxStatus } from '../composables'\n\nexport const useCheckboxDisabled = ({\n  model,\n  isChecked,\n}: Pick<CheckboxModel, 'model'> & Pick<CheckboxStatus, 'isChecked'>) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n\n  const isLimitDisabled = computed(() => {\n    const max = checkboxGroup?.max?.value\n    const min = checkboxGroup?.min?.value\n    return (\n      (!isUndefined(max) && model.value.length >= max && !isChecked.value) ||\n      (!isUndefined(min) && model.value.length <= min && isChecked.value)\n    )\n  })\n\n  const isDisabled = useFormDisabled(\n    computed(() => checkboxGroup?.disabled.value || isLimitDisabled.value)\n  )\n\n  return {\n    isDisabled,\n    isLimitDisabled,\n  }\n}\n\nexport type CheckboxDisabled = ReturnType<typeof useCheckboxDisabled>\n"], "mappings": ";;;;AAIY,MAACA,mBAAmB,GAAGA,CAAC;EAClCC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,aAAa,GAAGC,MAAM,CAACC,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAC7D,MAAMC,eAAe,GAAGC,QAAQ,CAAC,MAAM;IACrC,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,GAAG,GAAG,CAACF,EAAE,GAAGL,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,KAAK;IACjG,MAAMC,GAAG,GAAG,CAACH,EAAE,GAAGN,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACS,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACE,KAAK;IACjG,OAAO,CAACE,WAAW,CAACH,GAAG,CAAC,IAAIT,KAAK,CAACU,KAAK,CAACG,MAAM,IAAIJ,GAAG,IAAI,CAACR,SAAS,CAACS,KAAK,IAAI,CAACE,WAAW,CAACD,GAAG,CAAC,IAAIX,KAAK,CAACU,KAAK,CAACG,MAAM,IAAIF,GAAG,IAAIV,SAAS,CAACS,KAAK;EAClJ,CAAG,CAAC;EACF,MAAMI,UAAU,GAAGC,eAAe,CAACT,QAAQ,CAAC,MAAM,CAACJ,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACc,QAAQ,CAACN,KAAK,KAAKL,eAAe,CAACK,KAAK,CAAC,CAAC;EAC5I,OAAO;IACLI,UAAU;IACVT;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}