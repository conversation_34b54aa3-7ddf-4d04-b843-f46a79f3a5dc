{"ast": null, "code": "import { defineComponent, inject, h } from 'vue';\nimport { ElText } from '../../text/index.mjs';\nimport { treeNodeContentProps, ROOT_TREE_INJECTION_KEY } from './virtual-tree.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nvar ElNodeContent = defineComponent({\n  name: \"ElTreeNodeContent\",\n  props: treeNodeContentProps,\n  setup(props) {\n    const tree = inject(ROOT_TREE_INJECTION_KEY);\n    const ns = useNamespace(\"tree\");\n    return () => {\n      const node = props.node;\n      const {\n        data\n      } = node;\n      return (tree == null ? void 0 : tree.ctx.slots.default) ? tree.ctx.slots.default({\n        node,\n        data\n      }) : h(ElText, {\n        tag: \"span\",\n        truncated: true,\n        class: ns.be(\"node\", \"label\")\n      }, () => [node == null ? void 0 : node.label]);\n    };\n  }\n});\nexport { ElNodeContent as default };", "map": {"version": 3, "names": ["ElNodeContent", "defineComponent", "name", "props", "treeNodeContentProps", "setup", "tree", "inject", "ROOT_TREE_INJECTION_KEY", "ns", "useNamespace", "node", "data", "ctx", "slots", "default", "h", "ElText", "tag", "truncated", "class", "be", "label"], "sources": ["../../../../../../packages/components/tree-v2/src/tree-node-content.ts"], "sourcesContent": ["import { defineComponent, h, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport ElText from '@element-plus/components/text'\nimport { ROOT_TREE_INJECTION_KEY, treeNodeContentProps } from './virtual-tree'\n\nexport default defineComponent({\n  name: 'ElTreeNodeContent',\n  props: treeNodeContentProps,\n  setup(props) {\n    const tree = inject(ROOT_TREE_INJECTION_KEY)\n    const ns = useNamespace('tree')\n    return () => {\n      const node = props.node\n      const { data } = node!\n      return tree?.ctx.slots.default\n        ? tree.ctx.slots.default({ node, data })\n        : h(\n            ElText,\n            { tag: 'span', truncated: true, class: ns.be('node', 'label') },\n            () => [node?.label]\n          )\n    }\n  },\n})\n"], "mappings": ";;;;AAIA,IAAAA,aAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAEC,oBAAoB;EAC3BC,KAAKA,CAACF,KAAK,EAAE;IACX,MAAMG,IAAI,GAAGC,MAAM,CAACC,uBAAuB,CAAC;IAC5C,MAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM,CAAC;IAC/B,OAAO,MAAM;MACX,MAAMC,IAAI,GAAGR,KAAK,CAACQ,IAAI;MACvB,MAAM;QAAEC;MAAI,CAAE,GAAGD,IAAI;MACrB,OAAO,CAACL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACO,GAAG,CAACC,KAAK,CAACC,OAAO,IAAIT,IAAI,CAACO,GAAG,CAACC,KAAK,CAACC,OAAO,CAAC;QAAEJ,IAAI;QAAEC;MAAI,CAAE,CAAC,GAAGI,CAAC,CAACC,MAAM,EAAE;QAAEC,GAAG,EAAE,MAAM;QAAEC,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAEX,EAAE,CAACY,EAAE,CAAC,MAAM,EAAE,OAAO;MAAC,CAAE,EAAE,MAAM,CAACV,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACW,KAAK,CAAC,CAAC;IAC/N,CAAK;EACL;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}