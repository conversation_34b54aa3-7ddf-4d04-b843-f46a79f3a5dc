{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { inject, getCurrentInstance, computed, watch, nextTick } from 'vue';\nimport { checkboxGroupContextKey } from '../constants.mjs';\nimport { useFormItem } from '../../../form/src/hooks/use-form-item.mjs';\nimport { debugWarn } from '../../../../utils/error.mjs';\nimport { CHANGE_EVENT } from '../../../../constants/event.mjs';\nconst useCheckboxEvent = (props, {\n  model,\n  isLimitExceeded,\n  hasOwnLabel,\n  isDisabled,\n  isLabeledByFormItem\n}) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, void 0);\n  const {\n    formItem\n  } = useFormItem();\n  const {\n    emit\n  } = getCurrentInstance();\n  function getLabeledValue(value) {\n    var _a, _b, _c, _d;\n    return [true, props.trueValue, props.trueLabel].includes(value) ? (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true : (_d = (_c = props.falseValue) != null ? _c : props.falseLabel) != null ? _d : false;\n  }\n  function emitChangeEvent(checked, e) {\n    emit(CHANGE_EVENT, getLabeledValue(checked), e);\n  }\n  function handleChange(e) {\n    if (isLimitExceeded.value) return;\n    const target = e.target;\n    emit(CHANGE_EVENT, getLabeledValue(target.checked), e);\n  }\n  async function onClickRoot(e) {\n    if (isLimitExceeded.value) return;\n    if (!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value) {\n      const eventTargets = e.composedPath();\n      const hasLabel = eventTargets.some(item => item.tagName === \"LABEL\");\n      if (!hasLabel) {\n        model.value = getLabeledValue([false, props.falseValue, props.falseLabel].includes(model.value));\n        await nextTick();\n        emitChangeEvent(model.value, e);\n      }\n    }\n  }\n  const validateEvent = computed(() => (checkboxGroup == null ? void 0 : checkboxGroup.validateEvent) || props.validateEvent);\n  watch(() => props.modelValue, () => {\n    if (validateEvent.value) {\n      formItem == null ? void 0 : formItem.validate(\"change\").catch(err => debugWarn(err));\n    }\n  });\n  return {\n    handleChange,\n    onClickRoot\n  };\n};\nexport { useCheckboxEvent };", "map": {"version": 3, "names": ["useCheckboxEvent", "props", "model", "isLimitExceeded", "hasOwnLabel", "isDisabled", "isLabeledByFormItem", "checkboxGroup", "inject", "checkboxGroupContextKey", "formItem", "useFormItem", "emit", "getCurrentInstance", "getLabeledValue", "value", "_a", "_b", "_c", "_d", "trueValue", "<PERSON><PERSON><PERSON><PERSON>", "includes", "falseValue", "<PERSON><PERSON><PERSON><PERSON>", "emitChangeEvent", "checked", "e", "CHANGE_EVENT", "handleChange", "target", "onClickRoot", "eventTargets", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "some", "item", "tagName", "nextTick", "validateEvent", "computed", "watch", "modelValue", "validate", "catch", "err", "debugWarn"], "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-event.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, nextTick, watch } from 'vue'\nimport { useFormItem } from '@element-plus/components/form'\nimport { debugWarn } from '@element-plus/utils'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { useFormItemInputId } from '@element-plus/components/form'\nimport type { CheckboxProps } from '../checkbox'\nimport type {\n  CheckboxDisabled,\n  CheckboxModel,\n  CheckboxStatus,\n} from '../composables'\n\nexport const useCheckboxEvent = (\n  props: CheckboxProps,\n  {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem,\n  }: Pick<CheckboxModel, 'model' | 'isLimitExceeded'> &\n    Pick<CheckboxStatus, 'hasOwnLabel'> &\n    Pick<CheckboxDisabled, 'isDisabled'> &\n    Pick<ReturnType<typeof useFormItemInputId>, 'isLabeledByFormItem'>\n) => {\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const { formItem } = useFormItem()\n  const { emit } = getCurrentInstance()!\n\n  function getLabeledValue(value: string | number | boolean) {\n    return [true, props.trueValue, props.trueLabel].includes(value)\n      ? props.trueValue ?? props.trueLabel ?? true\n      : props.falseValue ?? props.falseLabel ?? false\n  }\n\n  function emitChangeEvent(\n    checked: string | number | boolean,\n    e: InputEvent | MouseEvent\n  ) {\n    emit(CHANGE_EVENT, getLabeledValue(checked), e)\n  }\n\n  function handleChange(e: Event) {\n    if (isLimitExceeded.value) return\n\n    const target = e.target as HTMLInputElement\n    emit(CHANGE_EVENT, getLabeledValue(target.checked), e)\n  }\n\n  async function onClickRoot(e: MouseEvent) {\n    if (isLimitExceeded.value) return\n\n    if (!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value) {\n      // fix: https://github.com/element-plus/element-plus/issues/9981\n      const eventTargets: EventTarget[] = e.composedPath()\n      const hasLabel = eventTargets.some(\n        (item) => (item as HTMLElement).tagName === 'LABEL'\n      )\n      if (!hasLabel) {\n        model.value = getLabeledValue(\n          [false, props.falseValue, props.falseLabel].includes(model.value)\n        )\n        await nextTick()\n        emitChangeEvent(model.value, e)\n      }\n    }\n  }\n\n  const validateEvent = computed(\n    () => checkboxGroup?.validateEvent || props.validateEvent\n  )\n\n  watch(\n    () => props.modelValue,\n    () => {\n      if (validateEvent.value) {\n        formItem?.validate('change').catch((err) => debugWarn(err))\n      }\n    }\n  )\n\n  return {\n    handleChange,\n    onClickRoot,\n  }\n}\n"], "mappings": ";;;;;;;AAKY,MAACA,gBAAgB,GAAGA,CAACC,KAAK,EAAE;EACtCC,KAAK;EACLC,eAAe;EACfC,WAAW;EACXC,UAAU;EACVC;AACF,CAAC,KAAK;EACJ,MAAMC,aAAa,GAAGC,MAAM,CAACC,uBAAuB,EAAE,KAAK,CAAC,CAAC;EAC7D,MAAM;IAAEC;EAAQ,CAAE,GAAGC,WAAW,EAAE;EAClC,MAAM;IAAEC;EAAI,CAAE,GAAGC,kBAAkB,EAAE;EACrC,SAASC,eAAeA,CAACC,KAAK,EAAE;IAC9B,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB,OAAO,CAAC,IAAI,EAAElB,KAAK,CAACmB,SAAS,EAAEnB,KAAK,CAACoB,SAAS,CAAC,CAACC,QAAQ,CAACP,KAAK,CAAC,GAAG,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGf,KAAK,CAACmB,SAAS,KAAK,IAAI,GAAGJ,EAAE,GAAGf,KAAK,CAACoB,SAAS,KAAK,IAAI,GAAGJ,EAAE,GAAG,IAAI,GAAG,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGjB,KAAK,CAACsB,UAAU,KAAK,IAAI,GAAGL,EAAE,GAAGjB,KAAK,CAACuB,UAAU,KAAK,IAAI,GAAGL,EAAE,GAAG,KAAK;EAC5O;EACE,SAASM,eAAeA,CAACC,OAAO,EAAEC,CAAC,EAAE;IACnCf,IAAI,CAACgB,YAAY,EAAEd,eAAe,CAACY,OAAO,CAAC,EAAEC,CAAC,CAAC;EACnD;EACE,SAASE,YAAYA,CAACF,CAAC,EAAE;IACvB,IAAIxB,eAAe,CAACY,KAAK,EACvB;IACF,MAAMe,MAAM,GAAGH,CAAC,CAACG,MAAM;IACvBlB,IAAI,CAACgB,YAAY,EAAEd,eAAe,CAACgB,MAAM,CAACJ,OAAO,CAAC,EAAEC,CAAC,CAAC;EAC1D;EACE,eAAeI,WAAWA,CAACJ,CAAC,EAAE;IAC5B,IAAIxB,eAAe,CAACY,KAAK,EACvB;IACF,IAAI,CAACX,WAAW,CAACW,KAAK,IAAI,CAACV,UAAU,CAACU,KAAK,IAAIT,mBAAmB,CAACS,KAAK,EAAE;MACxE,MAAMiB,YAAY,GAAGL,CAAC,CAACM,YAAY,EAAE;MACrC,MAAMC,QAAQ,GAAGF,YAAY,CAACG,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,OAAO,KAAK,OAAO,CAAC;MACtE,IAAI,CAACH,QAAQ,EAAE;QACbhC,KAAK,CAACa,KAAK,GAAGD,eAAe,CAAC,CAAC,KAAK,EAAEb,KAAK,CAACsB,UAAU,EAAEtB,KAAK,CAACuB,UAAU,CAAC,CAACF,QAAQ,CAACpB,KAAK,CAACa,KAAK,CAAC,CAAC;QAChG,MAAMuB,QAAQ,EAAE;QAChBb,eAAe,CAACvB,KAAK,CAACa,KAAK,EAAEY,CAAC,CAAC;MACvC;IACA;EACA;EACE,MAAMY,aAAa,GAAGC,QAAQ,CAAC,MAAM,CAACjC,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgC,aAAa,KAAKtC,KAAK,CAACsC,aAAa,CAAC;EAC3HE,KAAK,CAAC,MAAMxC,KAAK,CAACyC,UAAU,EAAE,MAAM;IAClC,IAAIH,aAAa,CAACxB,KAAK,EAAE;MACvBL,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACiC,QAAQ,CAAC,QAAQ,CAAC,CAACC,KAAK,CAAEC,GAAG,IAAKC,SAAS,CAACD,GAAG,CAAC,CAAC;IAC5F;EACA,CAAG,CAAC;EACF,OAAO;IACLhB,YAAY;IACZE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}