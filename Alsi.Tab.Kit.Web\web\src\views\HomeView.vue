<template>
  <div class="home">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>Alsi.Tab.Kit 工具集合</h1>
      <p>TabKit 中集成了多种实用工具</p>
    </div>

    <!-- 功能卡片网格 -->
    <div class="tools-grid">
      <!-- Log转换工具卡片 -->
      <el-card class="tool-card" shadow="hover" @click="navigateToTool('/log-converter')">
        <div class="tool-icon">
          <font-awesome-icon icon="exchange-alt" />
        </div>
        <h3>Log 转换工具</h3>
        <p>支持多种日志格式之间的转换，包括 ASC、BLF 等格式。提供批量处理和文件分割功能。</p>
        <div class="tool-features">
          <el-tag size="small">格式转换</el-tag>
          <el-tag size="small" type="success">批量处理</el-tag>
          <el-tag size="small" type="info">文件分割</el-tag>
        </div>
      </el-card>

      <!-- Log查看工具卡片 -->
      <el-card class="tool-card" shadow="hover" @click="navigateToTool('/log-viewer')">
        <div class="tool-icon">
          <font-awesome-icon icon="file-alt" />
        </div>
        <h3>Log 查看工具</h3>
        <p>强大的日志文件查看器，支持大文件快速加载、搜索过滤和数据分析功能。</p>
        <div class="tool-features">
          <el-tag size="small">快速查看</el-tag>
          <el-tag size="small" type="success">搜索过滤</el-tag>
          <el-tag size="small" type="warning">数据分析</el-tag>
        </div>
      </el-card>

      <!-- 更多工具卡片（预留） -->
      <el-card class="tool-card coming-soon" shadow="hover">
        <div class="tool-icon">
          <font-awesome-icon icon="cogs" />
        </div>
        <h3>更多工具</h3>
        <p>更多实用工具正在开发中，敬请期待...</p>
        <div class="tool-features">
          <el-tag size="small" type="info">即将推出</el-tag>
        </div>
      </el-card>
    </div>


  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { useRouter } from "vue-router";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export default defineComponent({
  name: "HomeView",
  components: {
    FontAwesomeIcon,
  },
  setup() {
    const router = useRouter();

    const navigateToTool = (path: string) => {
      router.push(path);
    };

    return {
      navigateToTool,
    };
  },
});
</script>

<style scoped>
.home {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: var(--el-text-color-primary);
  margin-bottom: 10px;
}

.page-header p {
  font-size: 1.1rem;
  color: var(--el-text-color-regular);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.tool-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tool-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tool-card.coming-soon {
  opacity: 0.7;
  cursor: not-allowed;
}

.tool-card.coming-soon:hover {
  transform: none;
  box-shadow: none;
}

.tool-icon {
  font-size: 2.2rem;
  color: var(--el-color-primary);
  margin-bottom: 20px;
}

.tool-card h3 {
  font-size: 1.5rem;
  color: var(--el-text-color-primary);
  margin-bottom: 15px;
}

.tool-card p {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.tool-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}



@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 2rem;
  }
}
</style>
