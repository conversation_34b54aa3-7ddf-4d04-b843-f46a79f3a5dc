{"ast": null, "code": "import arrayEvery from './_arrayEvery.js';\nimport baseEvery from './_baseEvery.js';\nimport baseIteratee from './_baseIteratee.js';\nimport isArray from './isArray.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Checks if `predicate` returns truthy for **all** elements of `collection`.\n * Iteration is stopped once `predicate` returns falsey. The predicate is\n * invoked with three arguments: (value, index|key, collection).\n *\n * **Note:** This method returns `true` for\n * [empty collections](https://en.wikipedia.org/wiki/Empty_set) because\n * [everything is true](https://en.wikipedia.org/wiki/Vacuous_truth) of\n * elements of empty collections.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`.\n * @example\n *\n * _.every([true, 1, null, 'yes'], Boolean);\n * // => false\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': false },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * // The `_.matches` iteratee shorthand.\n * _.every(users, { 'user': 'barney', 'active': false });\n * // => false\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.every(users, ['active', false]);\n * // => true\n *\n * // The `_.property` iteratee shorthand.\n * _.every(users, 'active');\n * // => false\n */\nfunction every(collection, predicate, guard) {\n  var func = isArray(collection) ? arrayEvery : baseEvery;\n  if (guard && isIterateeCall(collection, predicate, guard)) {\n    predicate = undefined;\n  }\n  return func(collection, baseIteratee(predicate, 3));\n}\nexport default every;", "map": {"version": 3, "names": ["arrayEvery", "baseEvery", "baseIteratee", "isArray", "isIterateeCall", "every", "collection", "predicate", "guard", "func", "undefined"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/every.js"], "sourcesContent": ["import arrayEvery from './_arrayEvery.js';\nimport baseEvery from './_baseEvery.js';\nimport baseIteratee from './_baseIteratee.js';\nimport isArray from './isArray.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Checks if `predicate` returns truthy for **all** elements of `collection`.\n * Iteration is stopped once `predicate` returns falsey. The predicate is\n * invoked with three arguments: (value, index|key, collection).\n *\n * **Note:** This method returns `true` for\n * [empty collections](https://en.wikipedia.org/wiki/Empty_set) because\n * [everything is true](https://en.wikipedia.org/wiki/Vacuous_truth) of\n * elements of empty collections.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`.\n * @example\n *\n * _.every([true, 1, null, 'yes'], Boolean);\n * // => false\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36, 'active': false },\n *   { 'user': 'fred',   'age': 40, 'active': false }\n * ];\n *\n * // The `_.matches` iteratee shorthand.\n * _.every(users, { 'user': 'barney', 'active': false });\n * // => false\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.every(users, ['active', false]);\n * // => true\n *\n * // The `_.property` iteratee shorthand.\n * _.every(users, 'active');\n * // => false\n */\nfunction every(collection, predicate, guard) {\n  var func = isArray(collection) ? arrayEvery : baseEvery;\n  if (guard && isIterateeCall(collection, predicate, guard)) {\n    predicate = undefined;\n  }\n  return func(collection, baseIteratee(predicate, 3));\n}\n\nexport default every;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC3C,IAAIC,IAAI,GAAGN,OAAO,CAACG,UAAU,CAAC,GAAGN,UAAU,GAAGC,SAAS;EACvD,IAAIO,KAAK,IAAIJ,cAAc,CAACE,UAAU,EAAEC,SAAS,EAAEC,KAAK,CAAC,EAAE;IACzDD,SAAS,GAAGG,SAAS;EACvB;EACA,OAAOD,IAAI,CAACH,UAAU,EAAEJ,YAAY,CAACK,SAAS,EAAE,CAAC,CAAC,CAAC;AACrD;AAEA,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}