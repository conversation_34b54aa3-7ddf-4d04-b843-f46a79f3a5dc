{"ast": null, "code": "import baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * Creates a function that invokes the method at `path` of a given object.\n * Any additional arguments are provided to the invoked method.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Util\n * @param {Array|string} path The path of the method to invoke.\n * @param {...*} [args] The arguments to invoke the method with.\n * @returns {Function} Returns the new invoker function.\n * @example\n *\n * var objects = [\n *   { 'a': { 'b': _.constant(2) } },\n *   { 'a': { 'b': _.constant(1) } }\n * ];\n *\n * _.map(objects, _.method('a.b'));\n * // => [2, 1]\n *\n * _.map(objects, _.method(['a', 'b']));\n * // => [2, 1]\n */\nvar method = baseRest(function (path, args) {\n  return function (object) {\n    return baseInvoke(object, path, args);\n  };\n});\nexport default method;", "map": {"version": 3, "names": ["baseInvoke", "baseRest", "method", "path", "args", "object"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/method.js"], "sourcesContent": ["import baseInvoke from './_baseInvoke.js';\nimport baseRest from './_baseRest.js';\n\n/**\n * Creates a function that invokes the method at `path` of a given object.\n * Any additional arguments are provided to the invoked method.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Util\n * @param {Array|string} path The path of the method to invoke.\n * @param {...*} [args] The arguments to invoke the method with.\n * @returns {Function} Returns the new invoker function.\n * @example\n *\n * var objects = [\n *   { 'a': { 'b': _.constant(2) } },\n *   { 'a': { 'b': _.constant(1) } }\n * ];\n *\n * _.map(objects, _.method('a.b'));\n * // => [2, 1]\n *\n * _.map(objects, _.method(['a', 'b']));\n * // => [2, 1]\n */\nvar method = baseRest(function(path, args) {\n  return function(object) {\n    return baseInvoke(object, path, args);\n  };\n});\n\nexport default method;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAGD,QAAQ,CAAC,UAASE,IAAI,EAAEC,IAAI,EAAE;EACzC,OAAO,UAASC,MAAM,EAAE;IACtB,OAAOL,UAAU,CAACK,MAAM,EAAEF,IAAI,EAAEC,IAAI,CAAC;EACvC,CAAC;AACH,CAAC,CAAC;AAEF,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}