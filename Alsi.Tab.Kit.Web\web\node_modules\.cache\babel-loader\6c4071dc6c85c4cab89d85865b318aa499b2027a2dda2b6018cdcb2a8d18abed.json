{"ast": null, "code": "import { defineComponent, inject, computed, openBlock, createElementBlock, normalizeStyle, unref, normalizeClass } from 'vue';\nimport { tooltipV2RootKey, tooltipV2ContentKey } from './constants.mjs';\nimport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './arrow.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTooltipV2Arrow\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    ...tooltipV2ArrowProps,\n    ...tooltipV2ArrowSpecialProps\n  },\n  setup(__props) {\n    const props = __props;\n    const {\n      ns\n    } = inject(tooltipV2RootKey);\n    const {\n      arrowRef\n    } = inject(tooltipV2ContentKey);\n    const arrowStyle = computed(() => {\n      const {\n        style,\n        width,\n        height\n      } = props;\n      const namespace = ns.namespace.value;\n      return {\n        [`--${namespace}-tooltip-v2-arrow-width`]: `${width}px`,\n        [`--${namespace}-tooltip-v2-arrow-height`]: `${height}px`,\n        [`--${namespace}-tooltip-v2-arrow-border-width`]: `${width / 2}px`,\n        [`--${namespace}-tooltip-v2-arrow-cover-width`]: width / 2 - 1,\n        ...(style || {})\n      };\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"span\", {\n        ref_key: \"arrowRef\",\n        ref: arrowRef,\n        style: normalizeStyle(unref(arrowStyle)),\n        class: normalizeClass(unref(ns).e(\"arrow\"))\n      }, null, 6);\n    };\n  }\n});\nvar TooltipV2Arrow = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"arrow.vue\"]]);\nexport { TooltipV2Arrow as default };", "map": {"version": 3, "names": ["name", "ns", "inject", "tooltipV2RootKey", "arrowRef", "tooltipV2ContentKey", "arrowStyle", "computed", "style", "width", "height", "props", "namespace", "value"], "sources": ["../../../../../../packages/components/tooltip-v2/src/arrow.vue"], "sourcesContent": ["<template>\n  <span ref=\"arrowRef\" :style=\"arrowStyle\" :class=\"ns.e('arrow')\" />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject } from 'vue'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './arrow'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElTooltipV2Arrow',\n})\n\nconst props = defineProps({\n  ...tooltipV2ArrowProps,\n  ...tooltipV2ArrowSpecialProps,\n})\n\nconst { ns } = inject(tooltipV2RootKey)!\nconst { arrowRef } = inject(tooltipV2ContentKey)!\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  const { style, width, height } = props\n  const namespace = ns.namespace.value\n\n  return {\n    [`--${namespace}-tooltip-v2-arrow-width`]: `${width}px`,\n    [`--${namespace}-tooltip-v2-arrow-height`]: `${height}px`,\n    [`--${namespace}-tooltip-v2-arrow-border-width`]: `${width / 2}px`,\n    [`--${namespace}-tooltip-v2-arrow-cover-width`]: width / 2 - 1,\n    ...(style || {}),\n  }\n})\n</script>\n"], "mappings": ";;;;mCAWc;EACZA,IAAM;AACR;;;;;;;;;IAOA,MAAM;MAAEC;IAAA,CAAO,GAAAC,MAAA,CAAOC,gBAAgB;IACtC,MAAM;MAAEC;IAAA,CAAa,GAAAF,MAAA,CAAOG,mBAAmB;IAEzC,MAAAC,UAAA,GAAaC,QAAA,CAAwB,MAAM;MAC/C,MAAM;QAAEC,KAAA;QAAOC,KAAO;QAAAC;MAAA,CAAW,GAAAC,KAAA;MAC3B,MAAAC,SAAA,GAAYX,EAAA,CAAGW,SAAU,CAAAC,KAAA;MAExB;QACL,CAAC,KAAKD,SAAS,yBAAyB,GAAG,GAAGH,KAAK;QACnD,CAAC,KAAKG,SAAS,0BAA0B,GAAG,GAAGF,MAAM;QACrD,CAAC,KAAKE,SAAS,gCAAgC,GAAG,GAAGH,KAAA,GAAQ,CAAC;QAC9D,CAAC,KAAKG,SAAS,+BAA+B,GAAGH,KAAA,GAAQ,CAAI;QAC7D,IAAID,KAAA,IAAS,EAAC;MAAA,CAChB;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}