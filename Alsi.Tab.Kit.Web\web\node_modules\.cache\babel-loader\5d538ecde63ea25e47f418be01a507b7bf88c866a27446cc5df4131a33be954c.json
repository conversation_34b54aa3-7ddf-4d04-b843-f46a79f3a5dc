{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, getCurrentInstance, watch, nextTick } from 'vue';\nimport { NODE_CHECK, NODE_CHECK_CHANGE, SetOperationEnum } from '../virtual-tree.mjs';\nfunction useCheck(props, tree) {\n  const checkedKeys = ref(/* @__PURE__ */new Set());\n  const indeterminateKeys = ref(/* @__PURE__ */new Set());\n  const {\n    emit\n  } = getCurrentInstance();\n  watch([() => tree.value, () => props.defaultCheckedKeys], () => {\n    return nextTick(() => {\n      _setCheckedKeys(props.defaultCheckedKeys);\n    });\n  }, {\n    immediate: true\n  });\n  const updateCheckedKeys = () => {\n    if (!tree.value || !props.showCheckbox || props.checkStrictly) {\n      return;\n    }\n    const {\n      levelTreeNodeMap,\n      maxLevel\n    } = tree.value;\n    const checkedKeySet = checkedKeys.value;\n    const indeterminateKeySet = /* @__PURE__ */new Set();\n    for (let level = maxLevel - 1; level >= 1; --level) {\n      const nodes = levelTreeNodeMap.get(level);\n      if (!nodes) continue;\n      nodes.forEach(node => {\n        const children = node.children;\n        if (children) {\n          let allChecked = true;\n          let hasChecked = false;\n          for (const childNode of children) {\n            const key = childNode.key;\n            if (checkedKeySet.has(key)) {\n              hasChecked = true;\n            } else if (indeterminateKeySet.has(key)) {\n              allChecked = false;\n              hasChecked = true;\n              break;\n            } else {\n              allChecked = false;\n            }\n          }\n          if (allChecked) {\n            checkedKeySet.add(node.key);\n          } else if (hasChecked) {\n            indeterminateKeySet.add(node.key);\n            checkedKeySet.delete(node.key);\n          } else {\n            checkedKeySet.delete(node.key);\n            indeterminateKeySet.delete(node.key);\n          }\n        }\n      });\n    }\n    indeterminateKeys.value = indeterminateKeySet;\n  };\n  const isChecked = node => checkedKeys.value.has(node.key);\n  const isIndeterminate = node => indeterminateKeys.value.has(node.key);\n  const toggleCheckbox = (node, isChecked2, nodeClick = true, immediateUpdate = true) => {\n    const checkedKeySet = checkedKeys.value;\n    const toggle = (node2, checked) => {\n      checkedKeySet[checked ? SetOperationEnum.ADD : SetOperationEnum.DELETE](node2.key);\n      const children = node2.children;\n      if (!props.checkStrictly && children) {\n        children.forEach(childNode => {\n          if (!childNode.disabled) {\n            toggle(childNode, checked);\n          }\n        });\n      }\n    };\n    toggle(node, isChecked2);\n    if (immediateUpdate) {\n      updateCheckedKeys();\n    }\n    if (nodeClick) {\n      afterNodeCheck(node, isChecked2);\n    }\n  };\n  const afterNodeCheck = (node, checked) => {\n    const {\n      checkedNodes,\n      checkedKeys: checkedKeys2\n    } = getChecked();\n    const {\n      halfCheckedNodes,\n      halfCheckedKeys\n    } = getHalfChecked();\n    emit(NODE_CHECK, node.data, {\n      checkedKeys: checkedKeys2,\n      checkedNodes,\n      halfCheckedKeys,\n      halfCheckedNodes\n    });\n    emit(NODE_CHECK_CHANGE, node.data, checked);\n  };\n  function getCheckedKeys(leafOnly = false) {\n    return getChecked(leafOnly).checkedKeys;\n  }\n  function getCheckedNodes(leafOnly = false) {\n    return getChecked(leafOnly).checkedNodes;\n  }\n  function getHalfCheckedKeys() {\n    return getHalfChecked().halfCheckedKeys;\n  }\n  function getHalfCheckedNodes() {\n    return getHalfChecked().halfCheckedNodes;\n  }\n  function getChecked(leafOnly = false) {\n    const checkedNodes = [];\n    const keys = [];\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      checkedKeys.value.forEach(key => {\n        const node = treeNodeMap.get(key);\n        if (node && (!leafOnly || leafOnly && node.isLeaf)) {\n          keys.push(key);\n          checkedNodes.push(node.data);\n        }\n      });\n    }\n    return {\n      checkedKeys: keys,\n      checkedNodes\n    };\n  }\n  function getHalfChecked() {\n    const halfCheckedNodes = [];\n    const halfCheckedKeys = [];\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      indeterminateKeys.value.forEach(key => {\n        const node = treeNodeMap.get(key);\n        if (node) {\n          halfCheckedKeys.push(key);\n          halfCheckedNodes.push(node.data);\n        }\n      });\n    }\n    return {\n      halfCheckedNodes,\n      halfCheckedKeys\n    };\n  }\n  function setCheckedKeys(keys) {\n    checkedKeys.value.clear();\n    indeterminateKeys.value.clear();\n    nextTick(() => {\n      _setCheckedKeys(keys);\n    });\n  }\n  function setChecked(key, isChecked2) {\n    if ((tree == null ? void 0 : tree.value) && props.showCheckbox) {\n      const node = tree.value.treeNodeMap.get(key);\n      if (node) {\n        toggleCheckbox(node, isChecked2, false);\n      }\n    }\n  }\n  function _setCheckedKeys(keys) {\n    if (tree == null ? void 0 : tree.value) {\n      const {\n        treeNodeMap\n      } = tree.value;\n      if (props.showCheckbox && treeNodeMap && (keys == null ? void 0 : keys.length) > 0) {\n        for (const key of keys) {\n          const node = treeNodeMap.get(key);\n          if (node && !isChecked(node)) {\n            toggleCheckbox(node, true, false, false);\n          }\n        }\n        updateCheckedKeys();\n      }\n    }\n  }\n  return {\n    updateCheckedKeys,\n    toggleCheckbox,\n    isChecked,\n    isIndeterminate,\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys\n  };\n}\nexport { useCheck };", "map": {"version": 3, "names": ["useCheck", "props", "tree", "checked<PERSON>eys", "ref", "Set", "indeterminateKeys", "emit", "getCurrentInstance", "watch", "value", "defaultCheckedKeys", "nextTick", "_setChe<PERSON><PERSON><PERSON>s", "immediate", "updateCheckedKeys", "showCheckbox", "checkStrictly", "levelTreeNodeMap", "maxLevel", "checkedKeySet", "indeterminateKeySet", "level", "nodes", "get", "for<PERSON>ach", "node", "children", "allChecked", "hasChecked", "childNode", "key", "has", "add", "delete", "isChecked", "isIndeterminate", "toggleCheckbox", "isChecked2", "nodeClick", "immediateUpdate", "toggle", "node2", "checked", "SetOperationEnum", "ADD", "DELETE", "disabled", "after<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkedNodes", "checkedKeys2", "getChecked", "halfCheckedNodes", "halfC<PERSON>cked<PERSON>eys", "getHalfChecked", "NODE_CHECK", "data", "NODE_CHECK_CHANGE", "getChe<PERSON><PERSON>eys", "leafOnly", "getCheckedNodes", "getHalfCheckedKeys", "getHalfCheckedNodes", "keys", "treeNodeMap", "<PERSON><PERSON><PERSON><PERSON>", "push", "set<PERSON><PERSON><PERSON><PERSON>eys", "clear", "setChecked", "length"], "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useCheck.ts"], "sourcesContent": ["import { getCurrentInstance, nextTick, ref, watch } from 'vue'\nimport {\n  NODE_CHECK,\n  NODE_CHECK_CHANGE,\n  SetOperationEnum,\n} from '../virtual-tree'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { Ref } from 'vue'\nimport type { Tree, TreeKey, TreeNode, TreeNodeData, TreeProps } from '../types'\n\nexport function useCheck(props: TreeProps, tree: Ref<Tree | undefined>) {\n  const checkedKeys = ref<Set<TreeKey>>(new Set())\n  const indeterminateKeys = ref<Set<TreeKey>>(new Set())\n  const { emit } = getCurrentInstance()!\n\n  watch(\n    [() => tree.value, () => props.defaultCheckedKeys],\n    () => {\n      return nextTick(() => {\n        _setCheckedKeys(props.defaultCheckedKeys)\n      })\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  const updateCheckedKeys = () => {\n    if (!tree.value || !props.showCheckbox || props.checkStrictly) {\n      return\n    }\n    const { levelTreeNodeMap, maxLevel } = tree.value\n    const checkedKeySet = checkedKeys.value\n    const indeterminateKeySet = new Set<TreeKey>()\n    // It is easier to determine the indeterminate state by\n    // traversing from bottom to top\n    // leaf nodes not have indeterminate status and can be skipped\n    for (let level = maxLevel - 1; level >= 1; --level) {\n      const nodes = levelTreeNodeMap.get(level)\n      if (!nodes) continue\n      nodes.forEach((node) => {\n        const children = node.children\n        if (children) {\n          // Whether all child nodes are selected\n          let allChecked = true\n          // Whether a child node is selected\n          let hasChecked = false\n          for (const childNode of children) {\n            const key = childNode.key\n            if (checkedKeySet.has(key)) {\n              hasChecked = true\n            } else if (indeterminateKeySet.has(key)) {\n              allChecked = false\n              hasChecked = true\n              break\n            } else {\n              allChecked = false\n            }\n          }\n          if (allChecked) {\n            checkedKeySet.add(node.key)\n          } else if (hasChecked) {\n            indeterminateKeySet.add(node.key)\n            checkedKeySet.delete(node.key)\n          } else {\n            checkedKeySet.delete(node.key)\n            indeterminateKeySet.delete(node.key)\n          }\n        }\n      })\n    }\n    indeterminateKeys.value = indeterminateKeySet\n  }\n\n  const isChecked = (node: TreeNode) => checkedKeys.value.has(node.key)\n\n  const isIndeterminate = (node: TreeNode) =>\n    indeterminateKeys.value.has(node.key)\n\n  const toggleCheckbox = (\n    node: TreeNode,\n    isChecked: CheckboxValueType,\n    nodeClick = true,\n    immediateUpdate = true\n  ) => {\n    const checkedKeySet = checkedKeys.value\n    const toggle = (node: TreeNode, checked: CheckboxValueType) => {\n      checkedKeySet[checked ? SetOperationEnum.ADD : SetOperationEnum.DELETE](\n        node.key\n      )\n      const children = node.children\n      if (!props.checkStrictly && children) {\n        children.forEach((childNode) => {\n          if (!childNode.disabled) {\n            toggle(childNode, checked)\n          }\n        })\n      }\n    }\n    toggle(node, isChecked)\n    if (immediateUpdate) {\n      updateCheckedKeys()\n    }\n    if (nodeClick) {\n      afterNodeCheck(node, isChecked)\n    }\n  }\n\n  const afterNodeCheck = (node: TreeNode, checked: CheckboxValueType) => {\n    const { checkedNodes, checkedKeys } = getChecked()\n    const { halfCheckedNodes, halfCheckedKeys } = getHalfChecked()\n    emit(NODE_CHECK, node.data, {\n      checkedKeys,\n      checkedNodes,\n      halfCheckedKeys,\n      halfCheckedNodes,\n    })\n    emit(NODE_CHECK_CHANGE, node.data, checked)\n  }\n\n  // expose\n  function getCheckedKeys(leafOnly = false): TreeKey[] {\n    return getChecked(leafOnly).checkedKeys\n  }\n\n  function getCheckedNodes(leafOnly = false): TreeNodeData[] {\n    return getChecked(leafOnly).checkedNodes\n  }\n\n  function getHalfCheckedKeys(): TreeKey[] {\n    return getHalfChecked().halfCheckedKeys\n  }\n\n  function getHalfCheckedNodes(): TreeNodeData[] {\n    return getHalfChecked().halfCheckedNodes\n  }\n\n  function getChecked(leafOnly = false): {\n    checkedKeys: TreeKey[]\n    checkedNodes: TreeNodeData[]\n  } {\n    const checkedNodes: TreeNodeData[] = []\n    const keys: TreeKey[] = []\n    if (tree?.value && props.showCheckbox) {\n      const { treeNodeMap } = tree.value\n      checkedKeys.value.forEach((key) => {\n        const node = treeNodeMap.get(key)\n        if (node && (!leafOnly || (leafOnly && node.isLeaf))) {\n          keys.push(key)\n          checkedNodes.push(node.data)\n        }\n      })\n    }\n    return {\n      checkedKeys: keys,\n      checkedNodes,\n    }\n  }\n\n  function getHalfChecked(): {\n    halfCheckedKeys: TreeKey[]\n    halfCheckedNodes: TreeNodeData[]\n  } {\n    const halfCheckedNodes: TreeNodeData[] = []\n    const halfCheckedKeys: TreeKey[] = []\n    if (tree?.value && props.showCheckbox) {\n      const { treeNodeMap } = tree.value\n      indeterminateKeys.value.forEach((key) => {\n        const node = treeNodeMap.get(key)\n        if (node) {\n          halfCheckedKeys.push(key)\n          halfCheckedNodes.push(node.data)\n        }\n      })\n    }\n    return {\n      halfCheckedNodes,\n      halfCheckedKeys,\n    }\n  }\n\n  function setCheckedKeys(keys: TreeKey[]) {\n    checkedKeys.value.clear()\n    indeterminateKeys.value.clear()\n    nextTick(() => {\n      _setCheckedKeys(keys)\n    })\n  }\n\n  function setChecked(key: TreeKey, isChecked: boolean) {\n    if (tree?.value && props.showCheckbox) {\n      const node = tree.value.treeNodeMap.get(key)\n      if (node) {\n        toggleCheckbox(node, isChecked, false)\n      }\n    }\n  }\n\n  function _setCheckedKeys(keys: TreeKey[]) {\n    if (tree?.value) {\n      const { treeNodeMap } = tree.value\n      if (props.showCheckbox && treeNodeMap && keys?.length > 0) {\n        for (const key of keys) {\n          const node = treeNodeMap.get(key)\n          if (node && !isChecked(node)) {\n            toggleCheckbox(node, true, false, false)\n          }\n        }\n        updateCheckedKeys()\n      }\n    }\n  }\n\n  return {\n    updateCheckedKeys,\n    toggleCheckbox,\n    isChecked,\n    isIndeterminate,\n    // expose\n    getCheckedKeys,\n    getCheckedNodes,\n    getHalfCheckedKeys,\n    getHalfCheckedNodes,\n    setChecked,\n    setCheckedKeys,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAMO,SAASA,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACpC,MAAMC,WAAW,GAAGC,GAAG,gBAAiB,IAAIC,GAAG,EAAE,CAAC;EAClD,MAAMC,iBAAiB,GAAGF,GAAG,gBAAiB,IAAIC,GAAG,EAAE,CAAC;EACxD,MAAM;IAAEE;EAAI,CAAE,GAAGC,kBAAkB,EAAE;EACrCC,KAAK,CAAC,CAAC,MAAMP,IAAI,CAACQ,KAAK,EAAE,MAAMT,KAAK,CAACU,kBAAkB,CAAC,EAAE,MAAM;IAC9D,OAAOC,QAAQ,CAAC,MAAM;MACpBC,eAAe,CAACZ,KAAK,CAACU,kBAAkB,CAAC;IAC/C,CAAK,CAAC;EACN,CAAG,EAAE;IACDG,SAAS,EAAE;EACf,CAAG,CAAC;EACF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACb,IAAI,CAACQ,KAAK,IAAI,CAACT,KAAK,CAACe,YAAY,IAAIf,KAAK,CAACgB,aAAa,EAAE;MAC7D;IACN;IACI,MAAM;MAAEC,gBAAgB;MAAEC;IAAQ,CAAE,GAAGjB,IAAI,CAACQ,KAAK;IACjD,MAAMU,aAAa,GAAGjB,WAAW,CAACO,KAAK;IACvC,MAAMW,mBAAmB,kBAAmB,IAAIhB,GAAG,EAAE;IACrD,KAAK,IAAIiB,KAAK,GAAGH,QAAQ,GAAG,CAAC,EAAEG,KAAK,IAAI,CAAC,EAAE,EAAEA,KAAK,EAAE;MAClD,MAAMC,KAAK,GAAGL,gBAAgB,CAACM,GAAG,CAACF,KAAK,CAAC;MACzC,IAAI,CAACC,KAAK,EACR;MACFA,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAK;QACtB,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;QAC9B,IAAIA,QAAQ,EAAE;UACZ,IAAIC,UAAU,GAAG,IAAI;UACrB,IAAIC,UAAU,GAAG,KAAK;UACtB,KAAK,MAAMC,SAAS,IAAIH,QAAQ,EAAE;YAChC,MAAMI,GAAG,GAAGD,SAAS,CAACC,GAAG;YACzB,IAAIX,aAAa,CAACY,GAAG,CAACD,GAAG,CAAC,EAAE;cAC1BF,UAAU,GAAG,IAAI;YAC/B,CAAa,MAAM,IAAIR,mBAAmB,CAACW,GAAG,CAACD,GAAG,CAAC,EAAE;cACvCH,UAAU,GAAG,KAAK;cAClBC,UAAU,GAAG,IAAI;cACjB;YACd,CAAa,MAAM;cACLD,UAAU,GAAG,KAAK;YAChC;UACA;UACU,IAAIA,UAAU,EAAE;YACdR,aAAa,CAACa,GAAG,CAACP,IAAI,CAACK,GAAG,CAAC;UACvC,CAAW,MAAM,IAAIF,UAAU,EAAE;YACrBR,mBAAmB,CAACY,GAAG,CAACP,IAAI,CAACK,GAAG,CAAC;YACjCX,aAAa,CAACc,MAAM,CAACR,IAAI,CAACK,GAAG,CAAC;UAC1C,CAAW,MAAM;YACLX,aAAa,CAACc,MAAM,CAACR,IAAI,CAACK,GAAG,CAAC;YAC9BV,mBAAmB,CAACa,MAAM,CAACR,IAAI,CAACK,GAAG,CAAC;UAChD;QACA;MACA,CAAO,CAAC;IACR;IACIzB,iBAAiB,CAACI,KAAK,GAAGW,mBAAmB;EACjD,CAAG;EACD,MAAMc,SAAS,GAAIT,IAAI,IAAKvB,WAAW,CAACO,KAAK,CAACsB,GAAG,CAACN,IAAI,CAACK,GAAG,CAAC;EAC3D,MAAMK,eAAe,GAAIV,IAAI,IAAKpB,iBAAiB,CAACI,KAAK,CAACsB,GAAG,CAACN,IAAI,CAACK,GAAG,CAAC;EACvE,MAAMM,cAAc,GAAGA,CAACX,IAAI,EAAEY,UAAU,EAAEC,SAAS,GAAG,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;IACrF,MAAMpB,aAAa,GAAGjB,WAAW,CAACO,KAAK;IACvC,MAAM+B,MAAM,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;MACjCvB,aAAa,CAACuB,OAAO,GAAGC,gBAAgB,CAACC,GAAG,GAAGD,gBAAgB,CAACE,MAAM,CAAC,CAACJ,KAAK,CAACX,GAAG,CAAC;MAClF,MAAMJ,QAAQ,GAAGe,KAAK,CAACf,QAAQ;MAC/B,IAAI,CAAC1B,KAAK,CAACgB,aAAa,IAAIU,QAAQ,EAAE;QACpCA,QAAQ,CAACF,OAAO,CAAEK,SAAS,IAAK;UAC9B,IAAI,CAACA,SAAS,CAACiB,QAAQ,EAAE;YACvBN,MAAM,CAACX,SAAS,EAAEa,OAAO,CAAC;UACtC;QACA,CAAS,CAAC;MACV;IACA,CAAK;IACDF,MAAM,CAACf,IAAI,EAAEY,UAAU,CAAC;IACxB,IAAIE,eAAe,EAAE;MACnBzB,iBAAiB,EAAE;IACzB;IACI,IAAIwB,SAAS,EAAE;MACbS,cAAc,CAACtB,IAAI,EAAEY,UAAU,CAAC;IACtC;EACA,CAAG;EACD,MAAMU,cAAc,GAAGA,CAACtB,IAAI,EAAEiB,OAAO,KAAK;IACxC,MAAM;MAAEM,YAAY;MAAE9C,WAAW,EAAE+C;IAAY,CAAE,GAAGC,UAAU,EAAE;IAChE,MAAM;MAAEC,gBAAgB;MAAEC;IAAe,CAAE,GAAGC,cAAc,EAAE;IAC9D/C,IAAI,CAACgD,UAAU,EAAE7B,IAAI,CAAC8B,IAAI,EAAE;MAC1BrD,WAAW,EAAE+C,YAAY;MACzBD,YAAY;MACZI,eAAe;MACfD;IACN,CAAK,CAAC;IACF7C,IAAI,CAACkD,iBAAiB,EAAE/B,IAAI,CAAC8B,IAAI,EAAEb,OAAO,CAAC;EAC/C,CAAG;EACD,SAASe,cAAcA,CAACC,QAAQ,GAAG,KAAK,EAAE;IACxC,OAAOR,UAAU,CAACQ,QAAQ,CAAC,CAACxD,WAAW;EAC3C;EACE,SAASyD,eAAeA,CAACD,QAAQ,GAAG,KAAK,EAAE;IACzC,OAAOR,UAAU,CAACQ,QAAQ,CAAC,CAACV,YAAY;EAC5C;EACE,SAASY,kBAAkBA,CAAA,EAAG;IAC5B,OAAOP,cAAc,EAAE,CAACD,eAAe;EAC3C;EACE,SAASS,mBAAmBA,CAAA,EAAG;IAC7B,OAAOR,cAAc,EAAE,CAACF,gBAAgB;EAC5C;EACE,SAASD,UAAUA,CAACQ,QAAQ,GAAG,KAAK,EAAE;IACpC,MAAMV,YAAY,GAAG,EAAE;IACvB,MAAMc,IAAI,GAAG,EAAE;IACf,IAAI,CAAC7D,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,KAAK,KAAKT,KAAK,CAACe,YAAY,EAAE;MAC9D,MAAM;QAAEgD;MAAW,CAAE,GAAG9D,IAAI,CAACQ,KAAK;MAClCP,WAAW,CAACO,KAAK,CAACe,OAAO,CAAEM,GAAG,IAAK;QACjC,MAAML,IAAI,GAAGsC,WAAW,CAACxC,GAAG,CAACO,GAAG,CAAC;QACjC,IAAIL,IAAI,KAAK,CAACiC,QAAQ,IAAIA,QAAQ,IAAIjC,IAAI,CAACuC,MAAM,CAAC,EAAE;UAClDF,IAAI,CAACG,IAAI,CAACnC,GAAG,CAAC;UACdkB,YAAY,CAACiB,IAAI,CAACxC,IAAI,CAAC8B,IAAI,CAAC;QACtC;MACA,CAAO,CAAC;IACR;IACI,OAAO;MACLrD,WAAW,EAAE4D,IAAI;MACjBd;IACN,CAAK;EACL;EACE,SAASK,cAAcA,CAAA,EAAG;IACxB,MAAMF,gBAAgB,GAAG,EAAE;IAC3B,MAAMC,eAAe,GAAG,EAAE;IAC1B,IAAI,CAACnD,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,KAAK,KAAKT,KAAK,CAACe,YAAY,EAAE;MAC9D,MAAM;QAAEgD;MAAW,CAAE,GAAG9D,IAAI,CAACQ,KAAK;MAClCJ,iBAAiB,CAACI,KAAK,CAACe,OAAO,CAAEM,GAAG,IAAK;QACvC,MAAML,IAAI,GAAGsC,WAAW,CAACxC,GAAG,CAACO,GAAG,CAAC;QACjC,IAAIL,IAAI,EAAE;UACR2B,eAAe,CAACa,IAAI,CAACnC,GAAG,CAAC;UACzBqB,gBAAgB,CAACc,IAAI,CAACxC,IAAI,CAAC8B,IAAI,CAAC;QAC1C;MACA,CAAO,CAAC;IACR;IACI,OAAO;MACLJ,gBAAgB;MAChBC;IACN,CAAK;EACL;EACE,SAASc,cAAcA,CAACJ,IAAI,EAAE;IAC5B5D,WAAW,CAACO,KAAK,CAAC0D,KAAK,EAAE;IACzB9D,iBAAiB,CAACI,KAAK,CAAC0D,KAAK,EAAE;IAC/BxD,QAAQ,CAAC,MAAM;MACbC,eAAe,CAACkD,IAAI,CAAC;IAC3B,CAAK,CAAC;EACN;EACE,SAASM,UAAUA,CAACtC,GAAG,EAAEO,UAAU,EAAE;IACnC,IAAI,CAACpC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,KAAK,KAAKT,KAAK,CAACe,YAAY,EAAE;MAC9D,MAAMU,IAAI,GAAGxB,IAAI,CAACQ,KAAK,CAACsD,WAAW,CAACxC,GAAG,CAACO,GAAG,CAAC;MAC5C,IAAIL,IAAI,EAAE;QACRW,cAAc,CAACX,IAAI,EAAEY,UAAU,EAAE,KAAK,CAAC;MAC/C;IACA;EACA;EACE,SAASzB,eAAeA,CAACkD,IAAI,EAAE;IAC7B,IAAI7D,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,KAAK,EAAE;MACtC,MAAM;QAAEsD;MAAW,CAAE,GAAG9D,IAAI,CAACQ,KAAK;MAClC,IAAIT,KAAK,CAACe,YAAY,IAAIgD,WAAW,IAAI,CAACD,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACO,MAAM,IAAI,CAAC,EAAE;QAClF,KAAK,MAAMvC,GAAG,IAAIgC,IAAI,EAAE;UACtB,MAAMrC,IAAI,GAAGsC,WAAW,CAACxC,GAAG,CAACO,GAAG,CAAC;UACjC,IAAIL,IAAI,IAAI,CAACS,SAAS,CAACT,IAAI,CAAC,EAAE;YAC5BW,cAAc,CAACX,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;UACpD;QACA;QACQX,iBAAiB,EAAE;MAC3B;IACA;EACA;EACE,OAAO;IACLA,iBAAiB;IACjBsB,cAAc;IACdF,SAAS;IACTC,eAAe;IACfsB,cAAc;IACdE,eAAe;IACfC,kBAAkB;IAClBC,mBAAmB;IACnBO,UAAU;IACVF;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}