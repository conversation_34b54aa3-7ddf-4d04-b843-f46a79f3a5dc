{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, normalizeStyle, toDisplayString } from 'vue';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  props: {\n    item: {\n      type: Object,\n      required: true\n    },\n    style: {\n      type: Object\n    },\n    height: Number\n  },\n  setup() {\n    const ns = useNamespace(\"select\");\n    return {\n      ns\n    };\n  }\n});\nfunction _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass(_ctx.ns.be(\"group\", \"title\")),\n    style: normalizeStyle({\n      ..._ctx.style,\n      lineHeight: `${_ctx.height}px`\n    })\n  }, toDisplayString(_ctx.item.label), 7);\n}\nvar GroupItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"render\", _sfc_render], [\"__file\", \"group-item.vue\"]]);\nexport { GroupItem as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "props", "item", "type", "Object", "required", "style", "height", "Number", "setup", "ns", "useNamespace", "openBlock", "createElementBlock", "class", "normalizeClass", "_ctx", "be", "normalizeStyle", "lineHeight", "toDisplayString", "label", "GroupItem", "_export_sfc", "_sfc_render"], "sources": ["../../../../../../packages/components/select-v2/src/group-item.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"ns.be('group', 'title')\"\n    :style=\"{ ...style, lineHeight: `${height}px` }\"\n  >\n    {{ item.label }}\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport type { CSSProperties, PropType } from 'vue'\n\nexport default defineComponent({\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    style: {\n      type: Object as PropType<CSSProperties>,\n    },\n    height: Number,\n  },\n  setup() {\n    const ns = useNamespace('select')\n    return {\n      ns,\n    }\n  },\n})\n</script>\n"], "mappings": ";;;AAcA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,KAAO;IACLC,IAAM;MACJC,IAAM,EAAAC,MAAA;MACNC,QAAU;IAAA,CACZ;IACAC,KAAO;MACLH,IAAM,EAAAC;IAAA,CACR;IACAG,MAAQ,EAAAC;EAAA,CACV;EACAC,KAAQA,CAAA;IACA,MAAAC,EAAA,GAAKC,YAAA,CAAa,QAAQ;IACzB;MACLD;IAAA,CACF;EAAA;AAEJ,CAAC;;EA9BC,OAAAE,SAAA,IAAAC,kBAAA;IAKMC,KAAA,EAAAC,cAAA,CAAAC,IAAA,CAAAN,EAAA,CAAAO,EAAA;IAAAX,KAAA,EAAAY,cAAA;MAAA,GAAAF,IAAA,CAAAV,KAAA;MAAAa,UAAA,KAAAH,IAAA,CAAAT,MAAA;IAAA;EAAA,CAJH,EAAAa,eAAO,CAAAJ,IAAA,CAAAd,IAAA,CAAAmB,KAAG,CAAE;AAAA;AAGV,IAAAC,SAAA,kBAAKC,WAAK,CAAAxB,SAAA,cAAAyB,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}