{"ast": null, "code": "function easeInOutCubic(t, b, c, d) {\n  const cc = c - b;\n  t /= d / 2;\n  if (t < 1) {\n    return cc / 2 * t * t * t + b;\n  }\n  return cc / 2 * ((t -= 2) * t * t + 2) + b;\n}\nexport { easeInOutCubic };", "map": {"version": 3, "names": ["easeInOutCubic", "t", "b", "c", "d", "cc"], "sources": ["../../../../packages/utils/easings.ts"], "sourcesContent": ["export function easeInOutCubic(t: number, b: number, c: number, d: number) {\n  const cc = c - b\n  t /= d / 2\n  if (t < 1) {\n    return (cc / 2) * t * t * t + b\n  }\n  return (cc / 2) * ((t -= 2) * t * t + 2) + b\n}\n"], "mappings": "AAAO,SAASA,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACzC,MAAMC,EAAE,GAAGF,CAAC,GAAGD,CAAC;EAChBD,CAAC,IAAIG,CAAC,GAAG,CAAC;EACV,IAAIH,CAAC,GAAG,CAAC,EAAE;IACT,OAAOI,EAAE,GAAG,CAAC,GAAGJ,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGC,CAAC;EACjC;EACE,OAAOG,EAAE,GAAG,CAAC,IAAI,CAACJ,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}