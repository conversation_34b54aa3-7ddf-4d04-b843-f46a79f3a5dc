{"ast": null, "code": "import isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Casts `value` to an empty array if it's not an array like object.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Array|Object} Returns the cast array-like object.\n */\nfunction castArrayLikeObject(value) {\n  return isArrayLikeObject(value) ? value : [];\n}\nexport default castArrayLikeObject;", "map": {"version": 3, "names": ["isArrayLikeObject", "castArrayLikeObject", "value"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_castArrayLikeObject.js"], "sourcesContent": ["import isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Casts `value` to an empty array if it's not an array like object.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Array|Object} Returns the cast array-like object.\n */\nfunction castArrayLikeObject(value) {\n  return isArrayLikeObject(value) ? value : [];\n}\n\nexport default castArrayLikeObject;\n"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,wBAAwB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,OAAOF,iBAAiB,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;AAC9C;AAEA,eAAeD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}