{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { computed, watch } from 'vue';\nimport { CHECKED_CHANGE_EVENT } from '../transfer-panel2.mjs';\nimport { usePropsAlias } from './use-props-alias.mjs';\nimport { isFunction } from '@vue/shared';\nconst useCheck = (props, panelState, emit) => {\n  const propsAlias = usePropsAlias(props);\n  const filteredData = computed(() => {\n    return props.data.filter(item => {\n      if (isFunction(props.filterMethod)) {\n        return props.filterMethod(panelState.query, item);\n      } else {\n        const label = String(item[propsAlias.value.label] || item[propsAlias.value.key]);\n        return label.toLowerCase().includes(panelState.query.toLowerCase());\n      }\n    });\n  });\n  const checkableData = computed(() => filteredData.value.filter(item => !item[propsAlias.value.disabled]));\n  const checkedSummary = computed(() => {\n    const checkedLength = panelState.checked.length;\n    const dataLength = props.data.length;\n    const {\n      noChecked,\n      hasChecked\n    } = props.format;\n    if (noChecked && hasChecked) {\n      return checkedLength > 0 ? hasChecked.replace(/\\${checked}/g, checkedLength.toString()).replace(/\\${total}/g, dataLength.toString()) : noChecked.replace(/\\${total}/g, dataLength.toString());\n    } else {\n      return `${checkedLength}/${dataLength}`;\n    }\n  });\n  const isIndeterminate = computed(() => {\n    const checkedLength = panelState.checked.length;\n    return checkedLength > 0 && checkedLength < checkableData.value.length;\n  });\n  const updateAllChecked = () => {\n    const checkableDataKeys = checkableData.value.map(item => item[propsAlias.value.key]);\n    panelState.allChecked = checkableDataKeys.length > 0 && checkableDataKeys.every(item => panelState.checked.includes(item));\n  };\n  const handleAllCheckedChange = value => {\n    panelState.checked = value ? checkableData.value.map(item => item[propsAlias.value.key]) : [];\n  };\n  watch(() => panelState.checked, (val, oldVal) => {\n    updateAllChecked();\n    if (panelState.checkChangeByUser) {\n      const movedKeys = val.concat(oldVal).filter(v => !val.includes(v) || !oldVal.includes(v));\n      emit(CHECKED_CHANGE_EVENT, val, movedKeys);\n    } else {\n      emit(CHECKED_CHANGE_EVENT, val);\n      panelState.checkChangeByUser = true;\n    }\n  });\n  watch(checkableData, () => {\n    updateAllChecked();\n  });\n  watch(() => props.data, () => {\n    const checked = [];\n    const filteredDataKeys = filteredData.value.map(item => item[propsAlias.value.key]);\n    panelState.checked.forEach(item => {\n      if (filteredDataKeys.includes(item)) {\n        checked.push(item);\n      }\n    });\n    panelState.checkChangeByUser = false;\n    panelState.checked = checked;\n  });\n  watch(() => props.defaultChecked, (val, oldVal) => {\n    if (oldVal && val.length === oldVal.length && val.every(item => oldVal.includes(item))) return;\n    const checked = [];\n    const checkableDataKeys = checkableData.value.map(item => item[propsAlias.value.key]);\n    val.forEach(item => {\n      if (checkableDataKeys.includes(item)) {\n        checked.push(item);\n      }\n    });\n    panelState.checkChangeByUser = false;\n    panelState.checked = checked;\n  }, {\n    immediate: true\n  });\n  return {\n    filteredData,\n    checkableData,\n    checkedSummary,\n    isIndeterminate,\n    updateAllChecked,\n    handleAllCheckedChange\n  };\n};\nexport { useCheck };", "map": {"version": 3, "names": ["useCheck", "props", "panelState", "emit", "props<PERSON><PERSON><PERSON>", "usePropsAlias", "filteredData", "computed", "data", "filter", "item", "isFunction", "filterMethod", "query", "label", "String", "value", "key", "toLowerCase", "includes", "checkableData", "disabled", "checkedSummary", "<PERSON><PERSON><PERSON><PERSON>", "checked", "length", "dataLength", "noChecked", "hasChecked", "format", "replace", "toString", "isIndeterminate", "updateAllChecked", "checkableDataKeys", "map", "allChecked", "every", "handleAllCheckedChange", "watch", "val", "oldVal", "checkChangeByUser", "<PERSON><PERSON><PERSON><PERSON>", "concat", "v", "CHECKED_CHANGE_EVENT", "filteredDataKeys", "for<PERSON>ach", "push", "defaultChecked", "immediate"], "sources": ["../../../../../../../packages/components/transfer/src/composables/use-check.ts"], "sourcesContent": ["import { computed, watch } from 'vue'\nimport { isFunction } from '@element-plus/utils'\nimport { CHECKED_CHANGE_EVENT } from '../transfer-panel'\nimport { usePropsAlias } from './use-props-alias'\n\nimport type { SetupContext } from 'vue'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { TransferKey } from '../transfer'\nimport type {\n  TransferPanelEmits,\n  TransferPanelProps,\n  TransferPanelState,\n} from '../transfer-panel'\n\nexport const useCheck = (\n  props: TransferPanelProps,\n  panelState: TransferPanelState,\n  emit: SetupContext<TransferPanelEmits>['emit']\n) => {\n  const propsAlias = usePropsAlias(props)\n\n  const filteredData = computed(() => {\n    return props.data.filter((item) => {\n      if (isFunction(props.filterMethod)) {\n        return props.filterMethod(panelState.query, item)\n      } else {\n        const label = String(\n          item[propsAlias.value.label] || item[propsAlias.value.key]\n        )\n        return label.toLowerCase().includes(panelState.query.toLowerCase())\n      }\n    })\n  })\n\n  const checkableData = computed(() =>\n    filteredData.value.filter((item) => !item[propsAlias.value.disabled])\n  )\n\n  const checkedSummary = computed(() => {\n    const checkedLength = panelState.checked.length\n    const dataLength = props.data.length\n    const { noChecked, hasChecked } = props.format\n\n    if (noChecked && hasChecked) {\n      return checkedLength > 0\n        ? hasChecked\n            .replace(/\\${checked}/g, checkedLength.toString())\n            .replace(/\\${total}/g, dataLength.toString())\n        : noChecked.replace(/\\${total}/g, dataLength.toString())\n    } else {\n      return `${checkedLength}/${dataLength}`\n    }\n  })\n\n  const isIndeterminate = computed(() => {\n    const checkedLength = panelState.checked.length\n    return checkedLength > 0 && checkedLength < checkableData.value.length\n  })\n\n  const updateAllChecked = () => {\n    const checkableDataKeys = checkableData.value.map(\n      (item) => item[propsAlias.value.key]\n    )\n    panelState.allChecked =\n      checkableDataKeys.length > 0 &&\n      checkableDataKeys.every((item) => panelState.checked.includes(item))\n  }\n\n  const handleAllCheckedChange = (value: CheckboxValueType) => {\n    panelState.checked = value\n      ? checkableData.value.map((item) => item[propsAlias.value.key])\n      : []\n  }\n\n  watch(\n    () => panelState.checked,\n    (val, oldVal) => {\n      updateAllChecked()\n\n      if (panelState.checkChangeByUser) {\n        const movedKeys = val\n          .concat(oldVal)\n          .filter((v) => !val.includes(v) || !oldVal.includes(v))\n        emit(CHECKED_CHANGE_EVENT, val, movedKeys)\n      } else {\n        emit(CHECKED_CHANGE_EVENT, val)\n        panelState.checkChangeByUser = true\n      }\n    }\n  )\n\n  watch(checkableData, () => {\n    updateAllChecked()\n  })\n\n  watch(\n    () => props.data,\n    () => {\n      const checked: TransferKey[] = []\n      const filteredDataKeys = filteredData.value.map(\n        (item) => item[propsAlias.value.key]\n      )\n      panelState.checked.forEach((item) => {\n        if (filteredDataKeys.includes(item)) {\n          checked.push(item)\n        }\n      })\n      panelState.checkChangeByUser = false\n      panelState.checked = checked\n    }\n  )\n\n  watch(\n    () => props.defaultChecked,\n    (val, oldVal) => {\n      if (\n        oldVal &&\n        val.length === oldVal.length &&\n        val.every((item) => oldVal.includes(item))\n      )\n        return\n\n      const checked: TransferKey[] = []\n      const checkableDataKeys = checkableData.value.map(\n        (item) => item[propsAlias.value.key]\n      )\n\n      val.forEach((item) => {\n        if (checkableDataKeys.includes(item)) {\n          checked.push(item)\n        }\n      })\n      panelState.checkChangeByUser = false\n      panelState.checked = checked\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  return {\n    filteredData,\n    checkableData,\n    checkedSummary,\n    isIndeterminate,\n    updateAllChecked,\n    handleAllCheckedChange,\n  }\n}\n"], "mappings": ";;;;;;;;;;AAIY,MAACA,QAAQ,GAAGA,CAACC,KAAK,EAAEC,UAAU,EAAEC,IAAI,KAAK;EACnD,MAAMC,UAAU,GAAGC,aAAa,CAACJ,KAAK,CAAC;EACvC,MAAMK,YAAY,GAAGC,QAAQ,CAAC,MAAM;IAClC,OAAON,KAAK,CAACO,IAAI,CAACC,MAAM,CAAEC,IAAI,IAAK;MACjC,IAAIC,UAAU,CAACV,KAAK,CAACW,YAAY,CAAC,EAAE;QAClC,OAAOX,KAAK,CAACW,YAAY,CAACV,UAAU,CAACW,KAAK,EAAEH,IAAI,CAAC;MACzD,CAAO,MAAM;QACL,MAAMI,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACN,UAAU,CAACY,KAAK,CAACF,KAAK,CAAC,IAAIJ,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC,CAAC;QAChF,OAAOH,KAAK,CAACI,WAAW,EAAE,CAACC,QAAQ,CAACjB,UAAU,CAACW,KAAK,CAACK,WAAW,EAAE,CAAC;MAC3E;IACA,CAAK,CAAC;EACN,CAAG,CAAC;EACF,MAAME,aAAa,GAAGb,QAAQ,CAAC,MAAMD,YAAY,CAACU,KAAK,CAACP,MAAM,CAAEC,IAAI,IAAK,CAACA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAC3G,MAAMC,cAAc,GAAGf,QAAQ,CAAC,MAAM;IACpC,MAAMgB,aAAa,GAAGrB,UAAU,CAACsB,OAAO,CAACC,MAAM;IAC/C,MAAMC,UAAU,GAAGzB,KAAK,CAACO,IAAI,CAACiB,MAAM;IACpC,MAAM;MAAEE,SAAS;MAAEC;IAAU,CAAE,GAAG3B,KAAK,CAAC4B,MAAM;IAC9C,IAAIF,SAAS,IAAIC,UAAU,EAAE;MAC3B,OAAOL,aAAa,GAAG,CAAC,GAAGK,UAAU,CAACE,OAAO,CAAC,cAAc,EAAEP,aAAa,CAACQ,QAAQ,EAAE,CAAC,CAACD,OAAO,CAAC,YAAY,EAAEJ,UAAU,CAACK,QAAQ,EAAE,CAAC,GAAGJ,SAAS,CAACG,OAAO,CAAC,YAAY,EAAEJ,UAAU,CAACK,QAAQ,EAAE,CAAC;IACnM,CAAK,MAAM;MACL,OAAO,GAAGR,aAAa,IAAIG,UAAU,EAAE;IAC7C;EACA,CAAG,CAAC;EACF,MAAMM,eAAe,GAAGzB,QAAQ,CAAC,MAAM;IACrC,MAAMgB,aAAa,GAAGrB,UAAU,CAACsB,OAAO,CAACC,MAAM;IAC/C,OAAOF,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAGH,aAAa,CAACJ,KAAK,CAACS,MAAM;EAC1E,CAAG,CAAC;EACF,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,iBAAiB,GAAGd,aAAa,CAACJ,KAAK,CAACmB,GAAG,CAAEzB,IAAI,IAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC,CAAC;IACvFf,UAAU,CAACkC,UAAU,GAAGF,iBAAiB,CAACT,MAAM,GAAG,CAAC,IAAIS,iBAAiB,CAACG,KAAK,CAAE3B,IAAI,IAAKR,UAAU,CAACsB,OAAO,CAACL,QAAQ,CAACT,IAAI,CAAC,CAAC;EAChI,CAAG;EACD,MAAM4B,sBAAsB,GAAItB,KAAK,IAAK;IACxCd,UAAU,CAACsB,OAAO,GAAGR,KAAK,GAAGI,aAAa,CAACJ,KAAK,CAACmB,GAAG,CAAEzB,IAAI,IAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE;EACnG,CAAG;EACDsB,KAAK,CAAC,MAAMrC,UAAU,CAACsB,OAAO,EAAE,CAACgB,GAAG,EAAEC,MAAM,KAAK;IAC/CR,gBAAgB,EAAE;IAClB,IAAI/B,UAAU,CAACwC,iBAAiB,EAAE;MAChC,MAAMC,SAAS,GAAGH,GAAG,CAACI,MAAM,CAACH,MAAM,CAAC,CAAChC,MAAM,CAAEoC,CAAC,IAAK,CAACL,GAAG,CAACrB,QAAQ,CAAC0B,CAAC,CAAC,IAAI,CAACJ,MAAM,CAACtB,QAAQ,CAAC0B,CAAC,CAAC,CAAC;MAC3F1C,IAAI,CAAC2C,oBAAoB,EAAEN,GAAG,EAAEG,SAAS,CAAC;IAChD,CAAK,MAAM;MACLxC,IAAI,CAAC2C,oBAAoB,EAAEN,GAAG,CAAC;MAC/BtC,UAAU,CAACwC,iBAAiB,GAAG,IAAI;IACzC;EACA,CAAG,CAAC;EACFH,KAAK,CAACnB,aAAa,EAAE,MAAM;IACzBa,gBAAgB,EAAE;EACtB,CAAG,CAAC;EACFM,KAAK,CAAC,MAAMtC,KAAK,CAACO,IAAI,EAAE,MAAM;IAC5B,MAAMgB,OAAO,GAAG,EAAE;IAClB,MAAMuB,gBAAgB,GAAGzC,YAAY,CAACU,KAAK,CAACmB,GAAG,CAAEzB,IAAI,IAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC,CAAC;IACrFf,UAAU,CAACsB,OAAO,CAACwB,OAAO,CAAEtC,IAAI,IAAK;MACnC,IAAIqC,gBAAgB,CAAC5B,QAAQ,CAACT,IAAI,CAAC,EAAE;QACnCc,OAAO,CAACyB,IAAI,CAACvC,IAAI,CAAC;MAC1B;IACA,CAAK,CAAC;IACFR,UAAU,CAACwC,iBAAiB,GAAG,KAAK;IACpCxC,UAAU,CAACsB,OAAO,GAAGA,OAAO;EAChC,CAAG,CAAC;EACFe,KAAK,CAAC,MAAMtC,KAAK,CAACiD,cAAc,EAAE,CAACV,GAAG,EAAEC,MAAM,KAAK;IACjD,IAAIA,MAAM,IAAID,GAAG,CAACf,MAAM,KAAKgB,MAAM,CAAChB,MAAM,IAAIe,GAAG,CAACH,KAAK,CAAE3B,IAAI,IAAK+B,MAAM,CAACtB,QAAQ,CAACT,IAAI,CAAC,CAAC,EACtF;IACF,MAAMc,OAAO,GAAG,EAAE;IAClB,MAAMU,iBAAiB,GAAGd,aAAa,CAACJ,KAAK,CAACmB,GAAG,CAAEzB,IAAI,IAAKA,IAAI,CAACN,UAAU,CAACY,KAAK,CAACC,GAAG,CAAC,CAAC;IACvFuB,GAAG,CAACQ,OAAO,CAAEtC,IAAI,IAAK;MACpB,IAAIwB,iBAAiB,CAACf,QAAQ,CAACT,IAAI,CAAC,EAAE;QACpCc,OAAO,CAACyB,IAAI,CAACvC,IAAI,CAAC;MAC1B;IACA,CAAK,CAAC;IACFR,UAAU,CAACwC,iBAAiB,GAAG,KAAK;IACpCxC,UAAU,CAACsB,OAAO,GAAGA,OAAO;EAChC,CAAG,EAAE;IACD2B,SAAS,EAAE;EACf,CAAG,CAAC;EACF,OAAO;IACL7C,YAAY;IACZc,aAAa;IACbE,cAAc;IACdU,eAAe;IACfC,gBAAgB;IAChBK;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}