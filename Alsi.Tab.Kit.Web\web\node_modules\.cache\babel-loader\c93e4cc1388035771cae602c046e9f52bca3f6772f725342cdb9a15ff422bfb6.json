{"ast": null, "code": "import { isObject } from '@vue/shared';\nfunction getCollapsible(collapsible) {\n  if (collapsible && isObject(collapsible)) {\n    return collapsible;\n  }\n  return {\n    start: !!collapsible,\n    end: !!collapsible\n  };\n}\nfunction isCollapsible(panel, size, nextPanel, nextSize) {\n  if ((panel == null ? void 0 : panel.collapsible.end) && size > 0) {\n    return true;\n  }\n  if ((nextPanel == null ? void 0 : nextPanel.collapsible.start) && nextSize === 0 && size > 0) {\n    return true;\n  }\n  return false;\n}\nexport { getCollapsible, isCollapsible };", "map": {"version": 3, "names": ["getCollapsible", "collapsible", "isObject", "start", "end", "isCollapsible", "panel", "size", "nextPanel", "nextSize"], "sources": ["../../../../../../../packages/components/splitter/src/hooks/usePanel.ts"], "sourcesContent": ["import { isObject } from '@element-plus/utils'\nimport type { PanelItemState } from '../type'\n\nexport function getCollapsible(\n  collapsible: boolean | { start?: boolean; end?: boolean }\n) {\n  if (collapsible && isObject(collapsible)) {\n    return collapsible\n  }\n  return {\n    start: !!collapsible,\n    end: !!collapsible,\n  }\n}\n\nexport function isCollapsible(\n  panel: PanelItemState | null | undefined,\n  size: number,\n  nextPanel: PanelItemState | null | undefined,\n  nextSize: number\n) {\n  // If the current panel is collapsible and has size, then it can be collapsed\n  if (panel?.collapsible.end && size > 0) {\n    return true\n  }\n\n  // If the next panel is collapsible and has no size, but the current panel has size, then it can be collapsed\n  if (nextPanel?.collapsible.start && nextSize === 0 && size > 0) {\n    return true\n  }\n\n  return false\n}\n"], "mappings": ";AACO,SAASA,cAAcA,CAACC,WAAW,EAAE;EAC1C,IAAIA,WAAW,IAAIC,QAAQ,CAACD,WAAW,CAAC,EAAE;IACxC,OAAOA,WAAW;EACtB;EACE,OAAO;IACLE,KAAK,EAAE,CAAC,CAACF,WAAW;IACpBG,GAAG,EAAE,CAAC,CAACH;EACX,CAAG;AACH;AACO,SAASI,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9D,IAAI,CAACH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACL,WAAW,CAACG,GAAG,KAAKG,IAAI,GAAG,CAAC,EAAE;IAChE,OAAO,IAAI;EACf;EACE,IAAI,CAACC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACP,WAAW,CAACE,KAAK,KAAKM,QAAQ,KAAK,CAAC,IAAIF,IAAI,GAAG,CAAC,EAAE;IAC5F,OAAO,IAAI;EACf;EACE,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}