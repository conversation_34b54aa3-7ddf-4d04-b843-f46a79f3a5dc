{"ast": null, "code": "import { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst basicMonthTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault(\"month\")\n});\nexport { basicMonthTableProps };", "map": {"version": 3, "names": ["basicMonthTableProps", "buildProps", "datePickerSharedProps", "selectionMode", "selectionModeWithDefault"], "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-month-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const basicMonthTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault('month'),\n})\n\nexport type BasicMonthTableProps = ExtractPropTypes<typeof basicMonthTableProps>\n"], "mappings": ";;AAEY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7C,GAAGC,qBAAqB;EACxBC,aAAa,EAAEC,wBAAwB,CAAC,OAAO;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}