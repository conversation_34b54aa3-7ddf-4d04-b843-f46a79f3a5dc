{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { defineComponent, inject, watch } from 'vue';\nimport { selectKey } from '../../select/src/token.mjs';\nimport { isClient } from '@vueuse/core';\nvar CacheOptions = defineComponent({\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup(props) {\n    const select = inject(selectKey);\n    watch(() => props.data, () => {\n      var _a;\n      props.data.forEach(item => {\n        if (!select.states.cachedOptions.has(item.value)) {\n          select.states.cachedOptions.set(item.value, item);\n        }\n      });\n      const inputs = ((_a = select.selectRef) == null ? void 0 : _a.querySelectorAll(\"input\")) || [];\n      if (isClient && !Array.from(inputs).includes(document.activeElement)) {\n        select.setSelected();\n      }\n    }, {\n      flush: \"post\",\n      immediate: true\n    });\n    return () => void 0;\n  }\n});\nexport { CacheOptions as default };", "map": {"version": 3, "names": ["CacheOptions", "defineComponent", "props", "data", "type", "Array", "default", "setup", "select", "inject", "<PERSON><PERSON><PERSON>", "watch", "_a", "for<PERSON>ach", "item", "states", "cachedOptions", "has", "value", "set", "inputs", "selectRef", "querySelectorAll", "isClient", "from", "includes", "document", "activeElement", "setSelected", "flush", "immediate"], "sources": ["../../../../../../packages/components/tree-select/src/cache-options.ts"], "sourcesContent": ["import { defineComponent, inject, watch } from 'vue'\nimport { selectKey } from '@element-plus/components/select'\nimport { isClient } from '@element-plus/utils'\nimport type { SelectContext } from '@element-plus/components/select'\nimport type { PropType } from 'vue'\n\n// same as el-option instance,\n// these are required for `cachedOptions`\nexport type CacheOption = {\n  value: string | number | boolean | object\n  currentLabel: string | number\n  isDisabled: boolean\n}\n\nexport default defineComponent({\n  props: {\n    data: {\n      type: Array as PropType<CacheOption[]>,\n      default: () => [],\n    },\n  },\n  setup(props) {\n    const select = inject(selectKey) as NonNullable<SelectContext>\n\n    watch(\n      () => props.data,\n      () => {\n        props.data.forEach((item) => {\n          if (!select.states.cachedOptions.has(item.value)) {\n            // TODO: the type of 'item' is not compatible with the type of 'cachedOptions',\n            // which may indicate potential runtime issues.\n            // @ts-expect-error\n            select.states.cachedOptions.set(item.value, item)\n          }\n        })\n\n        // fork from packages/select/src/useSelect.ts#330\n        const inputs = select.selectRef?.querySelectorAll('input') || []\n        if (\n          isClient &&\n          !Array.from(inputs).includes(\n            document.activeElement as HTMLInputElement\n          )\n        ) {\n          select.setSelected()\n        }\n      },\n      { flush: 'post', immediate: true }\n    )\n\n    return () => undefined\n  },\n})\n"], "mappings": ";;;;;AAGA,IAAAA,YAAA,GAAeC,eAAe,CAAC;EAC7BC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACrB;EACA,CAAG;EACDC,KAAKA,CAACL,KAAK,EAAE;IACX,MAAMM,MAAM,GAAGC,MAAM,CAACC,SAAS,CAAC;IAChCC,KAAK,CAAC,MAAMT,KAAK,CAACC,IAAI,EAAE,MAAM;MAC5B,IAAIS,EAAE;MACNV,KAAK,CAACC,IAAI,CAACU,OAAO,CAAEC,IAAI,IAAK;QAC3B,IAAI,CAACN,MAAM,CAACO,MAAM,CAACC,aAAa,CAACC,GAAG,CAACH,IAAI,CAACI,KAAK,CAAC,EAAE;UAChDV,MAAM,CAACO,MAAM,CAACC,aAAa,CAACG,GAAG,CAACL,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAAC;QAC3D;MACA,CAAO,CAAC;MACF,MAAMM,MAAM,GAAG,CAAC,CAACR,EAAE,GAAGJ,MAAM,CAACa,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGT,EAAE,CAACU,gBAAgB,CAAC,OAAO,CAAC,KAAK,EAAE;MAC9F,IAAIC,QAAQ,IAAI,CAAClB,KAAK,CAACmB,IAAI,CAACJ,MAAM,CAAC,CAACK,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,EAAE;QACpEnB,MAAM,CAACoB,WAAW,EAAE;MAC5B;IACA,CAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAI,CAAE,CAAC;IACtC,OAAO,MAAM,KAAK,CAAC;EACvB;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}