{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport isKeyable from './_isKeyable.js';\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key) ? data[typeof key == 'string' ? 'string' : 'hash'] : data.map;\n}\nexport default getMapData;", "map": {"version": 3, "names": ["isKeyable", "getMapData", "map", "key", "data", "__data__"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_getMapData.js"], "sourcesContent": ["import isKeyable from './_isKeyable.js';\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\nexport default getMapData;\n"], "mappings": ";;AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAC5B,IAAIC,IAAI,GAAGF,GAAG,CAACG,QAAQ;EACvB,OAAOL,SAAS,CAACG,GAAG,CAAC,GACjBC,IAAI,CAAC,OAAOD,GAAG,IAAI,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,GAChDC,IAAI,CAACF,GAAG;AACd;AAEA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}