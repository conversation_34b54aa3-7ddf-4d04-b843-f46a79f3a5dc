{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/**\n * Converts `value` to an integer suitable for use as the length of an\n * array-like object.\n *\n * **Note:** This method is based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toLength(3.2);\n * // => 3\n *\n * _.toLength(Number.MIN_VALUE);\n * // => 0\n *\n * _.toLength(Infinity);\n * // => 4294967295\n *\n * _.toLength('3.2');\n * // => 3\n */\nfunction toLength(value) {\n  return value ? baseClamp(toInteger(value), 0, MAX_ARRAY_LENGTH) : 0;\n}\nexport default toLength;", "map": {"version": 3, "names": ["baseClamp", "toInteger", "MAX_ARRAY_LENGTH", "to<PERSON><PERSON><PERSON>", "value"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/toLength.js"], "sourcesContent": ["import baseClamp from './_baseClamp.js';\nimport toInteger from './toInteger.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/**\n * Converts `value` to an integer suitable for use as the length of an\n * array-like object.\n *\n * **Note:** This method is based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toLength(3.2);\n * // => 3\n *\n * _.toLength(Number.MIN_VALUE);\n * // => 0\n *\n * _.toLength(Infinity);\n * // => 4294967295\n *\n * _.toLength('3.2');\n * // => 3\n */\nfunction toLength(value) {\n  return value ? baseClamp(toInteger(value), 0, MAX_ARRAY_LENGTH) : 0;\n}\n\nexport default toLength;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,gBAAgB,GAAG,UAAU;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,GAAGJ,SAAS,CAACC,SAAS,CAACG,KAAK,CAAC,EAAE,CAAC,EAAEF,gBAAgB,CAAC,GAAG,CAAC;AACrE;AAEA,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}