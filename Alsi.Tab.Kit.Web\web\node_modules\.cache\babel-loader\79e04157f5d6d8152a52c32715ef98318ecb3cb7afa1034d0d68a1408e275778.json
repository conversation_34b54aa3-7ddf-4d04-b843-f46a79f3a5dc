{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, computed } from 'vue';\nimport { isFunction } from '@vue/shared';\nfunction useFilter(props, tree) {\n  const hiddenNodeKeySet = ref(/* @__PURE__ */new Set([]));\n  const hiddenExpandIconKeySet = ref(/* @__PURE__ */new Set([]));\n  const filterable = computed(() => {\n    return isFunction(props.filterMethod);\n  });\n  function doFilter(query) {\n    var _a;\n    if (!filterable.value) {\n      return;\n    }\n    const expandKeySet = /* @__PURE__ */new Set();\n    const hiddenExpandIconKeys = hiddenExpandIconKeySet.value;\n    const hiddenKeys = hiddenNodeKeySet.value;\n    const family = [];\n    const nodes = ((_a = tree.value) == null ? void 0 : _a.treeNodes) || [];\n    const filter = props.filterMethod;\n    hiddenKeys.clear();\n    function traverse(nodes2) {\n      nodes2.forEach(node => {\n        family.push(node);\n        if (filter == null ? void 0 : filter(query, node.data, node)) {\n          family.forEach(member => {\n            expandKeySet.add(member.key);\n          });\n        } else if (node.isLeaf) {\n          hiddenKeys.add(node.key);\n        }\n        const children = node.children;\n        if (children) {\n          traverse(children);\n        }\n        if (!node.isLeaf) {\n          if (!expandKeySet.has(node.key)) {\n            hiddenKeys.add(node.key);\n          } else if (children) {\n            let allHidden = true;\n            for (const childNode of children) {\n              if (!hiddenKeys.has(childNode.key)) {\n                allHidden = false;\n                break;\n              }\n            }\n            if (allHidden) {\n              hiddenExpandIconKeys.add(node.key);\n            } else {\n              hiddenExpandIconKeys.delete(node.key);\n            }\n          }\n        }\n        family.pop();\n      });\n    }\n    traverse(nodes);\n    return expandKeySet;\n  }\n  function isForceHiddenExpandIcon(node) {\n    return hiddenExpandIconKeySet.value.has(node.key);\n  }\n  return {\n    hiddenExpandIconKeySet,\n    hiddenNodeKeySet,\n    doFilter,\n    isForceHiddenExpandIcon\n  };\n}\nexport { useFilter };", "map": {"version": 3, "names": ["useFilter", "props", "tree", "hiddenNodeKeySet", "ref", "Set", "hiddenExpandIconKeySet", "filterable", "computed", "isFunction", "filterMethod", "<PERSON><PERSON><PERSON><PERSON>", "query", "_a", "value", "expandKeySet", "hiddenExpandIconKeys", "hiddenKeys", "family", "nodes", "treeNodes", "filter", "clear", "traverse", "nodes2", "for<PERSON>ach", "node", "push", "data", "member", "add", "key", "<PERSON><PERSON><PERSON><PERSON>", "children", "has", "allHidden", "childNode", "delete", "pop", "isForceHiddenExpandIcon"], "sources": ["../../../../../../../packages/components/tree-v2/src/composables/useFilter.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { isFunction } from '@element-plus/utils'\nimport type { Ref } from 'vue'\nimport type { Tree, TreeKey, TreeNode, TreeProps } from '../types'\n\n// When the data volume is very large using filter will cause lag\n// I haven't found a better way to optimize it for now\n// Maybe this problem should be left to the server side\nexport function useFilter(props: TreeProps, tree: Ref<Tree | undefined>) {\n  const hiddenNodeKeySet = ref<Set<TreeKey>>(new Set([]))\n  const hiddenExpandIconKeySet = ref<Set<TreeKey>>(new Set([]))\n\n  const filterable = computed(() => {\n    return isFunction(props.filterMethod)\n  })\n\n  function doFilter(query: string) {\n    if (!filterable.value) {\n      return\n    }\n    const expandKeySet = new Set<TreeKey>()\n    const hiddenExpandIconKeys = hiddenExpandIconKeySet.value\n    const hiddenKeys = hiddenNodeKeySet.value\n    const family: TreeNode[] = []\n    const nodes = tree.value?.treeNodes || []\n    const filter = props.filterMethod\n    hiddenKeys.clear()\n    function traverse(nodes: TreeNode[]) {\n      nodes.forEach((node) => {\n        family.push(node)\n        if (filter?.(query, node.data, node)) {\n          family.forEach((member) => {\n            expandKeySet.add(member.key)\n          })\n        } else if (node.isLeaf) {\n          hiddenKeys.add(node.key)\n        }\n        const children = node.children\n        if (children) {\n          traverse(children)\n        }\n        if (!node.isLeaf) {\n          if (!expandKeySet.has(node.key)) {\n            hiddenKeys.add(node.key)\n          } else if (children) {\n            // If all child nodes are hidden, then the expand icon will be hidden\n            let allHidden = true\n            for (const childNode of children) {\n              if (!hiddenKeys.has(childNode.key)) {\n                allHidden = false\n                break\n              }\n            }\n            if (allHidden) {\n              hiddenExpandIconKeys.add(node.key)\n            } else {\n              hiddenExpandIconKeys.delete(node.key)\n            }\n          }\n        }\n        family.pop()\n      })\n    }\n    traverse(nodes)\n    return expandKeySet\n  }\n\n  function isForceHiddenExpandIcon(node: TreeNode): boolean {\n    return hiddenExpandIconKeySet.value.has(node.key)\n  }\n\n  return {\n    hiddenExpandIconKeySet,\n    hiddenNodeKeySet,\n    doFilter,\n    isForceHiddenExpandIcon,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAEO,SAASA,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACrC,MAAMC,gBAAgB,GAAGC,GAAG,gBAAiB,IAAIC,GAAG,CAAC,EAAE,CAAC,CAAC;EACzD,MAAMC,sBAAsB,GAAGF,GAAG,gBAAiB,IAAIC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC/D,MAAME,UAAU,GAAGC,QAAQ,CAAC,MAAM;IAChC,OAAOC,UAAU,CAACR,KAAK,CAACS,YAAY,CAAC;EACzC,CAAG,CAAC;EACF,SAASC,QAAQA,CAACC,KAAK,EAAE;IACvB,IAAIC,EAAE;IACN,IAAI,CAACN,UAAU,CAACO,KAAK,EAAE;MACrB;IACN;IACI,MAAMC,YAAY,kBAAmB,IAAIV,GAAG,EAAE;IAC9C,MAAMW,oBAAoB,GAAGV,sBAAsB,CAACQ,KAAK;IACzD,MAAMG,UAAU,GAAGd,gBAAgB,CAACW,KAAK;IACzC,MAAMI,MAAM,GAAG,EAAE;IACjB,MAAMC,KAAK,GAAG,CAAC,CAACN,EAAE,GAAGX,IAAI,CAACY,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACO,SAAS,KAAK,EAAE;IACvE,MAAMC,MAAM,GAAGpB,KAAK,CAACS,YAAY;IACjCO,UAAU,CAACK,KAAK,EAAE;IAClB,SAASC,QAAQA,CAACC,MAAM,EAAE;MACxBA,MAAM,CAACC,OAAO,CAAEC,IAAI,IAAK;QACvBR,MAAM,CAACS,IAAI,CAACD,IAAI,CAAC;QACjB,IAAIL,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACT,KAAK,EAAEc,IAAI,CAACE,IAAI,EAAEF,IAAI,CAAC,EAAE;UAC5DR,MAAM,CAACO,OAAO,CAAEI,MAAM,IAAK;YACzBd,YAAY,CAACe,GAAG,CAACD,MAAM,CAACE,GAAG,CAAC;UACxC,CAAW,CAAC;QACZ,CAAS,MAAM,IAAIL,IAAI,CAACM,MAAM,EAAE;UACtBf,UAAU,CAACa,GAAG,CAACJ,IAAI,CAACK,GAAG,CAAC;QAClC;QACQ,MAAME,QAAQ,GAAGP,IAAI,CAACO,QAAQ;QAC9B,IAAIA,QAAQ,EAAE;UACZV,QAAQ,CAACU,QAAQ,CAAC;QAC5B;QACQ,IAAI,CAACP,IAAI,CAACM,MAAM,EAAE;UAChB,IAAI,CAACjB,YAAY,CAACmB,GAAG,CAACR,IAAI,CAACK,GAAG,CAAC,EAAE;YAC/Bd,UAAU,CAACa,GAAG,CAACJ,IAAI,CAACK,GAAG,CAAC;UACpC,CAAW,MAAM,IAAIE,QAAQ,EAAE;YACnB,IAAIE,SAAS,GAAG,IAAI;YACpB,KAAK,MAAMC,SAAS,IAAIH,QAAQ,EAAE;cAChC,IAAI,CAAChB,UAAU,CAACiB,GAAG,CAACE,SAAS,CAACL,GAAG,CAAC,EAAE;gBAClCI,SAAS,GAAG,KAAK;gBACjB;cAChB;YACA;YACY,IAAIA,SAAS,EAAE;cACbnB,oBAAoB,CAACc,GAAG,CAACJ,IAAI,CAACK,GAAG,CAAC;YAChD,CAAa,MAAM;cACLf,oBAAoB,CAACqB,MAAM,CAACX,IAAI,CAACK,GAAG,CAAC;YACnD;UACA;QACA;QACQb,MAAM,CAACoB,GAAG,EAAE;MACpB,CAAO,CAAC;IACR;IACIf,QAAQ,CAACJ,KAAK,CAAC;IACf,OAAOJ,YAAY;EACvB;EACE,SAASwB,uBAAuBA,CAACb,IAAI,EAAE;IACrC,OAAOpB,sBAAsB,CAACQ,KAAK,CAACoB,GAAG,CAACR,IAAI,CAACK,GAAG,CAAC;EACrD;EACE,OAAO;IACLzB,sBAAsB;IACtBH,gBAAgB;IAChBQ,QAAQ;IACR4B;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}