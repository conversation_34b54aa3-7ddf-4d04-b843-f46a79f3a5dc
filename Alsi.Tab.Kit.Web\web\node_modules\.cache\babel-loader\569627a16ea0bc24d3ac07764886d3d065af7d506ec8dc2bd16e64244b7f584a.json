{"ast": null, "code": "import { inject, computed } from 'vue';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nfunction useMapState() {\n  var _a;\n  const table = inject(TABLE_INJECTION_KEY);\n  const store = table == null ? void 0 : table.store;\n  const leftFixedLeafCount = computed(() => {\n    var _a2;\n    return (_a2 = store == null ? void 0 : store.states.fixedLeafColumnsLength.value) != null ? _a2 : 0;\n  });\n  const rightFixedLeafCount = computed(() => {\n    var _a2;\n    return (_a2 = store == null ? void 0 : store.states.rightFixedColumns.value.length) != null ? _a2 : 0;\n  });\n  const columnsCount = computed(() => {\n    var _a2;\n    return (_a2 = store == null ? void 0 : store.states.columns.value.length) != null ? _a2 : 0;\n  });\n  const leftFixedCount = computed(() => {\n    var _a2;\n    return (_a2 = store == null ? void 0 : store.states.fixedColumns.value.length) != null ? _a2 : 0;\n  });\n  const rightFixedCount = computed(() => {\n    var _a2;\n    return (_a2 = store == null ? void 0 : store.states.rightFixedColumns.value.length) != null ? _a2 : 0;\n  });\n  return {\n    leftFixedLeafCount,\n    rightFixedLeafCount,\n    columnsCount,\n    leftFixedCount,\n    rightFixedCount,\n    columns: (_a = store == null ? void 0 : store.states.columns) != null ? _a : []\n  };\n}\nexport { useMapState as default };", "map": {"version": 3, "names": ["useMapState", "_a", "table", "inject", "TABLE_INJECTION_KEY", "store", "leftFixedLeafCount", "computed", "_a2", "states", "fixedLeafColumnsLength", "value", "rightFixedLeafCount", "rightFixedColumns", "length", "columnsCount", "columns", "leftFixedCount", "fixedColumns", "rightFixedCount"], "sources": ["../../../../../../../packages/components/table/src/table-footer/mapState-helper.ts"], "sourcesContent": ["import { computed, inject } from 'vue'\nimport { TABLE_INJECTION_KEY } from '../tokens'\n\nfunction useMapState() {\n  const table = inject(TABLE_INJECTION_KEY)\n  const store = table?.store\n  const leftFixedLeafCount = computed(() => {\n    return store?.states.fixedLeafColumnsLength.value ?? 0\n  })\n  const rightFixedLeafCount = computed(() => {\n    return store?.states.rightFixedColumns.value.length ?? 0\n  })\n  const columnsCount = computed(() => {\n    return store?.states.columns.value.length ?? 0\n  })\n  const leftFixedCount = computed(() => {\n    return store?.states.fixedColumns.value.length ?? 0\n  })\n  const rightFixedCount = computed(() => {\n    return store?.states.rightFixedColumns.value.length ?? 0\n  })\n\n  return {\n    leftFixedLeafCount,\n    rightFixedLeafCount,\n    columnsCount,\n    leftFixedCount,\n    rightFixedCount,\n    columns: store?.states.columns ?? [],\n  }\n}\n\nexport default useMapState\n"], "mappings": ";;AAEA,SAASA,WAAWA,CAAA,EAAG;EACrB,IAAIC,EAAE;EACN,MAAMC,KAAK,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EACzC,MAAMC,KAAK,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,KAAK;EAClD,MAAMC,kBAAkB,GAAGC,QAAQ,CAAC,MAAM;IACxC,IAAIC,GAAG;IACP,OAAO,CAACA,GAAG,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACC,sBAAsB,CAACC,KAAK,KAAK,IAAI,GAAGH,GAAG,GAAG,CAAC;EACvG,CAAG,CAAC;EACF,MAAMI,mBAAmB,GAAGL,QAAQ,CAAC,MAAM;IACzC,IAAIC,GAAG;IACP,OAAO,CAACA,GAAG,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACI,iBAAiB,CAACF,KAAK,CAACG,MAAM,KAAK,IAAI,GAAGN,GAAG,GAAG,CAAC;EACzG,CAAG,CAAC;EACF,MAAMO,YAAY,GAAGR,QAAQ,CAAC,MAAM;IAClC,IAAIC,GAAG;IACP,OAAO,CAACA,GAAG,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACO,OAAO,CAACL,KAAK,CAACG,MAAM,KAAK,IAAI,GAAGN,GAAG,GAAG,CAAC;EAC/F,CAAG,CAAC;EACF,MAAMS,cAAc,GAAGV,QAAQ,CAAC,MAAM;IACpC,IAAIC,GAAG;IACP,OAAO,CAACA,GAAG,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACS,YAAY,CAACP,KAAK,CAACG,MAAM,KAAK,IAAI,GAAGN,GAAG,GAAG,CAAC;EACpG,CAAG,CAAC;EACF,MAAMW,eAAe,GAAGZ,QAAQ,CAAC,MAAM;IACrC,IAAIC,GAAG;IACP,OAAO,CAACA,GAAG,GAAGH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACI,iBAAiB,CAACF,KAAK,CAACG,MAAM,KAAK,IAAI,GAAGN,GAAG,GAAG,CAAC;EACzG,CAAG,CAAC;EACF,OAAO;IACLF,kBAAkB;IAClBM,mBAAmB;IACnBG,YAAY;IACZE,cAAc;IACdE,eAAe;IACfH,OAAO,EAAE,CAACf,EAAE,GAAGI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM,CAACO,OAAO,KAAK,IAAI,GAAGf,EAAE,GAAG;EACjF,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}