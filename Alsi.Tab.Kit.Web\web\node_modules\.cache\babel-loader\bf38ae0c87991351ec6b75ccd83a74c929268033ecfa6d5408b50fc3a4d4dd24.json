{"ast": null, "code": "import { panelRangeSharedProps } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst panelYearRangeProps = buildProps({\n  ...panelRangeSharedProps\n});\nconst panelYearRangeEmits = [\"pick\", \"set-picker-option\", \"calendar-change\"];\nexport { panelYearRangeEmits, panelYearRangeProps };", "map": {"version": 3, "names": ["panelYearRangeProps", "buildProps", "panelRangeSharedProps", "panelYearRangeEmits"], "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-year-range.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { panelRangeSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const panelYearRangeProps = buildProps({\n  ...panelRangeSharedProps,\n} as const)\n\nexport const panelYearRangeEmits = [\n  'pick',\n  'set-picker-option',\n  'calendar-change',\n]\n\nexport type PanelYearRangeProps = ExtractPropTypes<typeof panelYearRangeProps>\n"], "mappings": ";;AAEY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5C,GAAGC;AACL,CAAC;AACW,MAACC,mBAAmB,GAAG,CACjC,MAAM,EACN,mBAAmB,EACnB,iBAAiB,CACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}