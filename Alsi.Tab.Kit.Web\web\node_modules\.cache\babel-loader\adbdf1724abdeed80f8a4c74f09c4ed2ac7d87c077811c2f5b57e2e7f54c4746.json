{"ast": null, "code": "import { panelSharedProps, panelRangeSharedProps } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst panelDateRangeProps = buildProps({\n  ...panelSharedProps,\n  ...panelRangeSharedProps\n});\nexport { panelDateRangeProps };", "map": {"version": 3, "names": ["panelDateRangeProps", "buildProps", "panelSharedProps", "panelRangeSharedProps"], "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-date-range.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { panelRangeSharedProps, panelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const panelDateRangeProps = buildProps({\n  ...panelSharedProps,\n  ...panelRangeSharedProps,\n} as const)\n\nexport type PanelDateRangeProps = ExtractPropTypes<typeof panelDateRangeProps>\n"], "mappings": ";;AAEY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5C,GAAGC,gBAAgB;EACnB,GAAGC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}