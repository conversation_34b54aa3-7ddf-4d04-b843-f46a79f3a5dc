{"ast": null, "code": "import arrayFilter from './_arrayFilter.js';\nimport baseRest from './_baseRest.js';\nimport baseXor from './_baseXor.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Creates an array of unique values that is the\n * [symmetric difference](https://en.wikipedia.org/wiki/Symmetric_difference)\n * of the given arrays. The order of result values is determined by the order\n * they occur in the arrays.\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.difference, _.without\n * @example\n *\n * _.xor([2, 1], [2, 3]);\n * // => [1, 3]\n */\nvar xor = baseRest(function (arrays) {\n  return baseXor(arrayFilter(arrays, isArrayLikeObject));\n});\nexport default xor;", "map": {"version": 3, "names": ["arrayFilter", "baseRest", "baseXor", "isArrayLikeObject", "xor", "arrays"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/xor.js"], "sourcesContent": ["import arrayFilter from './_arrayFilter.js';\nimport baseRest from './_baseRest.js';\nimport baseXor from './_baseXor.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/**\n * Creates an array of unique values that is the\n * [symmetric difference](https://en.wikipedia.org/wiki/Symmetric_difference)\n * of the given arrays. The order of result values is determined by the order\n * they occur in the arrays.\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.difference, _.without\n * @example\n *\n * _.xor([2, 1], [2, 3]);\n * // => [1, 3]\n */\nvar xor = baseRest(function(arrays) {\n  return baseXor(arrayFilter(arrays, isArrayLikeObject));\n});\n\nexport default xor;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,iBAAiB,MAAM,wBAAwB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,UAASI,MAAM,EAAE;EAClC,OAAOH,OAAO,CAACF,WAAW,CAACK,MAAM,EAAEF,iBAAiB,CAAC,CAAC;AACxD,CAAC,CAAC;AAEF,eAAeC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}