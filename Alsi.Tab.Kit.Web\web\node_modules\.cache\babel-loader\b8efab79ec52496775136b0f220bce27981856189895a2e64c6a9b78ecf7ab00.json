{"ast": null, "code": "import Symbol from './_Symbol.js';\nimport copyArray from './_copyArray.js';\nimport getTag from './_getTag.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport iteratorToArray from './_iteratorToArray.js';\nimport mapToArray from './_mapToArray.js';\nimport setToArray from './_setToArray.js';\nimport stringToArray from './_stringToArray.js';\nimport values from './values.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n  setTag = '[object Set]';\n\n/** Built-in value references. */\nvar symIterator = Symbol ? Symbol.iterator : undefined;\n\n/**\n * Converts `value` to an array.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Array} Returns the converted array.\n * @example\n *\n * _.toArray({ 'a': 1, 'b': 2 });\n * // => [1, 2]\n *\n * _.toArray('abc');\n * // => ['a', 'b', 'c']\n *\n * _.toArray(1);\n * // => []\n *\n * _.toArray(null);\n * // => []\n */\nfunction toArray(value) {\n  if (!value) {\n    return [];\n  }\n  if (isArrayLike(value)) {\n    return isString(value) ? stringToArray(value) : copyArray(value);\n  }\n  if (symIterator && value[symIterator]) {\n    return iteratorToArray(value[symIterator]());\n  }\n  var tag = getTag(value),\n    func = tag == mapTag ? mapToArray : tag == setTag ? setToArray : values;\n  return func(value);\n}\nexport default toArray;", "map": {"version": 3, "names": ["Symbol", "copyArray", "getTag", "isArrayLike", "isString", "iteratorToArray", "mapToArray", "setToArray", "stringToArray", "values", "mapTag", "setTag", "symIterator", "iterator", "undefined", "toArray", "value", "tag", "func"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/toArray.js"], "sourcesContent": ["import Symbol from './_Symbol.js';\nimport copyArray from './_copyArray.js';\nimport getTag from './_getTag.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport iteratorToArray from './_iteratorToArray.js';\nimport mapToArray from './_mapToArray.js';\nimport setToArray from './_setToArray.js';\nimport stringToArray from './_stringToArray.js';\nimport values from './values.js';\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    setTag = '[object Set]';\n\n/** Built-in value references. */\nvar symIterator = Symbol ? Symbol.iterator : undefined;\n\n/**\n * Converts `value` to an array.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {Array} Returns the converted array.\n * @example\n *\n * _.toArray({ 'a': 1, 'b': 2 });\n * // => [1, 2]\n *\n * _.toArray('abc');\n * // => ['a', 'b', 'c']\n *\n * _.toArray(1);\n * // => []\n *\n * _.toArray(null);\n * // => []\n */\nfunction toArray(value) {\n  if (!value) {\n    return [];\n  }\n  if (isArrayLike(value)) {\n    return isString(value) ? stringToArray(value) : copyArray(value);\n  }\n  if (symIterator && value[symIterator]) {\n    return iteratorToArray(value[symIterator]());\n  }\n  var tag = getTag(value),\n      func = tag == mapTag ? mapToArray : (tag == setTag ? setToArray : values);\n\n  return func(value);\n}\n\nexport default toArray;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA,IAAIC,MAAM,GAAG,cAAc;EACvBC,MAAM,GAAG,cAAc;;AAE3B;AACA,IAAIC,WAAW,GAAGZ,MAAM,GAAGA,MAAM,CAACa,QAAQ,GAAGC,SAAS;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EACA,IAAIb,WAAW,CAACa,KAAK,CAAC,EAAE;IACtB,OAAOZ,QAAQ,CAACY,KAAK,CAAC,GAAGR,aAAa,CAACQ,KAAK,CAAC,GAAGf,SAAS,CAACe,KAAK,CAAC;EAClE;EACA,IAAIJ,WAAW,IAAII,KAAK,CAACJ,WAAW,CAAC,EAAE;IACrC,OAAOP,eAAe,CAACW,KAAK,CAACJ,WAAW,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA,IAAIK,GAAG,GAAGf,MAAM,CAACc,KAAK,CAAC;IACnBE,IAAI,GAAGD,GAAG,IAAIP,MAAM,GAAGJ,UAAU,GAAIW,GAAG,IAAIN,MAAM,GAAGJ,UAAU,GAAGE,MAAO;EAE7E,OAAOS,IAAI,CAACF,KAAK,CAAC;AACpB;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}