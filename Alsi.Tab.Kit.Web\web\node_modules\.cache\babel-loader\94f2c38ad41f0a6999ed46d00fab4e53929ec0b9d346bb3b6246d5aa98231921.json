{"ast": null, "code": "import { panelSharedProps } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst panelDatePickProps = buildProps({\n  ...panelSharedProps,\n  parsedValue: {\n    type: definePropType([Object, Array])\n  },\n  visible: {\n    type: Boolean\n  },\n  format: {\n    type: String,\n    default: \"\"\n  }\n});\nexport { panelDatePickProps };", "map": {"version": 3, "names": ["panelDatePickProps", "buildProps", "panelSharedProps", "parsedValue", "type", "definePropType", "Object", "Array", "visible", "Boolean", "format", "String", "default"], "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-date-pick.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { panelSharedProps } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelDatePickProps = buildProps({\n  ...panelSharedProps,\n  parsedValue: {\n    type: definePropType<Dayjs | Dayjs[]>([Object, Array]),\n  },\n  visible: {\n    type: Boolean,\n  },\n  format: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type PanelDatePickProps = ExtractPropTypes<typeof panelDatePickProps>\n"], "mappings": ";;AAEY,MAACA,kBAAkB,GAAGC,UAAU,CAAC;EAC3C,GAAGC,gBAAgB;EACnBC,WAAW,EAAE;IACXC,IAAI,EAAEC,cAAc,CAAC,CAACC,MAAM,EAAEC,KAAK,CAAC;EACxC,CAAG;EACDC,OAAO,EAAE;IACPJ,IAAI,EAAEK;EACV,CAAG;EACDC,MAAM,EAAE;IACNN,IAAI,EAAEO,MAAM;IACZC,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}