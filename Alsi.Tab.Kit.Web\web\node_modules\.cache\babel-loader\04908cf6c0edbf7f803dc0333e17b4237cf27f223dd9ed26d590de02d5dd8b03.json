{"ast": null, "code": "const tooltipV2RootKey = Symbol(\"tooltipV2\");\nconst tooltipV2ContentKey = Symbol(\"tooltipV2Content\");\nconst TOOLTIP_V2_OPEN = \"tooltip_v2.open\";\nexport { TOOLTIP_V2_OPEN, tooltipV2ContentKey, tooltipV2RootKey };", "map": {"version": 3, "names": ["tooltipV2RootKey", "Symbol", "tooltipV2ContentKey", "TOOLTIP_V2_OPEN"], "sources": ["../../../../../../packages/components/tooltip-v2/src/constants.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, Ref } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\n\nexport type TooltipV2Context = {\n  onClose: () => void\n  onDelayOpen: () => void\n  onOpen: () => void\n  contentId: Ref<string>\n  triggerRef: Ref<HTMLElement | null>\n  ns: UseNamespaceReturn\n}\n\nexport type TooltipV2ContentContext = {\n  arrowRef: Ref<HTMLElement | null>\n}\n\nexport const tooltipV2RootKey: InjectionKey<TooltipV2Context> =\n  Symbol('tooltipV2')\n\nexport const tooltipV2ContentKey: InjectionKey<TooltipV2ContentContext> =\n  Symbol('tooltipV2Content')\n\nexport const TOOLTIP_V2_OPEN = 'tooltip_v2.open'\n"], "mappings": "AAAY,MAACA,gBAAgB,GAAGC,MAAM,CAAC,WAAW;AACtC,MAACC,mBAAmB,GAAGD,MAAM,CAAC,kBAAkB;AAChD,MAACE,eAAe,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}