{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { getCurrentInstance, toRefs, ref, computed, watch, unref } from 'vue';\nimport { getKeysMap, getRowIdentity, toggleRowStatus, getColumnById, getColumnByKey, orderBy } from '../util.mjs';\nimport useExpand from './expand.mjs';\nimport useCurrent from './current.mjs';\nimport useTree from './tree.mjs';\nimport { hasOwn, isArray, isString } from '@vue/shared';\nconst sortData = (data, states) => {\n  const sortingColumn = states.sortingColumn;\n  if (!sortingColumn || isString(sortingColumn.sortable)) {\n    return data;\n  }\n  return orderBy(data, states.sortProp, states.sortOrder, sortingColumn.sortMethod, sortingColumn.sortBy);\n};\nconst doFlattenColumns = columns => {\n  const result = [];\n  columns.forEach(column => {\n    if (column.children && column.children.length > 0) {\n      result.push.apply(result, doFlattenColumns(column.children));\n    } else {\n      result.push(column);\n    }\n  });\n  return result;\n};\nfunction useWatcher() {\n  var _a;\n  const instance = getCurrentInstance();\n  const {\n    size: tableSize\n  } = toRefs((_a = instance.proxy) == null ? void 0 : _a.$props);\n  const rowKey = ref(null);\n  const data = ref([]);\n  const _data = ref([]);\n  const isComplex = ref(false);\n  const _columns = ref([]);\n  const originColumns = ref([]);\n  const columns = ref([]);\n  const fixedColumns = ref([]);\n  const rightFixedColumns = ref([]);\n  const leafColumns = ref([]);\n  const fixedLeafColumns = ref([]);\n  const rightFixedLeafColumns = ref([]);\n  const updateOrderFns = [];\n  const leafColumnsLength = ref(0);\n  const fixedLeafColumnsLength = ref(0);\n  const rightFixedLeafColumnsLength = ref(0);\n  const isAllSelected = ref(false);\n  const selection = ref([]);\n  const reserveSelection = ref(false);\n  const selectOnIndeterminate = ref(false);\n  const selectable = ref(null);\n  const filters = ref({});\n  const filteredData = ref(null);\n  const sortingColumn = ref(null);\n  const sortProp = ref(null);\n  const sortOrder = ref(null);\n  const hoverRow = ref(null);\n  const selectedMap = computed(() => {\n    return rowKey.value ? getKeysMap(selection.value, rowKey.value) : void 0;\n  });\n  watch(data, () => {\n    var _a2;\n    if (instance.state) {\n      scheduleLayout(false);\n      const needUpdateFixed = instance.props.tableLayout === \"auto\";\n      if (needUpdateFixed) {\n        (_a2 = instance.refs.tableHeaderRef) == null ? void 0 : _a2.updateFixedColumnStyle();\n      }\n    }\n  }, {\n    deep: true\n  });\n  const assertRowKey = () => {\n    if (!rowKey.value) throw new Error(\"[ElTable] prop row-key is required\");\n  };\n  const updateChildFixed = column => {\n    var _a2;\n    (_a2 = column.children) == null ? void 0 : _a2.forEach(childColumn => {\n      childColumn.fixed = column.fixed;\n      updateChildFixed(childColumn);\n    });\n  };\n  const updateColumns = () => {\n    _columns.value.forEach(column => {\n      updateChildFixed(column);\n    });\n    fixedColumns.value = _columns.value.filter(column => [true, \"left\"].includes(column.fixed));\n    const selectColumn = _columns.value.find(column => column.type === \"selection\");\n    let selectColFixLeft;\n    if (selectColumn && selectColumn.fixed !== \"right\" && !fixedColumns.value.includes(selectColumn)) {\n      const selectColumnIndex = _columns.value.indexOf(selectColumn);\n      if (selectColumnIndex === 0 && fixedColumns.value.length) {\n        fixedColumns.value.unshift(selectColumn);\n        selectColFixLeft = true;\n      }\n    }\n    rightFixedColumns.value = _columns.value.filter(column => column.fixed === \"right\");\n    const notFixedColumns = _columns.value.filter(column => (selectColFixLeft ? column.type !== \"selection\" : true) && !column.fixed);\n    originColumns.value = [].concat(fixedColumns.value).concat(notFixedColumns).concat(rightFixedColumns.value);\n    const leafColumns2 = doFlattenColumns(notFixedColumns);\n    const fixedLeafColumns2 = doFlattenColumns(fixedColumns.value);\n    const rightFixedLeafColumns2 = doFlattenColumns(rightFixedColumns.value);\n    leafColumnsLength.value = leafColumns2.length;\n    fixedLeafColumnsLength.value = fixedLeafColumns2.length;\n    rightFixedLeafColumnsLength.value = rightFixedLeafColumns2.length;\n    columns.value = [].concat(fixedLeafColumns2).concat(leafColumns2).concat(rightFixedLeafColumns2);\n    isComplex.value = fixedColumns.value.length > 0 || rightFixedColumns.value.length > 0;\n  };\n  const scheduleLayout = (needUpdateColumns, immediate = false) => {\n    if (needUpdateColumns) {\n      updateColumns();\n    }\n    if (immediate) {\n      instance.state.doLayout();\n    } else {\n      instance.state.debouncedUpdateLayout();\n    }\n  };\n  const isSelected = row => {\n    if (selectedMap.value) {\n      return !!selectedMap.value[getRowIdentity(row, rowKey.value)];\n    } else {\n      return selection.value.includes(row);\n    }\n  };\n  const clearSelection = () => {\n    isAllSelected.value = false;\n    const oldSelection = selection.value;\n    selection.value = [];\n    if (oldSelection.length) {\n      instance.emit(\"selection-change\", []);\n    }\n  };\n  const cleanSelection = () => {\n    var _a2, _b;\n    let deleted;\n    if (rowKey.value) {\n      deleted = [];\n      const childrenKey = (_b = (_a2 = instance == null ? void 0 : instance.store) == null ? void 0 : _a2.states) == null ? void 0 : _b.childrenColumnName.value;\n      const dataMap = getKeysMap(data.value, rowKey.value, true, childrenKey);\n      for (const key in selectedMap.value) {\n        if (hasOwn(selectedMap.value, key) && !dataMap[key]) {\n          deleted.push(selectedMap.value[key].row);\n        }\n      }\n    } else {\n      deleted = selection.value.filter(item => !data.value.includes(item));\n    }\n    if (deleted.length) {\n      const newSelection = selection.value.filter(item => !deleted.includes(item));\n      selection.value = newSelection;\n      instance.emit(\"selection-change\", newSelection.slice());\n    }\n  };\n  const getSelectionRows = () => {\n    return (selection.value || []).slice();\n  };\n  const toggleRowSelection = (row, selected, emitChange = true, ignoreSelectable = false) => {\n    var _a2, _b, _c, _d;\n    const treeProps = {\n      children: (_b = (_a2 = instance == null ? void 0 : instance.store) == null ? void 0 : _a2.states) == null ? void 0 : _b.childrenColumnName.value,\n      checkStrictly: (_d = (_c = instance == null ? void 0 : instance.store) == null ? void 0 : _c.states) == null ? void 0 : _d.checkStrictly.value\n    };\n    const changed = toggleRowStatus(selection.value, row, selected, treeProps, ignoreSelectable ? void 0 : selectable.value, data.value.indexOf(row));\n    if (changed) {\n      const newSelection = (selection.value || []).slice();\n      if (emitChange) {\n        instance.emit(\"select\", newSelection, row);\n      }\n      instance.emit(\"selection-change\", newSelection);\n    }\n  };\n  const _toggleAllSelection = () => {\n    var _a2, _b;\n    const value = selectOnIndeterminate.value ? !isAllSelected.value : !(isAllSelected.value || selection.value.length);\n    isAllSelected.value = value;\n    let selectionChanged = false;\n    let childrenCount = 0;\n    const rowKey2 = (_b = (_a2 = instance == null ? void 0 : instance.store) == null ? void 0 : _a2.states) == null ? void 0 : _b.rowKey.value;\n    const {\n      childrenColumnName\n    } = instance.store.states;\n    const treeProps = {\n      children: childrenColumnName.value,\n      checkStrictly: false\n    };\n    data.value.forEach((row, index) => {\n      const rowIndex = index + childrenCount;\n      if (toggleRowStatus(selection.value, row, value, treeProps, selectable.value, rowIndex)) {\n        selectionChanged = true;\n      }\n      childrenCount += getChildrenCount(getRowIdentity(row, rowKey2));\n    });\n    if (selectionChanged) {\n      instance.emit(\"selection-change\", selection.value ? selection.value.slice() : []);\n    }\n    instance.emit(\"select-all\", (selection.value || []).slice());\n  };\n  const updateSelectionByRowKey = () => {\n    data.value.forEach(row => {\n      const rowId = getRowIdentity(row, rowKey.value);\n      const rowInfo = selectedMap.value[rowId];\n      if (rowInfo) {\n        selection.value[rowInfo.index] = row;\n      }\n    });\n  };\n  const updateAllSelected = () => {\n    var _a2;\n    if (((_a2 = data.value) == null ? void 0 : _a2.length) === 0) {\n      isAllSelected.value = false;\n      return;\n    }\n    const {\n      childrenColumnName\n    } = instance.store.states;\n    let rowIndex = 0;\n    let selectedCount = 0;\n    const checkSelectedStatus = data2 => {\n      var _a3;\n      for (const row of data2) {\n        const isRowSelectable = selectable.value && selectable.value.call(null, row, rowIndex);\n        if (!isSelected(row)) {\n          if (!selectable.value || isRowSelectable) {\n            return false;\n          }\n        } else {\n          selectedCount++;\n        }\n        rowIndex++;\n        if (((_a3 = row[childrenColumnName.value]) == null ? void 0 : _a3.length) && !checkSelectedStatus(row[childrenColumnName.value])) {\n          return false;\n        }\n      }\n      return true;\n    };\n    const isAllSelected_ = checkSelectedStatus(data.value || []);\n    isAllSelected.value = selectedCount === 0 ? false : isAllSelected_;\n  };\n  const getChildrenCount = rowKey2 => {\n    var _a2;\n    if (!instance || !instance.store) return 0;\n    const {\n      treeData\n    } = instance.store.states;\n    let count = 0;\n    const children = (_a2 = treeData.value[rowKey2]) == null ? void 0 : _a2.children;\n    if (children) {\n      count += children.length;\n      children.forEach(childKey => {\n        count += getChildrenCount(childKey);\n      });\n    }\n    return count;\n  };\n  const updateFilters = (columns2, values) => {\n    if (!isArray(columns2)) {\n      columns2 = [columns2];\n    }\n    const filters_ = {};\n    columns2.forEach(col => {\n      filters.value[col.id] = values;\n      filters_[col.columnKey || col.id] = values;\n    });\n    return filters_;\n  };\n  const updateSort = (column, prop, order) => {\n    if (sortingColumn.value && sortingColumn.value !== column) {\n      sortingColumn.value.order = null;\n    }\n    sortingColumn.value = column;\n    sortProp.value = prop;\n    sortOrder.value = order;\n  };\n  const execFilter = () => {\n    let sourceData = unref(_data);\n    Object.keys(filters.value).forEach(columnId => {\n      const values = filters.value[columnId];\n      if (!values || values.length === 0) return;\n      const column = getColumnById({\n        columns: columns.value\n      }, columnId);\n      if (column && column.filterMethod) {\n        sourceData = sourceData.filter(row => {\n          return values.some(value => column.filterMethod.call(null, value, row, column));\n        });\n      }\n    });\n    filteredData.value = sourceData;\n  };\n  const execSort = () => {\n    data.value = sortData(filteredData.value, {\n      sortingColumn: sortingColumn.value,\n      sortProp: sortProp.value,\n      sortOrder: sortOrder.value\n    });\n  };\n  const execQuery = (ignore = void 0) => {\n    if (!(ignore && ignore.filter)) {\n      execFilter();\n    }\n    execSort();\n  };\n  const clearFilter = columnKeys => {\n    const {\n      tableHeaderRef\n    } = instance.refs;\n    if (!tableHeaderRef) return;\n    const panels = Object.assign({}, tableHeaderRef.filterPanels);\n    const keys = Object.keys(panels);\n    if (!keys.length) return;\n    if (isString(columnKeys)) {\n      columnKeys = [columnKeys];\n    }\n    if (isArray(columnKeys)) {\n      const columns_ = columnKeys.map(key => getColumnByKey({\n        columns: columns.value\n      }, key));\n      keys.forEach(key => {\n        const column = columns_.find(col => col.id === key);\n        if (column) {\n          column.filteredValue = [];\n        }\n      });\n      instance.store.commit(\"filterChange\", {\n        column: columns_,\n        values: [],\n        silent: true,\n        multi: true\n      });\n    } else {\n      keys.forEach(key => {\n        const column = columns.value.find(col => col.id === key);\n        if (column) {\n          column.filteredValue = [];\n        }\n      });\n      filters.value = {};\n      instance.store.commit(\"filterChange\", {\n        column: {},\n        values: [],\n        silent: true\n      });\n    }\n  };\n  const clearSort = () => {\n    if (!sortingColumn.value) return;\n    updateSort(null, null, null);\n    instance.store.commit(\"changeSortCondition\", {\n      silent: true\n    });\n  };\n  const {\n    setExpandRowKeys,\n    toggleRowExpansion,\n    updateExpandRows,\n    states: expandStates,\n    isRowExpanded\n  } = useExpand({\n    data,\n    rowKey\n  });\n  const {\n    updateTreeExpandKeys,\n    toggleTreeExpansion,\n    updateTreeData,\n    updateKeyChildren,\n    loadOrToggle,\n    states: treeStates\n  } = useTree({\n    data,\n    rowKey\n  });\n  const {\n    updateCurrentRowData,\n    updateCurrentRow,\n    setCurrentRowKey,\n    states: currentData\n  } = useCurrent({\n    data,\n    rowKey\n  });\n  const setExpandRowKeysAdapter = val => {\n    setExpandRowKeys(val);\n    updateTreeExpandKeys(val);\n  };\n  const toggleRowExpansionAdapter = (row, expanded) => {\n    const hasExpandColumn = columns.value.some(({\n      type\n    }) => type === \"expand\");\n    if (hasExpandColumn) {\n      toggleRowExpansion(row, expanded);\n    } else {\n      toggleTreeExpansion(row, expanded);\n    }\n  };\n  return {\n    assertRowKey,\n    updateColumns,\n    scheduleLayout,\n    isSelected,\n    clearSelection,\n    cleanSelection,\n    getSelectionRows,\n    toggleRowSelection,\n    _toggleAllSelection,\n    toggleAllSelection: null,\n    updateSelectionByRowKey,\n    updateAllSelected,\n    updateFilters,\n    updateCurrentRow,\n    updateSort,\n    execFilter,\n    execSort,\n    execQuery,\n    clearFilter,\n    clearSort,\n    toggleRowExpansion,\n    setExpandRowKeysAdapter,\n    setCurrentRowKey,\n    toggleRowExpansionAdapter,\n    isRowExpanded,\n    updateExpandRows,\n    updateCurrentRowData,\n    loadOrToggle,\n    updateTreeData,\n    updateKeyChildren,\n    states: {\n      tableSize,\n      rowKey,\n      data,\n      _data,\n      isComplex,\n      _columns,\n      originColumns,\n      columns,\n      fixedColumns,\n      rightFixedColumns,\n      leafColumns,\n      fixedLeafColumns,\n      rightFixedLeafColumns,\n      updateOrderFns,\n      leafColumnsLength,\n      fixedLeafColumnsLength,\n      rightFixedLeafColumnsLength,\n      isAllSelected,\n      selection,\n      reserveSelection,\n      selectOnIndeterminate,\n      selectable,\n      filters,\n      filteredData,\n      sortingColumn,\n      sortProp,\n      sortOrder,\n      hoverRow,\n      ...expandStates,\n      ...treeStates,\n      ...currentData\n    }\n  };\n}\nexport { useWatcher as default };", "map": {"version": 3, "names": ["sortData", "data", "states", "sortingColumn", "isString", "sortable", "orderBy", "sortProp", "sortOrder", "sortMethod", "sortBy", "doFlattenColumns", "columns", "result", "for<PERSON>ach", "column", "children", "length", "push", "apply", "useWatcher", "_a", "instance", "getCurrentInstance", "size", "tableSize", "toRefs", "proxy", "$props", "<PERSON><PERSON><PERSON>", "ref", "_data", "isComplex", "_columns", "originColumns", "fixedColumns", "rightFixedColumns", "leafColumns", "fixedLeafColumns", "rightFixedLeafColumns", "updateOrderFns", "leafColumns<PERSON>ength", "fixedLeafColumnsLength", "rightFixedLeafColumnsLength", "isAllSelected", "selection", "reserveSelection", "selectOnIndeterminate", "selectable", "filters", "filteredData", "hoverRow", "selectedMap", "computed", "value", "getKeysMap", "watch", "_a2", "state", "scheduleLayout", "needUpdateFixed", "props", "tableLayout", "refs", "tableHeaderRef", "updateFixedColumnStyle", "deep", "assertRowKey", "Error", "updateChildFixed", "childColumn", "fixed", "updateColumns", "filter", "includes", "selectColumn", "find", "type", "selectColFixLeft", "selectColumnIndex", "indexOf", "unshift", "notFixedColumns", "concat", "leafColumns2", "fixedLeafColumns2", "rightFixedLeafColumns2", "needUpdateColumns", "immediate", "doLayout", "debouncedUpdateLayout", "isSelected", "row", "getRowIdentity", "clearSelection", "oldSelection", "emit", "cleanSelection", "_b", "deleted", "<PERSON><PERSON><PERSON>", "store", "childrenColumnName", "dataMap", "key", "hasOwn", "item", "newSelection", "slice", "getSelectionRows", "toggleRowSelection", "selected", "emitChange", "ignoreSelectable", "_c", "_d", "treeProps", "checkStrictly", "changed", "toggleRowStatus", "_toggleAllSelection", "selectionChanged", "childrenCount", "rowKey2", "index", "rowIndex", "get<PERSON><PERSON><PERSON>n<PERSON>ount", "updateSelectionByRowKey", "rowId", "rowInfo", "updateAllSelected", "selectedCount", "checkSelectedStatus", "data2", "_a3", "isRowSelectable", "call", "isAllSelected_", "treeData", "count", "<PERSON><PERSON><PERSON>", "updateFilters", "columns2", "values", "isArray", "filters_", "col", "id", "column<PERSON>ey", "updateSort", "prop", "order", "execFilter", "sourceData", "unref", "Object", "keys", "columnId", "getColumnById", "filterMethod", "some", "execSort", "execQ<PERSON>y", "ignore", "clearFilter", "columnKeys", "panels", "assign", "filterPanels", "columns_", "map", "getColumnByKey", "filteredValue", "commit", "silent", "multi", "clearSort", "setExpandRowKeys", "toggleRowExpansion", "updateExpandRows", "expandStates", "isRowExpanded", "useExpand", "updateTreeExpandKeys", "toggleTreeExpansion", "updateTreeData", "update<PERSON>ey<PERSON><PERSON><PERSON>n", "loadOrToggle", "treeStates", "useTree", "updateCurrentRowData", "updateCurrentRow", "setCurrentRowKey", "currentData", "useCurrent", "setExpandRowKeysAdapter", "val", "toggleRowExpansionAdapter", "expanded", "hasExpandColumn", "toggleAllSelection"], "sources": ["../../../../../../../packages/components/table/src/store/watcher.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, getCurrentInstance, ref, toRefs, unref, watch } from 'vue'\nimport { hasOwn, isArray, isString } from '@element-plus/utils'\nimport {\n  getColumnById,\n  getColumnByKey,\n  getKeysMap,\n  getRowIdentity,\n  orderBy,\n  toggleRowStatus,\n} from '../util'\nimport useExpand from './expand'\nimport useCurrent from './current'\nimport useTree from './tree'\n\nimport type { Ref } from 'vue'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { DefaultRow, Table, TableRefs } from '../table/defaults'\nimport type { StoreFilter } from '.'\n\nconst sortData = (data, states) => {\n  const sortingColumn = states.sortingColumn\n  if (!sortingColumn || isString(sortingColumn.sortable)) {\n    return data\n  }\n  return orderBy(\n    data,\n    states.sortProp,\n    states.sortOrder,\n    sortingColumn.sortMethod,\n    sortingColumn.sortBy\n  )\n}\n\nconst doFlattenColumns = (columns) => {\n  const result = []\n  columns.forEach((column) => {\n    if (column.children && column.children.length > 0) {\n      // eslint-disable-next-line prefer-spread\n      result.push.apply(result, doFlattenColumns(column.children))\n    } else {\n      result.push(column)\n    }\n  })\n  return result\n}\n\nfunction useWatcher<T>() {\n  const instance = getCurrentInstance() as Table<T>\n  const { size: tableSize } = toRefs(instance.proxy?.$props as any)\n  const rowKey: Ref<string> = ref(null)\n  const data: Ref<T[]> = ref([])\n  const _data: Ref<T[]> = ref([])\n  const isComplex = ref(false)\n  const _columns: Ref<TableColumnCtx<T>[]> = ref([])\n  const originColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const columns: Ref<TableColumnCtx<T>[]> = ref([])\n  const fixedColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const rightFixedColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const leafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const fixedLeafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const rightFixedLeafColumns: Ref<TableColumnCtx<T>[]> = ref([])\n  const updateOrderFns: (() => void)[] = []\n  const leafColumnsLength = ref(0)\n  const fixedLeafColumnsLength = ref(0)\n  const rightFixedLeafColumnsLength = ref(0)\n  const isAllSelected = ref(false)\n  const selection: Ref<T[]> = ref([])\n  const reserveSelection = ref(false)\n  const selectOnIndeterminate = ref(false)\n  const selectable: Ref<(row: T, index: number) => boolean> = ref(null)\n  const filters: Ref<StoreFilter> = ref({})\n  const filteredData = ref(null)\n  const sortingColumn = ref(null)\n  const sortProp = ref(null)\n  const sortOrder = ref(null)\n  const hoverRow = ref(null)\n\n  const selectedMap = computed(() => {\n    return rowKey.value ? getKeysMap(selection.value, rowKey.value) : undefined\n  })\n\n  watch(\n    data,\n    () => {\n      if (instance.state) {\n        scheduleLayout(false)\n        const needUpdateFixed = instance.props.tableLayout === 'auto'\n        if (needUpdateFixed) {\n          instance.refs.tableHeaderRef?.updateFixedColumnStyle()\n        }\n      }\n    },\n    {\n      deep: true,\n    }\n  )\n\n  // 检查 rowKey 是否存在\n  const assertRowKey = () => {\n    if (!rowKey.value) throw new Error('[ElTable] prop row-key is required')\n  }\n\n  // 更新 fixed\n  const updateChildFixed = (column: TableColumnCtx<T>) => {\n    column.children?.forEach((childColumn) => {\n      childColumn.fixed = column.fixed\n      updateChildFixed(childColumn)\n    })\n  }\n\n  // 更新列\n  const updateColumns = () => {\n    _columns.value.forEach((column) => {\n      updateChildFixed(column)\n    })\n    fixedColumns.value = _columns.value.filter((column) =>\n      [true, 'left'].includes(column.fixed)\n    )\n\n    const selectColumn = _columns.value.find(\n      (column) => column.type === 'selection'\n    )\n\n    let selectColFixLeft\n    if (\n      selectColumn &&\n      selectColumn.fixed !== 'right' &&\n      !fixedColumns.value.includes(selectColumn)\n    ) {\n      const selectColumnIndex = _columns.value.indexOf(selectColumn)\n      if (selectColumnIndex === 0 && fixedColumns.value.length) {\n        fixedColumns.value.unshift(selectColumn)\n        selectColFixLeft = true\n      }\n    }\n\n    rightFixedColumns.value = _columns.value.filter(\n      (column) => column.fixed === 'right'\n    )\n\n    const notFixedColumns = _columns.value.filter(\n      (column) =>\n        (selectColFixLeft ? column.type !== 'selection' : true) && !column.fixed\n    )\n\n    originColumns.value = []\n      .concat(fixedColumns.value)\n      .concat(notFixedColumns)\n      .concat(rightFixedColumns.value)\n    const leafColumns = doFlattenColumns(notFixedColumns)\n    const fixedLeafColumns = doFlattenColumns(fixedColumns.value)\n    const rightFixedLeafColumns = doFlattenColumns(rightFixedColumns.value)\n\n    leafColumnsLength.value = leafColumns.length\n    fixedLeafColumnsLength.value = fixedLeafColumns.length\n    rightFixedLeafColumnsLength.value = rightFixedLeafColumns.length\n\n    columns.value = []\n      .concat(fixedLeafColumns)\n      .concat(leafColumns)\n      .concat(rightFixedLeafColumns)\n    isComplex.value =\n      fixedColumns.value.length > 0 || rightFixedColumns.value.length > 0\n  }\n\n  // 更新 DOM\n  const scheduleLayout = (needUpdateColumns?: boolean, immediate = false) => {\n    if (needUpdateColumns) {\n      updateColumns()\n    }\n    if (immediate) {\n      instance.state.doLayout()\n    } else {\n      instance.state.debouncedUpdateLayout()\n    }\n  }\n\n  // 选择\n  const isSelected = (row: DefaultRow) => {\n    if (selectedMap.value) {\n      return !!selectedMap.value[getRowIdentity(row, rowKey.value)]\n    } else {\n      return selection.value.includes(row)\n    }\n  }\n\n  const clearSelection = () => {\n    isAllSelected.value = false\n    const oldSelection = selection.value\n    selection.value = []\n    if (oldSelection.length) {\n      instance.emit('selection-change', [])\n    }\n  }\n\n  const cleanSelection = () => {\n    let deleted\n    if (rowKey.value) {\n      deleted = []\n      const childrenKey = instance?.store?.states?.childrenColumnName.value\n      const dataMap = getKeysMap(data.value, rowKey.value, true, childrenKey)\n      for (const key in selectedMap.value) {\n        if (hasOwn(selectedMap.value, key) && !dataMap[key]) {\n          deleted.push(selectedMap.value[key].row)\n        }\n      }\n    } else {\n      deleted = selection.value.filter((item) => !data.value.includes(item))\n    }\n    if (deleted.length) {\n      const newSelection = selection.value.filter(\n        (item) => !deleted.includes(item)\n      )\n      selection.value = newSelection\n      instance.emit('selection-change', newSelection.slice())\n    }\n  }\n\n  const getSelectionRows = () => {\n    return (selection.value || []).slice()\n  }\n\n  const toggleRowSelection = (\n    row: T,\n    selected?: boolean,\n    emitChange = true,\n    ignoreSelectable = false\n  ) => {\n    const treeProps = {\n      children: instance?.store?.states?.childrenColumnName.value,\n      checkStrictly: instance?.store?.states?.checkStrictly.value,\n    }\n    const changed = toggleRowStatus(\n      selection.value,\n      row,\n      selected,\n      treeProps,\n      ignoreSelectable ? undefined : selectable.value,\n      data.value.indexOf(row)\n    )\n    if (changed) {\n      const newSelection = (selection.value || []).slice()\n      // 调用 API 修改选中值，不触发 select 事件\n      if (emitChange) {\n        instance.emit('select', newSelection, row)\n      }\n      instance.emit('selection-change', newSelection)\n    }\n  }\n\n  const _toggleAllSelection = () => {\n    // when only some rows are selected (but not all), select or deselect all of them\n    // depending on the value of selectOnIndeterminate\n    const value = selectOnIndeterminate.value\n      ? !isAllSelected.value\n      : !(isAllSelected.value || selection.value.length)\n    isAllSelected.value = value\n\n    let selectionChanged = false\n    let childrenCount = 0\n    const rowKey = instance?.store?.states?.rowKey.value\n    const { childrenColumnName } = instance.store.states\n    const treeProps = {\n      children: childrenColumnName.value,\n      checkStrictly: false, // Disable checkStrictly when selecting all\n    }\n\n    data.value.forEach((row, index) => {\n      const rowIndex = index + childrenCount\n      if (\n        toggleRowStatus(\n          selection.value,\n          row,\n          value,\n          treeProps,\n          selectable.value,\n          rowIndex\n        )\n      ) {\n        selectionChanged = true\n      }\n      childrenCount += getChildrenCount(getRowIdentity(row, rowKey))\n    })\n\n    if (selectionChanged) {\n      instance.emit(\n        'selection-change',\n        selection.value ? selection.value.slice() : []\n      )\n    }\n    instance.emit('select-all', (selection.value || []).slice())\n  }\n\n  const updateSelectionByRowKey = () => {\n    data.value.forEach((row) => {\n      const rowId = getRowIdentity(row, rowKey.value)\n      const rowInfo = selectedMap.value![rowId]\n      if (rowInfo) {\n        selection.value[rowInfo.index] = row\n      }\n    })\n  }\n\n  const updateAllSelected = () => {\n    // data 为 null 时，解构时的默认值会被忽略\n    if (data.value?.length === 0) {\n      isAllSelected.value = false\n      return\n    }\n\n    const { childrenColumnName } = instance.store.states\n    let rowIndex = 0\n    let selectedCount = 0\n\n    const checkSelectedStatus = (data: DefaultRow[]) => {\n      for (const row of data) {\n        const isRowSelectable =\n          selectable.value && selectable.value.call(null, row, rowIndex)\n\n        if (!isSelected(row)) {\n          if (!selectable.value || isRowSelectable) {\n            return false\n          }\n        } else {\n          selectedCount++\n        }\n        rowIndex++\n\n        if (\n          row[childrenColumnName.value]?.length &&\n          !checkSelectedStatus(row[childrenColumnName.value])\n        ) {\n          return false\n        }\n      }\n      return true\n    }\n\n    const isAllSelected_ = checkSelectedStatus(data.value || [])\n    isAllSelected.value = selectedCount === 0 ? false : isAllSelected_\n  }\n\n  const getChildrenCount = (rowKey: string) => {\n    if (!instance || !instance.store) return 0\n    const { treeData } = instance.store.states\n    let count = 0\n    const children = treeData.value[rowKey]?.children\n    if (children) {\n      count += children.length\n      children.forEach((childKey) => {\n        count += getChildrenCount(childKey)\n      })\n    }\n    return count\n  }\n\n  // 过滤与排序\n  const updateFilters = (columns, values) => {\n    if (!isArray(columns)) {\n      columns = [columns]\n    }\n    const filters_ = {}\n    columns.forEach((col) => {\n      filters.value[col.id] = values\n      filters_[col.columnKey || col.id] = values\n    })\n    return filters_\n  }\n\n  const updateSort = (column, prop, order) => {\n    if (sortingColumn.value && sortingColumn.value !== column) {\n      sortingColumn.value.order = null\n    }\n    sortingColumn.value = column\n    sortProp.value = prop\n    sortOrder.value = order\n  }\n\n  const execFilter = () => {\n    let sourceData = unref(_data)\n    Object.keys(filters.value).forEach((columnId) => {\n      const values = filters.value[columnId]\n      if (!values || values.length === 0) return\n      const column = getColumnById(\n        {\n          columns: columns.value,\n        },\n        columnId\n      )\n      if (column && column.filterMethod) {\n        sourceData = sourceData.filter((row) => {\n          return values.some((value) =>\n            column.filterMethod.call(null, value, row, column)\n          )\n        })\n      }\n    })\n\n    filteredData.value = sourceData\n  }\n\n  const execSort = () => {\n    data.value = sortData(filteredData.value, {\n      sortingColumn: sortingColumn.value,\n      sortProp: sortProp.value,\n      sortOrder: sortOrder.value,\n    })\n  }\n\n  // 根据 filters 与 sort 去过滤 data\n  const execQuery = (ignore = undefined) => {\n    if (!(ignore && ignore.filter)) {\n      execFilter()\n    }\n    execSort()\n  }\n\n  const clearFilter = (columnKeys) => {\n    const { tableHeaderRef } = instance.refs as TableRefs\n    if (!tableHeaderRef) return\n    const panels = Object.assign({}, tableHeaderRef.filterPanels)\n\n    const keys = Object.keys(panels)\n    if (!keys.length) return\n\n    if (isString(columnKeys)) {\n      columnKeys = [columnKeys]\n    }\n\n    if (isArray(columnKeys)) {\n      const columns_ = columnKeys.map((key) =>\n        getColumnByKey(\n          {\n            columns: columns.value,\n          },\n          key\n        )\n      )\n      keys.forEach((key) => {\n        const column = columns_.find((col) => col.id === key)\n        if (column) {\n          column.filteredValue = []\n        }\n      })\n      instance.store.commit('filterChange', {\n        column: columns_,\n        values: [],\n        silent: true,\n        multi: true,\n      })\n    } else {\n      keys.forEach((key) => {\n        const column = columns.value.find((col) => col.id === key)\n        if (column) {\n          column.filteredValue = []\n        }\n      })\n\n      filters.value = {}\n      instance.store.commit('filterChange', {\n        column: {},\n        values: [],\n        silent: true,\n      })\n    }\n  }\n\n  const clearSort = () => {\n    if (!sortingColumn.value) return\n\n    updateSort(null, null, null)\n    instance.store.commit('changeSortCondition', {\n      silent: true,\n    })\n  }\n  const {\n    setExpandRowKeys,\n    toggleRowExpansion,\n    updateExpandRows,\n    states: expandStates,\n    isRowExpanded,\n  } = useExpand({\n    data,\n    rowKey,\n  })\n  const {\n    updateTreeExpandKeys,\n    toggleTreeExpansion,\n    updateTreeData,\n    updateKeyChildren,\n    loadOrToggle,\n    states: treeStates,\n  } = useTree({\n    data,\n    rowKey,\n  })\n  const {\n    updateCurrentRowData,\n    updateCurrentRow,\n    setCurrentRowKey,\n    states: currentData,\n  } = useCurrent({\n    data,\n    rowKey,\n  })\n  // 适配层，expand-row-keys 在 Expand 与 TreeTable 中都有使用\n  const setExpandRowKeysAdapter = (val: string[]) => {\n    // 这里会触发额外的计算，但为了兼容性，暂时这么做\n    setExpandRowKeys(val)\n    updateTreeExpandKeys(val)\n  }\n\n  // 展开行与 TreeTable 都要使用\n  const toggleRowExpansionAdapter = (row: T, expanded?: boolean) => {\n    const hasExpandColumn = columns.value.some(({ type }) => type === 'expand')\n    if (hasExpandColumn) {\n      toggleRowExpansion(row, expanded)\n    } else {\n      toggleTreeExpansion(row, expanded)\n    }\n  }\n\n  return {\n    assertRowKey,\n    updateColumns,\n    scheduleLayout,\n    isSelected,\n    clearSelection,\n    cleanSelection,\n    getSelectionRows,\n    toggleRowSelection,\n    _toggleAllSelection,\n    toggleAllSelection: null,\n    updateSelectionByRowKey,\n    updateAllSelected,\n    updateFilters,\n    updateCurrentRow,\n    updateSort,\n    execFilter,\n    execSort,\n    execQuery,\n    clearFilter,\n    clearSort,\n    toggleRowExpansion,\n    setExpandRowKeysAdapter,\n    setCurrentRowKey,\n    toggleRowExpansionAdapter,\n    isRowExpanded,\n    updateExpandRows,\n    updateCurrentRowData,\n    loadOrToggle,\n    updateTreeData,\n    updateKeyChildren,\n    states: {\n      tableSize,\n      rowKey,\n      data,\n      _data,\n      isComplex,\n      _columns,\n      originColumns,\n      columns,\n      fixedColumns,\n      rightFixedColumns,\n      leafColumns,\n      fixedLeafColumns,\n      rightFixedLeafColumns,\n      updateOrderFns,\n      leafColumnsLength,\n      fixedLeafColumnsLength,\n      rightFixedLeafColumnsLength,\n      isAllSelected,\n      selection,\n      reserveSelection,\n      selectOnIndeterminate,\n      selectable,\n      filters,\n      filteredData,\n      sortingColumn,\n      sortProp,\n      sortOrder,\n      hoverRow,\n      ...expandStates,\n      ...treeStates,\n      ...currentData,\n    },\n  }\n}\n\nexport default useWatcher\n"], "mappings": ";;;;;;;;;;;;;AAaA,MAAMA,QAAQ,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;EACjC,MAAMC,aAAa,GAAGD,MAAM,CAACC,aAAa;EAC1C,IAAI,CAACA,aAAa,IAAIC,QAAQ,CAACD,aAAa,CAACE,QAAQ,CAAC,EAAE;IACtD,OAAOJ,IAAI;EACf;EACE,OAAOK,OAAO,CAACL,IAAI,EAAEC,MAAM,CAACK,QAAQ,EAAEL,MAAM,CAACM,SAAS,EAAEL,aAAa,CAACM,UAAU,EAAEN,aAAa,CAACO,MAAM,CAAC;AACzG,CAAC;AACD,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;EACpC,MAAMC,MAAM,GAAG,EAAE;EACjBD,OAAO,CAACE,OAAO,CAAEC,MAAM,IAAK;IAC1B,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACjDJ,MAAM,CAACK,IAAI,CAACC,KAAK,CAACN,MAAM,EAAEF,gBAAgB,CAACI,MAAM,CAACC,QAAQ,CAAC,CAAC;IAClE,CAAK,MAAM;MACLH,MAAM,CAACK,IAAI,CAACH,MAAM,CAAC;IACzB;EACA,CAAG,CAAC;EACF,OAAOF,MAAM;AACf,CAAC;AACD,SAASO,UAAUA,CAAA,EAAG;EACpB,IAAIC,EAAE;EACN,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAM;IAAEC,IAAI,EAAEC;EAAS,CAAE,GAAGC,MAAM,CAAC,CAACL,EAAE,GAAGC,QAAQ,CAACK,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGN,EAAE,CAACO,MAAM,CAAC;EACtF,MAAMC,MAAM,GAAGC,GAAG,CAAC,IAAI,CAAC;EACxB,MAAM7B,IAAI,GAAG6B,GAAG,CAAC,EAAE,CAAC;EACpB,MAAMC,KAAK,GAAGD,GAAG,CAAC,EAAE,CAAC;EACrB,MAAME,SAAS,GAAGF,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAMG,QAAQ,GAAGH,GAAG,CAAC,EAAE,CAAC;EACxB,MAAMI,aAAa,GAAGJ,GAAG,CAAC,EAAE,CAAC;EAC7B,MAAMlB,OAAO,GAAGkB,GAAG,CAAC,EAAE,CAAC;EACvB,MAAMK,YAAY,GAAGL,GAAG,CAAC,EAAE,CAAC;EAC5B,MAAMM,iBAAiB,GAAGN,GAAG,CAAC,EAAE,CAAC;EACjC,MAAMO,WAAW,GAAGP,GAAG,CAAC,EAAE,CAAC;EAC3B,MAAMQ,gBAAgB,GAAGR,GAAG,CAAC,EAAE,CAAC;EAChC,MAAMS,qBAAqB,GAAGT,GAAG,CAAC,EAAE,CAAC;EACrC,MAAMU,cAAc,GAAG,EAAE;EACzB,MAAMC,iBAAiB,GAAGX,GAAG,CAAC,CAAC,CAAC;EAChC,MAAMY,sBAAsB,GAAGZ,GAAG,CAAC,CAAC,CAAC;EACrC,MAAMa,2BAA2B,GAAGb,GAAG,CAAC,CAAC,CAAC;EAC1C,MAAMc,aAAa,GAAGd,GAAG,CAAC,KAAK,CAAC;EAChC,MAAMe,SAAS,GAAGf,GAAG,CAAC,EAAE,CAAC;EACzB,MAAMgB,gBAAgB,GAAGhB,GAAG,CAAC,KAAK,CAAC;EACnC,MAAMiB,qBAAqB,GAAGjB,GAAG,CAAC,KAAK,CAAC;EACxC,MAAMkB,UAAU,GAAGlB,GAAG,CAAC,IAAI,CAAC;EAC5B,MAAMmB,OAAO,GAAGnB,GAAG,CAAC,EAAE,CAAC;EACvB,MAAMoB,YAAY,GAAGpB,GAAG,CAAC,IAAI,CAAC;EAC9B,MAAM3B,aAAa,GAAG2B,GAAG,CAAC,IAAI,CAAC;EAC/B,MAAMvB,QAAQ,GAAGuB,GAAG,CAAC,IAAI,CAAC;EAC1B,MAAMtB,SAAS,GAAGsB,GAAG,CAAC,IAAI,CAAC;EAC3B,MAAMqB,QAAQ,GAAGrB,GAAG,CAAC,IAAI,CAAC;EAC1B,MAAMsB,WAAW,GAAGC,QAAQ,CAAC,MAAM;IACjC,OAAOxB,MAAM,CAACyB,KAAK,GAAGC,UAAU,CAACV,SAAS,CAACS,KAAK,EAAEzB,MAAM,CAACyB,KAAK,CAAC,GAAG,KAAK,CAAC;EAC5E,CAAG,CAAC;EACFE,KAAK,CAACvD,IAAI,EAAE,MAAM;IAChB,IAAIwD,GAAG;IACP,IAAInC,QAAQ,CAACoC,KAAK,EAAE;MAClBC,cAAc,CAAC,KAAK,CAAC;MACrB,MAAMC,eAAe,GAAGtC,QAAQ,CAACuC,KAAK,CAACC,WAAW,KAAK,MAAM;MAC7D,IAAIF,eAAe,EAAE;QACnB,CAACH,GAAG,GAAGnC,QAAQ,CAACyC,IAAI,CAACC,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,GAAG,CAACQ,sBAAsB,EAAE;MAC5F;IACA;EACA,CAAG,EAAE;IACDC,IAAI,EAAE;EACV,CAAG,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACtC,MAAM,CAACyB,KAAK,EACf,MAAM,IAAIc,KAAK,CAAC,oCAAoC,CAAC;EAC3D,CAAG;EACD,MAAMC,gBAAgB,GAAItD,MAAM,IAAK;IACnC,IAAI0C,GAAG;IACP,CAACA,GAAG,GAAG1C,MAAM,CAACC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyC,GAAG,CAAC3C,OAAO,CAAEwD,WAAW,IAAK;MACtEA,WAAW,CAACC,KAAK,GAAGxD,MAAM,CAACwD,KAAK;MAChCF,gBAAgB,CAACC,WAAW,CAAC;IACnC,CAAK,CAAC;EACN,CAAG;EACD,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1BvC,QAAQ,CAACqB,KAAK,CAACxC,OAAO,CAAEC,MAAM,IAAK;MACjCsD,gBAAgB,CAACtD,MAAM,CAAC;IAC9B,CAAK,CAAC;IACFoB,YAAY,CAACmB,KAAK,GAAGrB,QAAQ,CAACqB,KAAK,CAACmB,MAAM,CAAE1D,MAAM,IAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC2D,QAAQ,CAAC3D,MAAM,CAACwD,KAAK,CAAC,CAAC;IAC7F,MAAMI,YAAY,GAAG1C,QAAQ,CAACqB,KAAK,CAACsB,IAAI,CAAE7D,MAAM,IAAKA,MAAM,CAAC8D,IAAI,KAAK,WAAW,CAAC;IACjF,IAAIC,gBAAgB;IACpB,IAAIH,YAAY,IAAIA,YAAY,CAACJ,KAAK,KAAK,OAAO,IAAI,CAACpC,YAAY,CAACmB,KAAK,CAACoB,QAAQ,CAACC,YAAY,CAAC,EAAE;MAChG,MAAMI,iBAAiB,GAAG9C,QAAQ,CAACqB,KAAK,CAAC0B,OAAO,CAACL,YAAY,CAAC;MAC9D,IAAII,iBAAiB,KAAK,CAAC,IAAI5C,YAAY,CAACmB,KAAK,CAACrC,MAAM,EAAE;QACxDkB,YAAY,CAACmB,KAAK,CAAC2B,OAAO,CAACN,YAAY,CAAC;QACxCG,gBAAgB,GAAG,IAAI;MAC/B;IACA;IACI1C,iBAAiB,CAACkB,KAAK,GAAGrB,QAAQ,CAACqB,KAAK,CAACmB,MAAM,CAAE1D,MAAM,IAAKA,MAAM,CAACwD,KAAK,KAAK,OAAO,CAAC;IACrF,MAAMW,eAAe,GAAGjD,QAAQ,CAACqB,KAAK,CAACmB,MAAM,CAAE1D,MAAM,IAAK,CAAC+D,gBAAgB,GAAG/D,MAAM,CAAC8D,IAAI,KAAK,WAAW,GAAG,IAAI,KAAK,CAAC9D,MAAM,CAACwD,KAAK,CAAC;IACnIrC,aAAa,CAACoB,KAAK,GAAG,EAAE,CAAC6B,MAAM,CAAChD,YAAY,CAACmB,KAAK,CAAC,CAAC6B,MAAM,CAACD,eAAe,CAAC,CAACC,MAAM,CAAC/C,iBAAiB,CAACkB,KAAK,CAAC;IAC3G,MAAM8B,YAAY,GAAGzE,gBAAgB,CAACuE,eAAe,CAAC;IACtD,MAAMG,iBAAiB,GAAG1E,gBAAgB,CAACwB,YAAY,CAACmB,KAAK,CAAC;IAC9D,MAAMgC,sBAAsB,GAAG3E,gBAAgB,CAACyB,iBAAiB,CAACkB,KAAK,CAAC;IACxEb,iBAAiB,CAACa,KAAK,GAAG8B,YAAY,CAACnE,MAAM;IAC7CyB,sBAAsB,CAACY,KAAK,GAAG+B,iBAAiB,CAACpE,MAAM;IACvD0B,2BAA2B,CAACW,KAAK,GAAGgC,sBAAsB,CAACrE,MAAM;IACjEL,OAAO,CAAC0C,KAAK,GAAG,EAAE,CAAC6B,MAAM,CAACE,iBAAiB,CAAC,CAACF,MAAM,CAACC,YAAY,CAAC,CAACD,MAAM,CAACG,sBAAsB,CAAC;IAChGtD,SAAS,CAACsB,KAAK,GAAGnB,YAAY,CAACmB,KAAK,CAACrC,MAAM,GAAG,CAAC,IAAImB,iBAAiB,CAACkB,KAAK,CAACrC,MAAM,GAAG,CAAC;EACzF,CAAG;EACD,MAAM0C,cAAc,GAAGA,CAAC4B,iBAAiB,EAAEC,SAAS,GAAG,KAAK,KAAK;IAC/D,IAAID,iBAAiB,EAAE;MACrBf,aAAa,EAAE;IACrB;IACI,IAAIgB,SAAS,EAAE;MACblE,QAAQ,CAACoC,KAAK,CAAC+B,QAAQ,EAAE;IAC/B,CAAK,MAAM;MACLnE,QAAQ,CAACoC,KAAK,CAACgC,qBAAqB,EAAE;IAC5C;EACA,CAAG;EACD,MAAMC,UAAU,GAAIC,GAAG,IAAK;IAC1B,IAAIxC,WAAW,CAACE,KAAK,EAAE;MACrB,OAAO,CAAC,CAACF,WAAW,CAACE,KAAK,CAACuC,cAAc,CAACD,GAAG,EAAE/D,MAAM,CAACyB,KAAK,CAAC,CAAC;IACnE,CAAK,MAAM;MACL,OAAOT,SAAS,CAACS,KAAK,CAACoB,QAAQ,CAACkB,GAAG,CAAC;IAC1C;EACA,CAAG;EACD,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BlD,aAAa,CAACU,KAAK,GAAG,KAAK;IAC3B,MAAMyC,YAAY,GAAGlD,SAAS,CAACS,KAAK;IACpCT,SAAS,CAACS,KAAK,GAAG,EAAE;IACpB,IAAIyC,YAAY,CAAC9E,MAAM,EAAE;MACvBK,QAAQ,CAAC0E,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;IAC3C;EACA,CAAG;EACD,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxC,GAAG,EAAEyC,EAAE;IACX,IAAIC,OAAO;IACX,IAAItE,MAAM,CAACyB,KAAK,EAAE;MAChB6C,OAAO,GAAG,EAAE;MACZ,MAAMC,WAAW,GAAG,CAACF,EAAE,GAAG,CAACzC,GAAG,GAAGnC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+E,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG5C,GAAG,CAACvD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgG,EAAE,CAACI,kBAAkB,CAAChD,KAAK;MAC1J,MAAMiD,OAAO,GAAGhD,UAAU,CAACtD,IAAI,CAACqD,KAAK,EAAEzB,MAAM,CAACyB,KAAK,EAAE,IAAI,EAAE8C,WAAW,CAAC;MACvE,KAAK,MAAMI,GAAG,IAAIpD,WAAW,CAACE,KAAK,EAAE;QACnC,IAAImD,MAAM,CAACrD,WAAW,CAACE,KAAK,EAAEkD,GAAG,CAAC,IAAI,CAACD,OAAO,CAACC,GAAG,CAAC,EAAE;UACnDL,OAAO,CAACjF,IAAI,CAACkC,WAAW,CAACE,KAAK,CAACkD,GAAG,CAAC,CAACZ,GAAG,CAAC;QAClD;MACA;IACA,CAAK,MAAM;MACLO,OAAO,GAAGtD,SAAS,CAACS,KAAK,CAACmB,MAAM,CAAEiC,IAAI,IAAK,CAACzG,IAAI,CAACqD,KAAK,CAACoB,QAAQ,CAACgC,IAAI,CAAC,CAAC;IAC5E;IACI,IAAIP,OAAO,CAAClF,MAAM,EAAE;MAClB,MAAM0F,YAAY,GAAG9D,SAAS,CAACS,KAAK,CAACmB,MAAM,CAAEiC,IAAI,IAAK,CAACP,OAAO,CAACzB,QAAQ,CAACgC,IAAI,CAAC,CAAC;MAC9E7D,SAAS,CAACS,KAAK,GAAGqD,YAAY;MAC9BrF,QAAQ,CAAC0E,IAAI,CAAC,kBAAkB,EAAEW,YAAY,CAACC,KAAK,EAAE,CAAC;IAC7D;EACA,CAAG;EACD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO,CAAChE,SAAS,CAACS,KAAK,IAAI,EAAE,EAAEsD,KAAK,EAAE;EAC1C,CAAG;EACD,MAAME,kBAAkB,GAAGA,CAAClB,GAAG,EAAEmB,QAAQ,EAAEC,UAAU,GAAG,IAAI,EAAEC,gBAAgB,GAAG,KAAK,KAAK;IACzF,IAAIxD,GAAG,EAAEyC,EAAE,EAAEgB,EAAE,EAAEC,EAAE;IACnB,MAAMC,SAAS,GAAG;MAChBpG,QAAQ,EAAE,CAACkF,EAAE,GAAG,CAACzC,GAAG,GAAGnC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+E,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG5C,GAAG,CAACvD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgG,EAAE,CAACI,kBAAkB,CAAChD,KAAK;MAChJ+D,aAAa,EAAE,CAACF,EAAE,GAAG,CAACD,EAAE,GAAG5F,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+E,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGa,EAAE,CAAChH,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiH,EAAE,CAACE,aAAa,CAAC/D;IAC/I,CAAK;IACD,MAAMgE,OAAO,GAAGC,eAAe,CAAC1E,SAAS,CAACS,KAAK,EAAEsC,GAAG,EAAEmB,QAAQ,EAAEK,SAAS,EAAEH,gBAAgB,GAAG,KAAK,CAAC,GAAGjE,UAAU,CAACM,KAAK,EAAErD,IAAI,CAACqD,KAAK,CAAC0B,OAAO,CAACY,GAAG,CAAC,CAAC;IACjJ,IAAI0B,OAAO,EAAE;MACX,MAAMX,YAAY,GAAG,CAAC9D,SAAS,CAACS,KAAK,IAAI,EAAE,EAAEsD,KAAK,EAAE;MACpD,IAAII,UAAU,EAAE;QACd1F,QAAQ,CAAC0E,IAAI,CAAC,QAAQ,EAAEW,YAAY,EAAEf,GAAG,CAAC;MAClD;MACMtE,QAAQ,CAAC0E,IAAI,CAAC,kBAAkB,EAAEW,YAAY,CAAC;IACrD;EACA,CAAG;EACD,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI/D,GAAG,EAAEyC,EAAE;IACX,MAAM5C,KAAK,GAAGP,qBAAqB,CAACO,KAAK,GAAG,CAACV,aAAa,CAACU,KAAK,GAAG,EAAEV,aAAa,CAACU,KAAK,IAAIT,SAAS,CAACS,KAAK,CAACrC,MAAM,CAAC;IACnH2B,aAAa,CAACU,KAAK,GAAGA,KAAK;IAC3B,IAAImE,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,OAAO,GAAG,CAACzB,EAAE,GAAG,CAACzC,GAAG,GAAGnC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC+E,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG5C,GAAG,CAACvD,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgG,EAAE,CAACrE,MAAM,CAACyB,KAAK;IAC1I,MAAM;MAAEgD;IAAkB,CAAE,GAAGhF,QAAQ,CAAC+E,KAAK,CAACnG,MAAM;IACpD,MAAMkH,SAAS,GAAG;MAChBpG,QAAQ,EAAEsF,kBAAkB,CAAChD,KAAK;MAClC+D,aAAa,EAAE;IACrB,CAAK;IACDpH,IAAI,CAACqD,KAAK,CAACxC,OAAO,CAAC,CAAC8E,GAAG,EAAEgC,KAAK,KAAK;MACjC,MAAMC,QAAQ,GAAGD,KAAK,GAAGF,aAAa;MACtC,IAAIH,eAAe,CAAC1E,SAAS,CAACS,KAAK,EAAEsC,GAAG,EAAEtC,KAAK,EAAE8D,SAAS,EAAEpE,UAAU,CAACM,KAAK,EAAEuE,QAAQ,CAAC,EAAE;QACvFJ,gBAAgB,GAAG,IAAI;MAC/B;MACMC,aAAa,IAAII,gBAAgB,CAACjC,cAAc,CAACD,GAAG,EAAE+B,OAAO,CAAC,CAAC;IACrE,CAAK,CAAC;IACF,IAAIF,gBAAgB,EAAE;MACpBnG,QAAQ,CAAC0E,IAAI,CAAC,kBAAkB,EAAEnD,SAAS,CAACS,KAAK,GAAGT,SAAS,CAACS,KAAK,CAACsD,KAAK,EAAE,GAAG,EAAE,CAAC;IACvF;IACItF,QAAQ,CAAC0E,IAAI,CAAC,YAAY,EAAE,CAACnD,SAAS,CAACS,KAAK,IAAI,EAAE,EAAEsD,KAAK,EAAE,CAAC;EAChE,CAAG;EACD,MAAMmB,uBAAuB,GAAGA,CAAA,KAAM;IACpC9H,IAAI,CAACqD,KAAK,CAACxC,OAAO,CAAE8E,GAAG,IAAK;MAC1B,MAAMoC,KAAK,GAAGnC,cAAc,CAACD,GAAG,EAAE/D,MAAM,CAACyB,KAAK,CAAC;MAC/C,MAAM2E,OAAO,GAAG7E,WAAW,CAACE,KAAK,CAAC0E,KAAK,CAAC;MACxC,IAAIC,OAAO,EAAE;QACXpF,SAAS,CAACS,KAAK,CAAC2E,OAAO,CAACL,KAAK,CAAC,GAAGhC,GAAG;MAC5C;IACA,CAAK,CAAC;EACN,CAAG;EACD,MAAMsC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIzE,GAAG;IACP,IAAI,CAAC,CAACA,GAAG,GAAGxD,IAAI,CAACqD,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,GAAG,CAACxC,MAAM,MAAM,CAAC,EAAE;MAC5D2B,aAAa,CAACU,KAAK,GAAG,KAAK;MAC3B;IACN;IACI,MAAM;MAAEgD;IAAkB,CAAE,GAAGhF,QAAQ,CAAC+E,KAAK,CAACnG,MAAM;IACpD,IAAI2H,QAAQ,GAAG,CAAC;IAChB,IAAIM,aAAa,GAAG,CAAC;IACrB,MAAMC,mBAAmB,GAAIC,KAAK,IAAK;MACrC,IAAIC,GAAG;MACP,KAAK,MAAM1C,GAAG,IAAIyC,KAAK,EAAE;QACvB,MAAME,eAAe,GAAGvF,UAAU,CAACM,KAAK,IAAIN,UAAU,CAACM,KAAK,CAACkF,IAAI,CAAC,IAAI,EAAE5C,GAAG,EAAEiC,QAAQ,CAAC;QACtF,IAAI,CAAClC,UAAU,CAACC,GAAG,CAAC,EAAE;UACpB,IAAI,CAAC5C,UAAU,CAACM,KAAK,IAAIiF,eAAe,EAAE;YACxC,OAAO,KAAK;UACxB;QACA,CAAS,MAAM;UACLJ,aAAa,EAAE;QACzB;QACQN,QAAQ,EAAE;QACV,IAAI,CAAC,CAACS,GAAG,GAAG1C,GAAG,CAACU,kBAAkB,CAAChD,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgF,GAAG,CAACrH,MAAM,KAAK,CAACmH,mBAAmB,CAACxC,GAAG,CAACU,kBAAkB,CAAChD,KAAK,CAAC,CAAC,EAAE;UAChI,OAAO,KAAK;QACtB;MACA;MACM,OAAO,IAAI;IACjB,CAAK;IACD,MAAMmF,cAAc,GAAGL,mBAAmB,CAACnI,IAAI,CAACqD,KAAK,IAAI,EAAE,CAAC;IAC5DV,aAAa,CAACU,KAAK,GAAG6E,aAAa,KAAK,CAAC,GAAG,KAAK,GAAGM,cAAc;EACtE,CAAG;EACD,MAAMX,gBAAgB,GAAIH,OAAO,IAAK;IACpC,IAAIlE,GAAG;IACP,IAAI,CAACnC,QAAQ,IAAI,CAACA,QAAQ,CAAC+E,KAAK,EAC9B,OAAO,CAAC;IACV,MAAM;MAAEqC;IAAQ,CAAE,GAAGpH,QAAQ,CAAC+E,KAAK,CAACnG,MAAM;IAC1C,IAAIyI,KAAK,GAAG,CAAC;IACb,MAAM3H,QAAQ,GAAG,CAACyC,GAAG,GAAGiF,QAAQ,CAACpF,KAAK,CAACqE,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlE,GAAG,CAACzC,QAAQ;IAChF,IAAIA,QAAQ,EAAE;MACZ2H,KAAK,IAAI3H,QAAQ,CAACC,MAAM;MACxBD,QAAQ,CAACF,OAAO,CAAE8H,QAAQ,IAAK;QAC7BD,KAAK,IAAIb,gBAAgB,CAACc,QAAQ,CAAC;MAC3C,CAAO,CAAC;IACR;IACI,OAAOD,KAAK;EAChB,CAAG;EACD,MAAME,aAAa,GAAGA,CAACC,QAAQ,EAAEC,MAAM,KAAK;IAC1C,IAAI,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;MACtBA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC3B;IACI,MAAMG,QAAQ,GAAG,EAAE;IACnBH,QAAQ,CAAChI,OAAO,CAAEoI,GAAG,IAAK;MACxBjG,OAAO,CAACK,KAAK,CAAC4F,GAAG,CAACC,EAAE,CAAC,GAAGJ,MAAM;MAC9BE,QAAQ,CAACC,GAAG,CAACE,SAAS,IAAIF,GAAG,CAACC,EAAE,CAAC,GAAGJ,MAAM;IAChD,CAAK,CAAC;IACF,OAAOE,QAAQ;EACnB,CAAG;EACD,MAAMI,UAAU,GAAGA,CAACtI,MAAM,EAAEuI,IAAI,EAAEC,KAAK,KAAK;IAC1C,IAAIpJ,aAAa,CAACmD,KAAK,IAAInD,aAAa,CAACmD,KAAK,KAAKvC,MAAM,EAAE;MACzDZ,aAAa,CAACmD,KAAK,CAACiG,KAAK,GAAG,IAAI;IACtC;IACIpJ,aAAa,CAACmD,KAAK,GAAGvC,MAAM;IAC5BR,QAAQ,CAAC+C,KAAK,GAAGgG,IAAI;IACrB9I,SAAS,CAAC8C,KAAK,GAAGiG,KAAK;EAC3B,CAAG;EACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIC,UAAU,GAAGC,KAAK,CAAC3H,KAAK,CAAC;IAC7B4H,MAAM,CAACC,IAAI,CAAC3G,OAAO,CAACK,KAAK,CAAC,CAACxC,OAAO,CAAE+I,QAAQ,IAAK;MAC/C,MAAMd,MAAM,GAAG9F,OAAO,CAACK,KAAK,CAACuG,QAAQ,CAAC;MACtC,IAAI,CAACd,MAAM,IAAIA,MAAM,CAAC9H,MAAM,KAAK,CAAC,EAChC;MACF,MAAMF,MAAM,GAAG+I,aAAa,CAAC;QAC3BlJ,OAAO,EAAEA,OAAO,CAAC0C;MACzB,CAAO,EAAEuG,QAAQ,CAAC;MACZ,IAAI9I,MAAM,IAAIA,MAAM,CAACgJ,YAAY,EAAE;QACjCN,UAAU,GAAGA,UAAU,CAAChF,MAAM,CAAEmB,GAAG,IAAK;UACtC,OAAOmD,MAAM,CAACiB,IAAI,CAAE1G,KAAK,IAAKvC,MAAM,CAACgJ,YAAY,CAACvB,IAAI,CAAC,IAAI,EAAElF,KAAK,EAAEsC,GAAG,EAAE7E,MAAM,CAAC,CAAC;QAC3F,CAAS,CAAC;MACV;IACA,CAAK,CAAC;IACFmC,YAAY,CAACI,KAAK,GAAGmG,UAAU;EACnC,CAAG;EACD,MAAMQ,QAAQ,GAAGA,CAAA,KAAM;IACrBhK,IAAI,CAACqD,KAAK,GAAGtD,QAAQ,CAACkD,YAAY,CAACI,KAAK,EAAE;MACxCnD,aAAa,EAAEA,aAAa,CAACmD,KAAK;MAClC/C,QAAQ,EAAEA,QAAQ,CAAC+C,KAAK;MACxB9C,SAAS,EAAEA,SAAS,CAAC8C;IAC3B,CAAK,CAAC;EACN,CAAG;EACD,MAAM4G,SAAS,GAAGA,CAACC,MAAM,GAAG,KAAK,CAAC,KAAK;IACrC,IAAI,EAAEA,MAAM,IAAIA,MAAM,CAAC1F,MAAM,CAAC,EAAE;MAC9B+E,UAAU,EAAE;IAClB;IACIS,QAAQ,EAAE;EACd,CAAG;EACD,MAAMG,WAAW,GAAIC,UAAU,IAAK;IAClC,MAAM;MAAErG;IAAc,CAAE,GAAG1C,QAAQ,CAACyC,IAAI;IACxC,IAAI,CAACC,cAAc,EACjB;IACF,MAAMsG,MAAM,GAAGX,MAAM,CAACY,MAAM,CAAC,EAAE,EAAEvG,cAAc,CAACwG,YAAY,CAAC;IAC7D,MAAMZ,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACU,MAAM,CAAC;IAChC,IAAI,CAACV,IAAI,CAAC3I,MAAM,EACd;IACF,IAAIb,QAAQ,CAACiK,UAAU,CAAC,EAAE;MACxBA,UAAU,GAAG,CAACA,UAAU,CAAC;IAC/B;IACI,IAAIrB,OAAO,CAACqB,UAAU,CAAC,EAAE;MACvB,MAAMI,QAAQ,GAAGJ,UAAU,CAACK,GAAG,CAAElE,GAAG,IAAKmE,cAAc,CAAC;QACtD/J,OAAO,EAAEA,OAAO,CAAC0C;MACzB,CAAO,EAAEkD,GAAG,CAAC,CAAC;MACRoD,IAAI,CAAC9I,OAAO,CAAE0F,GAAG,IAAK;QACpB,MAAMzF,MAAM,GAAG0J,QAAQ,CAAC7F,IAAI,CAAEsE,GAAG,IAAKA,GAAG,CAACC,EAAE,KAAK3C,GAAG,CAAC;QACrD,IAAIzF,MAAM,EAAE;UACVA,MAAM,CAAC6J,aAAa,GAAG,EAAE;QACnC;MACA,CAAO,CAAC;MACFtJ,QAAQ,CAAC+E,KAAK,CAACwE,MAAM,CAAC,cAAc,EAAE;QACpC9J,MAAM,EAAE0J,QAAQ;QAChB1B,MAAM,EAAE,EAAE;QACV+B,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE;MACf,CAAO,CAAC;IACR,CAAK,MAAM;MACLnB,IAAI,CAAC9I,OAAO,CAAE0F,GAAG,IAAK;QACpB,MAAMzF,MAAM,GAAGH,OAAO,CAAC0C,KAAK,CAACsB,IAAI,CAAEsE,GAAG,IAAKA,GAAG,CAACC,EAAE,KAAK3C,GAAG,CAAC;QAC1D,IAAIzF,MAAM,EAAE;UACVA,MAAM,CAAC6J,aAAa,GAAG,EAAE;QACnC;MACA,CAAO,CAAC;MACF3H,OAAO,CAACK,KAAK,GAAG,EAAE;MAClBhC,QAAQ,CAAC+E,KAAK,CAACwE,MAAM,CAAC,cAAc,EAAE;QACpC9J,MAAM,EAAE,EAAE;QACVgI,MAAM,EAAE,EAAE;QACV+B,MAAM,EAAE;MAChB,CAAO,CAAC;IACR;EACA,CAAG;EACD,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAAC7K,aAAa,CAACmD,KAAK,EACtB;IACF+F,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5B/H,QAAQ,CAAC+E,KAAK,CAACwE,MAAM,CAAC,qBAAqB,EAAE;MAC3CC,MAAM,EAAE;IACd,CAAK,CAAC;EACN,CAAG;EACD,MAAM;IACJG,gBAAgB;IAChBC,kBAAkB;IAClBC,gBAAgB;IAChBjL,MAAM,EAAEkL,YAAY;IACpBC;EACJ,CAAG,GAAGC,SAAS,CAAC;IACZrL,IAAI;IACJ4B;EACJ,CAAG,CAAC;EACF,MAAM;IACJ0J,oBAAoB;IACpBC,mBAAmB;IACnBC,cAAc;IACdC,iBAAiB;IACjBC,YAAY;IACZzL,MAAM,EAAE0L;EACZ,CAAG,GAAGC,OAAO,CAAC;IACV5L,IAAI;IACJ4B;EACJ,CAAG,CAAC;EACF,MAAM;IACJiK,oBAAoB;IACpBC,gBAAgB;IAChBC,gBAAgB;IAChB9L,MAAM,EAAE+L;EACZ,CAAG,GAAGC,UAAU,CAAC;IACbjM,IAAI;IACJ4B;EACJ,CAAG,CAAC;EACF,MAAMsK,uBAAuB,GAAIC,GAAG,IAAK;IACvCnB,gBAAgB,CAACmB,GAAG,CAAC;IACrBb,oBAAoB,CAACa,GAAG,CAAC;EAC7B,CAAG;EACD,MAAMC,yBAAyB,GAAGA,CAACzG,GAAG,EAAE0G,QAAQ,KAAK;IACnD,MAAMC,eAAe,GAAG3L,OAAO,CAAC0C,KAAK,CAAC0G,IAAI,CAAC,CAAC;MAAEnF;IAAI,CAAE,KAAKA,IAAI,KAAK,QAAQ,CAAC;IAC3E,IAAI0H,eAAe,EAAE;MACnBrB,kBAAkB,CAACtF,GAAG,EAAE0G,QAAQ,CAAC;IACvC,CAAK,MAAM;MACLd,mBAAmB,CAAC5F,GAAG,EAAE0G,QAAQ,CAAC;IACxC;EACA,CAAG;EACD,OAAO;IACLnI,YAAY;IACZK,aAAa;IACbb,cAAc;IACdgC,UAAU;IACVG,cAAc;IACdG,cAAc;IACdY,gBAAgB;IAChBC,kBAAkB;IAClBU,mBAAmB;IACnBgF,kBAAkB,EAAE,IAAI;IACxBzE,uBAAuB;IACvBG,iBAAiB;IACjBW,aAAa;IACbkD,gBAAgB;IAChB1C,UAAU;IACVG,UAAU;IACVS,QAAQ;IACRC,SAAS;IACTE,WAAW;IACXY,SAAS;IACTE,kBAAkB;IAClBiB,uBAAuB;IACvBH,gBAAgB;IAChBK,yBAAyB;IACzBhB,aAAa;IACbF,gBAAgB;IAChBW,oBAAoB;IACpBH,YAAY;IACZF,cAAc;IACdC,iBAAiB;IACjBxL,MAAM,EAAE;MACNuB,SAAS;MACTI,MAAM;MACN5B,IAAI;MACJ8B,KAAK;MACLC,SAAS;MACTC,QAAQ;MACRC,aAAa;MACbtB,OAAO;MACPuB,YAAY;MACZC,iBAAiB;MACjBC,WAAW;MACXC,gBAAgB;MAChBC,qBAAqB;MACrBC,cAAc;MACdC,iBAAiB;MACjBC,sBAAsB;MACtBC,2BAA2B;MAC3BC,aAAa;MACbC,SAAS;MACTC,gBAAgB;MAChBC,qBAAqB;MACrBC,UAAU;MACVC,OAAO;MACPC,YAAY;MACZ/C,aAAa;MACbI,QAAQ;MACRC,SAAS;MACT2C,QAAQ;MACR,GAAGiI,YAAY;MACf,GAAGQ,UAAU;MACb,GAAGK;IACT;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}