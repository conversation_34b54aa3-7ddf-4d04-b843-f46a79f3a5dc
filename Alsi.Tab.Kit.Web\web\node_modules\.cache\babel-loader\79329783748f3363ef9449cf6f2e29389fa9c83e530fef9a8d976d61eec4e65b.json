{"ast": null, "code": "import createRound from './_createRound.js';\n\n/**\n * Computes `number` rounded to `precision`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Math\n * @param {number} number The number to round.\n * @param {number} [precision=0] The precision to round to.\n * @returns {number} Returns the rounded number.\n * @example\n *\n * _.round(4.006);\n * // => 4\n *\n * _.round(4.006, 2);\n * // => 4.01\n *\n * _.round(4060, -2);\n * // => 4100\n */\nvar round = createRound('round');\nexport default round;", "map": {"version": 3, "names": ["createRound", "round"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/round.js"], "sourcesContent": ["import createRound from './_createRound.js';\n\n/**\n * Computes `number` rounded to `precision`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Math\n * @param {number} number The number to round.\n * @param {number} [precision=0] The precision to round to.\n * @returns {number} Returns the rounded number.\n * @example\n *\n * _.round(4.006);\n * // => 4\n *\n * _.round(4.006, 2);\n * // => 4.01\n *\n * _.round(4060, -2);\n * // => 4100\n */\nvar round = createRound('round');\n\nexport default round;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAGD,WAAW,CAAC,OAAO,CAAC;AAEhC,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}