{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst barProps = buildProps({\n  always: {\n    type: Boolean,\n    default: true\n  },\n  minSize: {\n    type: Number,\n    required: true\n  }\n});\nexport { barProps };", "map": {"version": 3, "names": ["barProps", "buildProps", "always", "type", "Boolean", "default", "minSize", "Number", "required"], "sources": ["../../../../../../packages/components/scrollbar/src/bar.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Bar from './bar.vue'\n\nexport const barProps = buildProps({\n  always: {\n    type: Boolean,\n    default: true,\n  },\n  minSize: {\n    type: Number,\n    required: true,\n  },\n} as const)\nexport type BarProps = ExtractPropTypes<typeof barProps>\n\nexport type BarInstance = InstanceType<typeof Bar> & unknown\n"], "mappings": ";AACY,MAACA,QAAQ,GAAGC,UAAU,CAAC;EACjCC,MAAM,EAAE;IACNC,IAAI,EAAEC,OAAO;IACbC,OAAO,EAAE;EACb,CAAG;EACDC,OAAO,EAAE;IACPH,IAAI,EAAEI,MAAM;IACZC,QAAQ,EAAE;EACd;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}