{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { inject } from 'vue';\nimport { getFixedColumnOffset, ensurePosition, getFixedColumnsClass } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { isFunction, isString } from '@vue/shared';\nfunction useStyle(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const ns = useNamespace(\"table\");\n  const getHeaderRowStyle = rowIndex => {\n    const headerRowStyle = parent == null ? void 0 : parent.props.headerRowStyle;\n    if (isFunction(headerRowStyle)) {\n      return headerRowStyle.call(null, {\n        rowIndex\n      });\n    }\n    return headerRowStyle;\n  };\n  const getHeaderRowClass = rowIndex => {\n    const classes = [];\n    const headerRowClassName = parent == null ? void 0 : parent.props.headerRowClassName;\n    if (isString(headerRowClassName)) {\n      classes.push(headerRowClassName);\n    } else if (isFunction(headerRowClassName)) {\n      classes.push(headerRowClassName.call(null, {\n        rowIndex\n      }));\n    }\n    return classes.join(\" \");\n  };\n  const getHeaderCellStyle = (rowIndex, columnIndex, row, column) => {\n    var _a;\n    let headerCellStyles = (_a = parent == null ? void 0 : parent.props.headerCellStyle) != null ? _a : {};\n    if (isFunction(headerCellStyles)) {\n      headerCellStyles = headerCellStyles.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      });\n    }\n    const fixedStyle = getFixedColumnOffset(columnIndex, column.fixed, props.store, row);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return Object.assign({}, headerCellStyles, fixedStyle);\n  };\n  const getHeaderCellClass = (rowIndex, columnIndex, row, column) => {\n    const fixedClasses = getFixedColumnsClass(ns.b(), columnIndex, column.fixed, props.store, row);\n    const classes = [column.id, column.order, column.headerAlign, column.className, column.labelClassName, ...fixedClasses];\n    if (!column.children) {\n      classes.push(\"is-leaf\");\n    }\n    if (column.sortable) {\n      classes.push(\"is-sortable\");\n    }\n    const headerCellClassName = parent == null ? void 0 : parent.props.headerCellClassName;\n    if (isString(headerCellClassName)) {\n      classes.push(headerCellClassName);\n    } else if (isFunction(headerCellClassName)) {\n      classes.push(headerCellClassName.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column\n      }));\n    }\n    classes.push(ns.e(\"cell\"));\n    return classes.filter(className => Boolean(className)).join(\" \");\n  };\n  return {\n    getHeaderRowStyle,\n    getHeaderRowClass,\n    getHeaderCellStyle,\n    getHeaderCellClass\n  };\n}\nexport { useStyle as default };", "map": {"version": 3, "names": ["useStyle", "props", "parent", "inject", "TABLE_INJECTION_KEY", "ns", "useNamespace", "getHeaderRowStyle", "rowIndex", "headerRowStyle", "isFunction", "call", "getHeaderRowClass", "classes", "headerRowClassName", "isString", "push", "join", "getHeaderCellStyle", "columnIndex", "row", "column", "_a", "headerCellStyles", "headerCellStyle", "fixedStyle", "getFixedColumnOffset", "fixed", "store", "ensurePosition", "Object", "assign", "getHeaderCellClass", "fixedClasses", "getFixedColumnsClass", "b", "id", "order", "headerAlign", "className", "labelClassName", "children", "sortable", "headerCellClassName", "e", "filter", "Boolean"], "sources": ["../../../../../../../packages/components/table/src/table-header/style.helper.ts"], "sourcesContent": ["import { inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isString } from '@element-plus/utils'\n\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableHeaderProps } from '.'\n\nfunction useStyle<T>(props: TableHeaderProps<T>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const ns = useNamespace('table')\n\n  const getHeaderRowStyle = (rowIndex: number) => {\n    const headerRowStyle = parent?.props.headerRowStyle\n    if (isFunction(headerRowStyle)) {\n      return headerRowStyle.call(null, { rowIndex })\n    }\n    return headerRowStyle\n  }\n\n  const getHeaderRowClass = (rowIndex: number): string => {\n    const classes: string[] = []\n    const headerRowClassName = parent?.props.headerRowClassName\n    if (isString(headerRowClassName)) {\n      classes.push(headerRowClassName)\n    } else if (isFunction(headerRowClassName)) {\n      classes.push(headerRowClassName.call(null, { rowIndex }))\n    }\n\n    return classes.join(' ')\n  }\n\n  const getHeaderCellStyle = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    let headerCellStyles = parent?.props.headerCellStyle ?? {}\n    if (isFunction(headerCellStyles)) {\n      headerCellStyles = headerCellStyles.call(null, {\n        rowIndex,\n        columnIndex,\n        row,\n        column,\n      })\n    }\n    const fixedStyle = getFixedColumnOffset<T>(\n      columnIndex,\n      column.fixed,\n      props.store,\n      row as unknown as TableColumnCtx<T>[]\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return Object.assign({}, headerCellStyles, fixedStyle)\n  }\n\n  const getHeaderCellClass = (\n    rowIndex: number,\n    columnIndex: number,\n    row: T,\n    column: TableColumnCtx<T>\n  ) => {\n    const fixedClasses = getFixedColumnsClass<T>(\n      ns.b(),\n      columnIndex,\n      column.fixed,\n      props.store,\n      row as unknown as TableColumnCtx<T>[]\n    )\n    const classes = [\n      column.id,\n      column.order,\n      column.headerAlign,\n      column.className,\n      column.labelClassName,\n      ...fixedClasses,\n    ]\n\n    if (!column.children) {\n      classes.push('is-leaf')\n    }\n\n    if (column.sortable) {\n      classes.push('is-sortable')\n    }\n\n    const headerCellClassName = parent?.props.headerCellClassName\n    if (isString(headerCellClassName)) {\n      classes.push(headerCellClassName)\n    } else if (isFunction(headerCellClassName)) {\n      classes.push(\n        headerCellClassName.call(null, {\n          rowIndex,\n          columnIndex,\n          row,\n          column,\n        })\n      )\n    }\n\n    classes.push(ns.e('cell'))\n\n    return classes.filter((className) => Boolean(className)).join(' ')\n  }\n\n  return {\n    getHeaderRowStyle,\n    getHeaderRowClass,\n    getHeaderCellStyle,\n    getHeaderCellClass,\n  }\n}\n\nexport default useStyle\n"], "mappings": ";;;;;;AASA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,MAAMC,cAAc,GAAGP,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACQ,cAAc;IAC5E,IAAIC,UAAU,CAACD,cAAc,CAAC,EAAE;MAC9B,OAAOA,cAAc,CAACE,IAAI,CAAC,IAAI,EAAE;QAAEH;MAAQ,CAAE,CAAC;IACpD;IACI,OAAOC,cAAc;EACzB,CAAG;EACD,MAAMG,iBAAiB,GAAIJ,QAAQ,IAAK;IACtC,MAAMK,OAAO,GAAG,EAAE;IAClB,MAAMC,kBAAkB,GAAGZ,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACa,kBAAkB;IACpF,IAAIC,QAAQ,CAACD,kBAAkB,CAAC,EAAE;MAChCD,OAAO,CAACG,IAAI,CAACF,kBAAkB,CAAC;IACtC,CAAK,MAAM,IAAIJ,UAAU,CAACI,kBAAkB,CAAC,EAAE;MACzCD,OAAO,CAACG,IAAI,CAACF,kBAAkB,CAACH,IAAI,CAAC,IAAI,EAAE;QAAEH;MAAQ,CAAE,CAAC,CAAC;IAC/D;IACI,OAAOK,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC;EAC5B,CAAG;EACD,MAAMC,kBAAkB,GAAGA,CAACV,QAAQ,EAAEW,WAAW,EAAEC,GAAG,EAAEC,MAAM,KAAK;IACjE,IAAIC,EAAE;IACN,IAAIC,gBAAgB,GAAG,CAACD,EAAE,GAAGpB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAACuB,eAAe,KAAK,IAAI,GAAGF,EAAE,GAAG,EAAE;IACtG,IAAIZ,UAAU,CAACa,gBAAgB,CAAC,EAAE;MAChCA,gBAAgB,GAAGA,gBAAgB,CAACZ,IAAI,CAAC,IAAI,EAAE;QAC7CH,QAAQ;QACRW,WAAW;QACXC,GAAG;QACHC;MACR,CAAO,CAAC;IACR;IACI,MAAMI,UAAU,GAAGC,oBAAoB,CAACP,WAAW,EAAEE,MAAM,CAACM,KAAK,EAAE1B,KAAK,CAAC2B,KAAK,EAAER,GAAG,CAAC;IACpFS,cAAc,CAACJ,UAAU,EAAE,MAAM,CAAC;IAClCI,cAAc,CAACJ,UAAU,EAAE,OAAO,CAAC;IACnC,OAAOK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAER,gBAAgB,EAAEE,UAAU,CAAC;EAC1D,CAAG;EACD,MAAMO,kBAAkB,GAAGA,CAACxB,QAAQ,EAAEW,WAAW,EAAEC,GAAG,EAAEC,MAAM,KAAK;IACjE,MAAMY,YAAY,GAAGC,oBAAoB,CAAC7B,EAAE,CAAC8B,CAAC,EAAE,EAAEhB,WAAW,EAAEE,MAAM,CAACM,KAAK,EAAE1B,KAAK,CAAC2B,KAAK,EAAER,GAAG,CAAC;IAC9F,MAAMP,OAAO,GAAG,CACdQ,MAAM,CAACe,EAAE,EACTf,MAAM,CAACgB,KAAK,EACZhB,MAAM,CAACiB,WAAW,EAClBjB,MAAM,CAACkB,SAAS,EAChBlB,MAAM,CAACmB,cAAc,EACrB,GAAGP,YAAY,CAChB;IACD,IAAI,CAACZ,MAAM,CAACoB,QAAQ,EAAE;MACpB5B,OAAO,CAACG,IAAI,CAAC,SAAS,CAAC;IAC7B;IACI,IAAIK,MAAM,CAACqB,QAAQ,EAAE;MACnB7B,OAAO,CAACG,IAAI,CAAC,aAAa,CAAC;IACjC;IACI,MAAM2B,mBAAmB,GAAGzC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACD,KAAK,CAAC0C,mBAAmB;IACtF,IAAI5B,QAAQ,CAAC4B,mBAAmB,CAAC,EAAE;MACjC9B,OAAO,CAACG,IAAI,CAAC2B,mBAAmB,CAAC;IACvC,CAAK,MAAM,IAAIjC,UAAU,CAACiC,mBAAmB,CAAC,EAAE;MAC1C9B,OAAO,CAACG,IAAI,CAAC2B,mBAAmB,CAAChC,IAAI,CAAC,IAAI,EAAE;QAC1CH,QAAQ;QACRW,WAAW;QACXC,GAAG;QACHC;MACR,CAAO,CAAC,CAAC;IACT;IACIR,OAAO,CAACG,IAAI,CAACX,EAAE,CAACuC,CAAC,CAAC,MAAM,CAAC,CAAC;IAC1B,OAAO/B,OAAO,CAACgC,MAAM,CAAEN,SAAS,IAAKO,OAAO,CAACP,SAAS,CAAC,CAAC,CAACtB,IAAI,CAAC,GAAG,CAAC;EACtE,CAAG;EACD,OAAO;IACLV,iBAAiB;IACjBK,iBAAiB;IACjBM,kBAAkB;IAClBc;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}