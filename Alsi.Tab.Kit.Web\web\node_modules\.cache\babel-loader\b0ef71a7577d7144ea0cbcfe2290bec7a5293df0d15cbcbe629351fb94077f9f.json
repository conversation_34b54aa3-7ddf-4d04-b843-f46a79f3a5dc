{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, renderSlot } from 'vue';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nconst __default__ = defineComponent({\n  name: \"TableTdWrapper\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: {\n    colspan: {\n      type: Number,\n      default: 1\n    },\n    rowspan: {\n      type: Number,\n      default: 1\n    }\n  },\n  setup(__props) {\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"td\", {\n        colspan: __props.colspan,\n        rowspan: __props.rowspan\n      }, [renderSlot(_ctx.$slots, \"default\")], 8, [\"colspan\", \"rowspan\"]);\n    };\n  }\n});\nvar TdWrapper = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"td-wrapper.vue\"]]);\nexport { TdWrapper as default };", "map": {"version": 3, "names": ["name"], "sources": ["../../../../../../../packages/components/table/src/table-body/td-wrapper.vue"], "sourcesContent": ["<template>\n  <td :colspan=\"colspan\" :rowspan=\"rowspan\"><slot /></td>\n</template>\n\n<script setup lang=\"ts\">\ndefineOptions({\n  name: 'TableTdWrapper',\n})\n\ndefineProps({\n  colspan: {\n    type: Number,\n    default: 1,\n  },\n  rowspan: {\n    type: Number,\n    default: 1,\n  },\n})\n</script>\n"], "mappings": ";;mCAKc;EACZA,IAAM;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}