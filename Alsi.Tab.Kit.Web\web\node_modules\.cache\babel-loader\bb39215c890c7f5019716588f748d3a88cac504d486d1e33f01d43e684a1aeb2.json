{"ast": null, "code": "import { buildProps } from '../../../utils/vue/props/runtime.mjs';\nconst tooltipV2CommonProps = buildProps({\n  nowrap: Boolean\n});\nvar TooltipV2Sides = /* @__PURE__ */(TooltipV2Sides2 => {\n  TooltipV2Sides2[\"top\"] = \"top\";\n  TooltipV2Sides2[\"bottom\"] = \"bottom\";\n  TooltipV2Sides2[\"left\"] = \"left\";\n  TooltipV2Sides2[\"right\"] = \"right\";\n  return TooltipV2Sides2;\n})(TooltipV2Sides || {});\nconst tooltipV2Sides = Object.values(TooltipV2Sides);\nconst tooltipV2OppositeSide = {\n  [\"top\" /* top */]: \"bottom\" /* bottom */,\n  [\"bottom\" /* bottom */]: \"top\" /* top */,\n  [\"left\" /* left */]: \"right\" /* right */,\n  [\"right\" /* right */]: \"left\" /* left */\n};\nconst tooltipV2ArrowBorders = {\n  [\"top\" /* top */]: [\"left\" /* left */, \"top\" /* top */],\n  [\"bottom\" /* bottom */]: [\"bottom\" /* bottom */, \"right\" /* right */],\n  [\"left\" /* left */]: [\"bottom\" /* bottom */, \"left\" /* left */],\n  [\"right\" /* right */]: [\"top\" /* top */, \"right\" /* right */]\n};\nexport { TooltipV2Sides, tooltipV2ArrowBorders, tooltipV2CommonProps, tooltipV2OppositeSide, tooltipV2Sides };", "map": {"version": 3, "names": ["tooltipV2CommonProps", "buildProps", "nowrap", "Boolean", "TooltipV2Sides", "TooltipV2Sides2", "tooltipV2Sides", "Object", "values", "tooltipV2OppositeSide", "tooltipV2ArrowBorders"], "sources": ["../../../../../../packages/components/tooltip-v2/src/common.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\n/**\n * TODO: make this under constants or tokens\n */\nexport const tooltipV2CommonProps = buildProps({\n  nowrap: Boolean,\n} as const)\n\nexport type TooltipV2CommonProps = ExtractPropTypes<typeof tooltipV2CommonProps>\n\nexport enum TooltipV2Sides {\n  top = 'top',\n  bottom = 'bottom',\n  left = 'left',\n  right = 'right',\n}\n\nexport const tooltipV2Sides = Object.values(TooltipV2Sides)\n\nexport const tooltipV2OppositeSide = {\n  [TooltipV2Sides.top]: TooltipV2Sides.bottom,\n  [TooltipV2Sides.bottom]: TooltipV2Sides.top,\n  [TooltipV2Sides.left]: TooltipV2Sides.right,\n  [TooltipV2Sides.right]: TooltipV2Sides.left,\n} as const\n\nexport const tooltipV2ArrowBorders = {\n  [TooltipV2Sides.top]: [TooltipV2Sides.left, TooltipV2Sides.top],\n  [TooltipV2Sides.bottom]: [TooltipV2Sides.bottom, TooltipV2Sides.right],\n  [TooltipV2Sides.left]: [TooltipV2Sides.bottom, TooltipV2Sides.left],\n  [TooltipV2Sides.right]: [TooltipV2Sides.top, TooltipV2Sides.right],\n} as const\n"], "mappings": ";AACY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7CC,MAAM,EAAEC;AACV,CAAC;AACS,IAACC,cAAc,kBAAmB,CAAEC,eAAe,IAAK;EAChEA,eAAe,CAAC,KAAK,CAAC,GAAG,KAAK;EAC9BA,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACpCA,eAAe,CAAC,MAAM,CAAC,GAAG,MAAM;EAChCA,eAAe,CAAC,OAAO,CAAC,GAAG,OAAO;EAClC,OAAOA,eAAe;AACxB,CAAC,EAAED,cAAc,IAAI,EAAE;AACX,MAACE,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACJ,cAAc;AAC9C,MAACK,qBAAqB,GAAG;EACnC,CAAC,KAAK,aAAa,QAAQ;EAC3B,CAAC,QAAQ,gBAAgB,KAAK;EAC9B,CAAC,MAAM,cAAc,OAAO;EAC5B,CAAC,OAAO,eAAe,MAAM;AAC/B;AACY,MAACC,qBAAqB,GAAG;EACnC,CAAC,KAAK,aAAa,CAAC,MAAM,aAAa,KAAK,WAAW;EACvD,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,eAAe,OAAO,aAAa;EACrE,CAAC,MAAM,cAAc,CAAC,QAAQ,eAAe,MAAM,YAAY;EAC/D,CAAC,OAAO,eAAe,CAAC,KAAK,YAAY,OAAO;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}