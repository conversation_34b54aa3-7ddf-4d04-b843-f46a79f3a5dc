{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport baseTrim from './_baseTrim.js';\nimport castSlice from './_castSlice.js';\nimport charsEndIndex from './_charsEndIndex.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/**\n * Removes leading and trailing whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trim('  abc  ');\n * // => 'abc'\n *\n * _.trim('-_-abc-_-', '_-');\n * // => 'abc'\n *\n * _.map(['  foo  ', '  bar  '], _.trim);\n * // => ['foo', 'bar']\n */\nfunction trim(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return baseTrim(string);\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n    chrSymbols = stringToArray(chars),\n    start = charsStartIndex(strSymbols, chrSymbols),\n    end = charsEndIndex(strSymbols, chrSymbols) + 1;\n  return castSlice(strSymbols, start, end).join('');\n}\nexport default trim;", "map": {"version": 3, "names": ["baseToString", "baseTrim", "castSlice", "charsEndIndex", "charsStartIndex", "stringToArray", "toString", "trim", "string", "chars", "guard", "undefined", "strSymbols", "chrSymbols", "start", "end", "join"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/trim.js"], "sourcesContent": ["import baseToString from './_baseToString.js';\nimport baseTrim from './_baseTrim.js';\nimport castSlice from './_castSlice.js';\nimport charsEndIndex from './_charsEndIndex.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/**\n * Removes leading and trailing whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trim('  abc  ');\n * // => 'abc'\n *\n * _.trim('-_-abc-_-', '_-');\n * // => 'abc'\n *\n * _.map(['  foo  ', '  bar  '], _.trim);\n * // => ['foo', 'bar']\n */\nfunction trim(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return baseTrim(string);\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n      chrSymbols = stringToArray(chars),\n      start = charsStartIndex(strSymbols, chrSymbols),\n      end = charsEndIndex(strSymbols, chrSymbols) + 1;\n\n  return castSlice(strSymbols, start, end).join('');\n}\n\nexport default trim;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAClCF,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC;EACzB,IAAIA,MAAM,KAAKE,KAAK,IAAID,KAAK,KAAKE,SAAS,CAAC,EAAE;IAC5C,OAAOV,QAAQ,CAACO,MAAM,CAAC;EACzB;EACA,IAAI,CAACA,MAAM,IAAI,EAAEC,KAAK,GAAGT,YAAY,CAACS,KAAK,CAAC,CAAC,EAAE;IAC7C,OAAOD,MAAM;EACf;EACA,IAAII,UAAU,GAAGP,aAAa,CAACG,MAAM,CAAC;IAClCK,UAAU,GAAGR,aAAa,CAACI,KAAK,CAAC;IACjCK,KAAK,GAAGV,eAAe,CAACQ,UAAU,EAAEC,UAAU,CAAC;IAC/CE,GAAG,GAAGZ,aAAa,CAACS,UAAU,EAAEC,UAAU,CAAC,GAAG,CAAC;EAEnD,OAAOX,SAAS,CAACU,UAAU,EAAEE,KAAK,EAAEC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AACnD;AAEA,eAAeT,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}