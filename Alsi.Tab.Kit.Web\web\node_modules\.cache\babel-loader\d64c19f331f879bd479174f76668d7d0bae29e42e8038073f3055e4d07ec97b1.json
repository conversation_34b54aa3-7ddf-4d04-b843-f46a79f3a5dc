{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport baseToString from './_baseToString.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Checks if `string` starts with the given target string.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {string} [target] The string to search for.\n * @param {number} [position=0] The position to search from.\n * @returns {boolean} Returns `true` if `string` starts with `target`,\n *  else `false`.\n * @example\n *\n * _.startsWith('abc', 'a');\n * // => true\n *\n * _.startsWith('abc', 'b');\n * // => false\n *\n * _.startsWith('abc', 'b', 1);\n * // => true\n */\nfunction startsWith(string, target, position) {\n  string = toString(string);\n  position = position == null ? 0 : baseClamp(toInteger(position), 0, string.length);\n  target = baseToString(target);\n  return string.slice(position, position + target.length) == target;\n}\nexport default startsWith;", "map": {"version": 3, "names": ["baseClamp", "baseToString", "toInteger", "toString", "startsWith", "string", "target", "position", "length", "slice"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/startsWith.js"], "sourcesContent": ["import baseClamp from './_baseClamp.js';\nimport baseToString from './_baseToString.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Checks if `string` starts with the given target string.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {string} [target] The string to search for.\n * @param {number} [position=0] The position to search from.\n * @returns {boolean} Returns `true` if `string` starts with `target`,\n *  else `false`.\n * @example\n *\n * _.startsWith('abc', 'a');\n * // => true\n *\n * _.startsWith('abc', 'b');\n * // => false\n *\n * _.startsWith('abc', 'b', 1);\n * // => true\n */\nfunction startsWith(string, target, position) {\n  string = toString(string);\n  position = position == null\n    ? 0\n    : baseClamp(toInteger(position), 0, string.length);\n\n  target = baseToString(target);\n  return string.slice(position, position + target.length) == target;\n}\n\nexport default startsWith;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAC5CF,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC;EACzBE,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GACvB,CAAC,GACDP,SAAS,CAACE,SAAS,CAACK,QAAQ,CAAC,EAAE,CAAC,EAAEF,MAAM,CAACG,MAAM,CAAC;EAEpDF,MAAM,GAAGL,YAAY,CAACK,MAAM,CAAC;EAC7B,OAAOD,MAAM,CAACI,KAAK,CAACF,QAAQ,EAAEA,QAAQ,GAAGD,MAAM,CAACE,MAAM,CAAC,IAAIF,MAAM;AACnE;AAEA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}