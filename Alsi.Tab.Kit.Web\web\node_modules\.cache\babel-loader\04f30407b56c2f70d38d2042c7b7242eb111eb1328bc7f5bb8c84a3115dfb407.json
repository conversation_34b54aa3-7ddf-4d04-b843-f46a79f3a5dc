{"ast": null, "code": "import now from './now.js';\nexport default {\n  now\n};", "map": {"version": 3, "names": ["now"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/date.default.js"], "sourcesContent": ["import now from './now.js';\n\nexport default {\n  now\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAE1B,eAAe;EACbA;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}