{"ast": null, "code": "import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that iterates over `pairs` and invokes the corresponding\n * function of the first predicate to return truthy. The predicate-function\n * pairs are invoked with the `this` binding and arguments of the created\n * function.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {Array} pairs The predicate-function pairs.\n * @returns {Function} Returns the new composite function.\n * @example\n *\n * var func = _.cond([\n *   [_.matches({ 'a': 1 }),           _.constant('matches A')],\n *   [_.conforms({ 'b': _.isNumber }), _.constant('matches B')],\n *   [_.stubTrue,                      _.constant('no match')]\n * ]);\n *\n * func({ 'a': 1, 'b': 2 });\n * // => 'matches A'\n *\n * func({ 'a': 0, 'b': 1 });\n * // => 'matches B'\n *\n * func({ 'a': '1', 'b': '2' });\n * // => 'no match'\n */\nfunction cond(pairs) {\n  var length = pairs == null ? 0 : pairs.length,\n    toIteratee = baseIteratee;\n  pairs = !length ? [] : arrayMap(pairs, function (pair) {\n    if (typeof pair[1] != 'function') {\n      throw new TypeError(FUNC_ERROR_TEXT);\n    }\n    return [toIteratee(pair[0]), pair[1]];\n  });\n  return baseRest(function (args) {\n    var index = -1;\n    while (++index < length) {\n      var pair = pairs[index];\n      if (apply(pair[0], this, args)) {\n        return apply(pair[1], this, args);\n      }\n    }\n  });\n}\nexport default cond;", "map": {"version": 3, "names": ["apply", "arrayMap", "baseIteratee", "baseRest", "FUNC_ERROR_TEXT", "cond", "pairs", "length", "toIteratee", "pair", "TypeError", "args", "index"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/cond.js"], "sourcesContent": ["import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that iterates over `pairs` and invokes the corresponding\n * function of the first predicate to return truthy. The predicate-function\n * pairs are invoked with the `this` binding and arguments of the created\n * function.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {Array} pairs The predicate-function pairs.\n * @returns {Function} Returns the new composite function.\n * @example\n *\n * var func = _.cond([\n *   [_.matches({ 'a': 1 }),           _.constant('matches A')],\n *   [_.conforms({ 'b': _.isNumber }), _.constant('matches B')],\n *   [_.stubTrue,                      _.constant('no match')]\n * ]);\n *\n * func({ 'a': 1, 'b': 2 });\n * // => 'matches A'\n *\n * func({ 'a': 0, 'b': 1 });\n * // => 'matches B'\n *\n * func({ 'a': '1', 'b': '2' });\n * // => 'no match'\n */\nfunction cond(pairs) {\n  var length = pairs == null ? 0 : pairs.length,\n      toIteratee = baseIteratee;\n\n  pairs = !length ? [] : arrayMap(pairs, function(pair) {\n    if (typeof pair[1] != 'function') {\n      throw new TypeError(FUNC_ERROR_TEXT);\n    }\n    return [toIteratee(pair[0]), pair[1]];\n  });\n\n  return baseRest(function(args) {\n    var index = -1;\n    while (++index < length) {\n      var pair = pairs[index];\n      if (apply(pair[0], this, args)) {\n        return apply(pair[1], this, args);\n      }\n    }\n  });\n}\n\nexport default cond;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,eAAe,GAAG,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACC,MAAM;IACzCC,UAAU,GAAGN,YAAY;EAE7BI,KAAK,GAAG,CAACC,MAAM,GAAG,EAAE,GAAGN,QAAQ,CAACK,KAAK,EAAE,UAASG,IAAI,EAAE;IACpD,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;MAChC,MAAM,IAAIC,SAAS,CAACN,eAAe,CAAC;IACtC;IACA,OAAO,CAACI,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC,CAAC;EAEF,OAAON,QAAQ,CAAC,UAASQ,IAAI,EAAE;IAC7B,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,OAAO,EAAEA,KAAK,GAAGL,MAAM,EAAE;MACvB,IAAIE,IAAI,GAAGH,KAAK,CAACM,KAAK,CAAC;MACvB,IAAIZ,KAAK,CAACS,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEE,IAAI,CAAC,EAAE;QAC9B,OAAOX,KAAK,CAACS,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEE,IAAI,CAAC;MACnC;IACF;EACF,CAAC,CAAC;AACJ;AAEA,eAAeN,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}