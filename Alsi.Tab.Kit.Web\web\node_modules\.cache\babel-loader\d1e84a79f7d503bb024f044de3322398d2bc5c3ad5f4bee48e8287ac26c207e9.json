{"ast": null, "code": "import baseRepeat from './_baseRepeat.js';\nimport baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport stringSize from './_stringSize.js';\nimport stringToArray from './_stringToArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil;\n\n/**\n * Creates the padding for `string` based on `length`. The `chars` string\n * is truncated if the number of characters exceeds `length`.\n *\n * @private\n * @param {number} length The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padding for `string`.\n */\nfunction createPadding(length, chars) {\n  chars = chars === undefined ? ' ' : baseToString(chars);\n  var charsLength = chars.length;\n  if (charsLength < 2) {\n    return charsLength ? baseRepeat(chars, length) : chars;\n  }\n  var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));\n  return hasUnicode(chars) ? castSlice(stringToArray(result), 0, length).join('') : result.slice(0, length);\n}\nexport default createPadding;", "map": {"version": 3, "names": ["baseRepeat", "baseToString", "castSlice", "hasUnicode", "stringSize", "stringToArray", "nativeCeil", "Math", "ceil", "createPadding", "length", "chars", "undefined", "chars<PERSON><PERSON><PERSON>", "result", "join", "slice"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_createPadding.js"], "sourcesContent": ["import baseRepeat from './_baseRepeat.js';\nimport baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport stringSize from './_stringSize.js';\nimport stringToArray from './_stringToArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil;\n\n/**\n * Creates the padding for `string` based on `length`. The `chars` string\n * is truncated if the number of characters exceeds `length`.\n *\n * @private\n * @param {number} length The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padding for `string`.\n */\nfunction createPadding(length, chars) {\n  chars = chars === undefined ? ' ' : baseToString(chars);\n\n  var charsLength = chars.length;\n  if (charsLength < 2) {\n    return charsLength ? baseRepeat(chars, length) : chars;\n  }\n  var result = baseRepeat(chars, nativeCeil(length / stringSize(chars)));\n  return hasUnicode(chars)\n    ? castSlice(stringToArray(result), 0, length).join('')\n    : result.slice(0, length);\n}\n\nexport default createPadding;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,aAAa,MAAM,qBAAqB;;AAE/C;AACA,IAAIC,UAAU,GAAGC,IAAI,CAACC,IAAI;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACpCA,KAAK,GAAGA,KAAK,KAAKC,SAAS,GAAG,GAAG,GAAGX,YAAY,CAACU,KAAK,CAAC;EAEvD,IAAIE,WAAW,GAAGF,KAAK,CAACD,MAAM;EAC9B,IAAIG,WAAW,GAAG,CAAC,EAAE;IACnB,OAAOA,WAAW,GAAGb,UAAU,CAACW,KAAK,EAAED,MAAM,CAAC,GAAGC,KAAK;EACxD;EACA,IAAIG,MAAM,GAAGd,UAAU,CAACW,KAAK,EAAEL,UAAU,CAACI,MAAM,GAAGN,UAAU,CAACO,KAAK,CAAC,CAAC,CAAC;EACtE,OAAOR,UAAU,CAACQ,KAAK,CAAC,GACpBT,SAAS,CAACG,aAAa,CAACS,MAAM,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,GACpDD,MAAM,CAACE,KAAK,CAAC,CAAC,EAAEN,MAAM,CAAC;AAC7B;AAEA,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}