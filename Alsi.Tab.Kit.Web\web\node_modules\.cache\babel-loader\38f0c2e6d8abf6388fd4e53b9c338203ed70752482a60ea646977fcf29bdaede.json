{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseAt from './_baseAt.js';\nimport basePullAt from './_basePullAt.js';\nimport compareAscending from './_compareAscending.js';\nimport flatRest from './_flatRest.js';\nimport isIndex from './_isIndex.js';\n\n/**\n * Removes elements from `array` corresponding to `indexes` and returns an\n * array of removed elements.\n *\n * **Note:** Unlike `_.at`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {...(number|number[])} [indexes] The indexes of elements to remove.\n * @returns {Array} Returns the new array of removed elements.\n * @example\n *\n * var array = ['a', 'b', 'c', 'd'];\n * var pulled = _.pullAt(array, [1, 3]);\n *\n * console.log(array);\n * // => ['a', 'c']\n *\n * console.log(pulled);\n * // => ['b', 'd']\n */\nvar pullAt = flatRest(function (array, indexes) {\n  var length = array == null ? 0 : array.length,\n    result = baseAt(array, indexes);\n  basePullAt(array, arrayMap(indexes, function (index) {\n    return isIndex(index, length) ? +index : index;\n  }).sort(compareAscending));\n  return result;\n});\nexport default pullAt;", "map": {"version": 3, "names": ["arrayMap", "baseAt", "basePullAt", "compareAscending", "flatRest", "isIndex", "pullAt", "array", "indexes", "length", "result", "index", "sort"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/pullAt.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseAt from './_baseAt.js';\nimport basePullAt from './_basePullAt.js';\nimport compareAscending from './_compareAscending.js';\nimport flatRest from './_flatRest.js';\nimport isIndex from './_isIndex.js';\n\n/**\n * Removes elements from `array` corresponding to `indexes` and returns an\n * array of removed elements.\n *\n * **Note:** Unlike `_.at`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {...(number|number[])} [indexes] The indexes of elements to remove.\n * @returns {Array} Returns the new array of removed elements.\n * @example\n *\n * var array = ['a', 'b', 'c', 'd'];\n * var pulled = _.pullAt(array, [1, 3]);\n *\n * console.log(array);\n * // => ['a', 'c']\n *\n * console.log(pulled);\n * // => ['b', 'd']\n */\nvar pullAt = flatRest(function(array, indexes) {\n  var length = array == null ? 0 : array.length,\n      result = baseAt(array, indexes);\n\n  basePullAt(array, arrayMap(indexes, function(index) {\n    return isIndex(index, length) ? +index : index;\n  }).sort(compareAscending));\n\n  return result;\n});\n\nexport default pullAt;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAGF,QAAQ,CAAC,UAASG,KAAK,EAAEC,OAAO,EAAE;EAC7C,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;IACzCC,MAAM,GAAGT,MAAM,CAACM,KAAK,EAAEC,OAAO,CAAC;EAEnCN,UAAU,CAACK,KAAK,EAAEP,QAAQ,CAACQ,OAAO,EAAE,UAASG,KAAK,EAAE;IAClD,OAAON,OAAO,CAACM,KAAK,EAAEF,MAAM,CAAC,GAAG,CAACE,KAAK,GAAGA,KAAK;EAChD,CAAC,CAAC,CAACC,IAAI,CAACT,gBAAgB,CAAC,CAAC;EAE1B,OAAOO,MAAM;AACf,CAAC,CAAC;AAEF,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}