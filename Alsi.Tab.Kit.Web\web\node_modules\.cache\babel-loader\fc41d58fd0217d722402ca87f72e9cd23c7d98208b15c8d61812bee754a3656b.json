{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => d.overflows[0] > 0 && getSideAxis(d.placement) === initialSideAxis)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };", "map": {"version": 3, "names": ["getSideAxis", "getAlignmentAxis", "getAxisLength", "getSide", "getAlignment", "evaluate", "getPaddingObject", "rectToClientRect", "min", "clamp", "placements", "getAlignmentSides", "getOppositeAlignmentPlacement", "getOppositePlacement", "getExpandedPlacements", "getOppositeAxisPlacements", "sides", "max", "getOppositeAxis", "computeCoordsFromPlacement", "_ref", "placement", "rtl", "reference", "floating", "sideAxis", "alignmentAxis", "align<PERSON><PERSON><PERSON>", "side", "isVertical", "commonX", "x", "width", "commonY", "y", "height", "commonAlign", "coords", "computePosition", "config", "strategy", "middleware", "platform", "validMiddleware", "filter", "Boolean", "isRTL", "rects", "getElementRects", "statefulPlacement", "middlewareData", "resetCount", "i", "length", "name", "fn", "nextX", "nextY", "data", "reset", "initialPlacement", "elements", "detectOverflow", "state", "options", "_await$platform$isEle", "boundary", "rootBoundary", "elementContext", "altBoundary", "padding", "paddingObject", "altContext", "element", "clippingClientRect", "getClippingRect", "isElement", "contextElement", "getDocumentElement", "rect", "offsetParent", "getOffsetParent", "offsetScale", "getScale", "elementClientRect", "convertOffsetParentRelativeRectToViewportRelativeRect", "top", "bottom", "left", "right", "arrow", "axis", "arrowDimensions", "getDimensions", "isYAxis", "minProp", "maxProp", "clientProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "centerToReference", "largestPossiblePadding", "minPadding", "maxPadding", "min$1", "center", "offset", "shouldAddOffset", "alignmentOffset", "centerOffset", "getPlacementList", "alignment", "autoAlignment", "allowedPlacements", "allowedPlacementsSortedByAlignment", "autoPlacement", "_middlewareData$autoP", "_middlewareData$autoP2", "_placementsThatFitOnE", "crossAxis", "detectOverflowOptions", "placements$1", "undefined", "overflow", "currentIndex", "index", "currentPlacement", "alignmentSides", "currentOverflows", "allOverflows", "overflows", "nextPlacement", "placementsSortedByMostSpace", "map", "d", "slice", "reduce", "acc", "v", "sort", "a", "b", "placementsThatFitOnEachSide", "every", "resetPlacement", "flip", "_middlewareData$arrow", "_middlewareData$flip", "mainAxis", "checkMainAxis", "checkCrossAxis", "fallbackPlacements", "specifiedFallbackPlacements", "fallbackStrategy", "fallbackAxisSideDirection", "flipAlignment", "initialSideAxis", "isBasePlacement", "hasFallbackAxisSideDirection", "push", "overflowsData", "_middlewareData$flip2", "_overflowsData$filter", "nextIndex", "ignoreCrossAxisOverflow", "_overflowsData$filter2", "currentSideAxis", "getSideOffsets", "isAnySideFullyClipped", "some", "hide", "offsets", "referenceHiddenOffsets", "referenceHidden", "escapedOffsets", "escaped", "getBoundingRect", "minX", "minY", "maxX", "maxY", "getRectsByLine", "sortedRects", "groups", "prevRect", "inline", "nativeClientRects", "Array", "from", "getClientRects", "clientRects", "fallback", "getBoundingClientRect", "find", "firstRect", "lastRect", "isTop", "isLeftSide", "maxRight", "minLeft", "measureRects", "resetRects", "convertValueToCoords", "mainAxisMulti", "includes", "crossAxisMulti", "rawValue", "_middlewareData$offse", "diffCoords", "shift", "limiter", "mainAxisCoord", "crossAxisCoord", "minSide", "maxSide", "limitedCoords", "enabled", "limitShift", "rawOffset", "computedOffset", "len", "limitMin", "limitMax", "_middlewareData$offse2", "isOriginSide", "size", "_state$middlewareData", "_state$middlewareData2", "apply", "heightSide", "widthSide", "maximumClippingHeight", "maximumClippingWidth", "overflowAvailableHeight", "overflowAvailableWidth", "noShift", "availableHeight", "availableWidth", "xMin", "xMax", "yMin", "yMax", "nextDimensions"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/@floating-ui/core/dist/floating-ui.core.mjs"], "sourcesContent": ["import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => d.overflows[0] > 0 && getSideAxis(d.placement) === initialSideAxis)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n"], "mappings": ";;;;;;;;AAAA,SAASA,WAAW,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,6BAA6B,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,yBAAyB,EAAEC,KAAK,EAAEC,GAAG,EAAEC,eAAe,QAAQ,oBAAoB;AACrU,SAASX,gBAAgB,QAAQ,oBAAoB;AAErD,SAASY,0BAA0BA,CAACC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAE;EACxD,IAAI;IACFC,SAAS;IACTC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,QAAQ,GAAGzB,WAAW,CAACqB,SAAS,CAAC;EACvC,MAAMK,aAAa,GAAGzB,gBAAgB,CAACoB,SAAS,CAAC;EACjD,MAAMM,WAAW,GAAGzB,aAAa,CAACwB,aAAa,CAAC;EAChD,MAAME,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;EAC/B,MAAMQ,UAAU,GAAGJ,QAAQ,KAAK,GAAG;EACnC,MAAMK,OAAO,GAAGP,SAAS,CAACQ,CAAC,GAAGR,SAAS,CAACS,KAAK,GAAG,CAAC,GAAGR,QAAQ,CAACQ,KAAK,GAAG,CAAC;EACtE,MAAMC,OAAO,GAAGV,SAAS,CAACW,CAAC,GAAGX,SAAS,CAACY,MAAM,GAAG,CAAC,GAAGX,QAAQ,CAACW,MAAM,GAAG,CAAC;EACxE,MAAMC,WAAW,GAAGb,SAAS,CAACI,WAAW,CAAC,GAAG,CAAC,GAAGH,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC;EAC1E,IAAIU,MAAM;EACV,QAAQT,IAAI;IACV,KAAK,KAAK;MACRS,MAAM,GAAG;QACPN,CAAC,EAAED,OAAO;QACVI,CAAC,EAAEX,SAAS,CAACW,CAAC,GAAGV,QAAQ,CAACW;MAC5B,CAAC;MACD;IACF,KAAK,QAAQ;MACXE,MAAM,GAAG;QACPN,CAAC,EAAED,OAAO;QACVI,CAAC,EAAEX,SAAS,CAACW,CAAC,GAAGX,SAAS,CAACY;MAC7B,CAAC;MACD;IACF,KAAK,OAAO;MACVE,MAAM,GAAG;QACPN,CAAC,EAAER,SAAS,CAACQ,CAAC,GAAGR,SAAS,CAACS,KAAK;QAChCE,CAAC,EAAED;MACL,CAAC;MACD;IACF,KAAK,MAAM;MACTI,MAAM,GAAG;QACPN,CAAC,EAAER,SAAS,CAACQ,CAAC,GAAGP,QAAQ,CAACQ,KAAK;QAC/BE,CAAC,EAAED;MACL,CAAC;MACD;IACF;MACEI,MAAM,GAAG;QACPN,CAAC,EAAER,SAAS,CAACQ,CAAC;QACdG,CAAC,EAAEX,SAAS,CAACW;MACf,CAAC;EACL;EACA,QAAQ9B,YAAY,CAACiB,SAAS,CAAC;IAC7B,KAAK,OAAO;MACVgB,MAAM,CAACX,aAAa,CAAC,IAAIU,WAAW,IAAId,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;IACF,KAAK,KAAK;MACRQ,MAAM,CAACX,aAAa,CAAC,IAAIU,WAAW,IAAId,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACnE;EACJ;EACA,OAAOQ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,MAAAA,CAAOf,SAAS,EAAEC,QAAQ,EAAEe,MAAM,KAAK;EAC7D,MAAM;IACJlB,SAAS,GAAG,QAAQ;IACpBmB,QAAQ,GAAG,UAAU;IACrBC,UAAU,GAAG,EAAE;IACfC;EACF,CAAC,GAAGH,MAAM;EACV,MAAMI,eAAe,GAAGF,UAAU,CAACG,MAAM,CAACC,OAAO,CAAC;EAClD,MAAMvB,GAAG,GAAG,OAAOoB,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACtB,QAAQ,CAAC,CAAC;EAC9E,IAAIuB,KAAK,GAAG,MAAML,QAAQ,CAACM,eAAe,CAAC;IACzCzB,SAAS;IACTC,QAAQ;IACRgB;EACF,CAAC,CAAC;EACF,IAAI;IACFT,CAAC;IACDG;EACF,CAAC,GAAGf,0BAA0B,CAAC4B,KAAK,EAAE1B,SAAS,EAAEC,GAAG,CAAC;EACrD,IAAI2B,iBAAiB,GAAG5B,SAAS;EACjC,IAAI6B,cAAc,GAAG,CAAC,CAAC;EACvB,IAAIC,UAAU,GAAG,CAAC;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,eAAe,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/C,MAAM;MACJE,IAAI;MACJC;IACF,CAAC,GAAGZ,eAAe,CAACS,CAAC,CAAC;IACtB,MAAM;MACJrB,CAAC,EAAEyB,KAAK;MACRtB,CAAC,EAAEuB,KAAK;MACRC,IAAI;MACJC;IACF,CAAC,GAAG,MAAMJ,EAAE,CAAC;MACXxB,CAAC;MACDG,CAAC;MACD0B,gBAAgB,EAAEvC,SAAS;MAC3BA,SAAS,EAAE4B,iBAAiB;MAC5BT,QAAQ;MACRU,cAAc;MACdH,KAAK;MACLL,QAAQ;MACRmB,QAAQ,EAAE;QACRtC,SAAS;QACTC;MACF;IACF,CAAC,CAAC;IACFO,CAAC,GAAGyB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGzB,CAAC;IAC7BG,CAAC,GAAGuB,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGvB,CAAC;IAC7BgB,cAAc,GAAG;MACf,GAAGA,cAAc;MACjB,CAACI,IAAI,GAAG;QACN,GAAGJ,cAAc,CAACI,IAAI,CAAC;QACvB,GAAGI;MACL;IACF,CAAC;IACD,IAAIC,KAAK,IAAIR,UAAU,IAAI,EAAE,EAAE;MAC7BA,UAAU,EAAE;MACZ,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACtC,SAAS,EAAE;UACnB4B,iBAAiB,GAAGU,KAAK,CAACtC,SAAS;QACrC;QACA,IAAIsC,KAAK,CAACZ,KAAK,EAAE;UACfA,KAAK,GAAGY,KAAK,CAACZ,KAAK,KAAK,IAAI,GAAG,MAAML,QAAQ,CAACM,eAAe,CAAC;YAC5DzB,SAAS;YACTC,QAAQ;YACRgB;UACF,CAAC,CAAC,GAAGmB,KAAK,CAACZ,KAAK;QAClB;QACA,CAAC;UACChB,CAAC;UACDG;QACF,CAAC,GAAGf,0BAA0B,CAAC4B,KAAK,EAAEE,iBAAiB,EAAE3B,GAAG,CAAC;MAC/D;MACA8B,CAAC,GAAG,CAAC,CAAC;IACR;EACF;EACA,OAAO;IACLrB,CAAC;IACDG,CAAC;IACDb,SAAS,EAAE4B,iBAAiB;IAC5BT,QAAQ;IACRU;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeY,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5C,IAAIC,qBAAqB;EACzB,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,MAAM;IACJjC,CAAC;IACDG,CAAC;IACDQ,QAAQ;IACRK,KAAK;IACLc,QAAQ;IACRrB;EACF,CAAC,GAAGuB,KAAK;EACT,MAAM;IACJG,QAAQ,GAAG,mBAAmB;IAC9BC,YAAY,GAAG,UAAU;IACzBC,cAAc,GAAG,UAAU;IAC3BC,WAAW,GAAG,KAAK;IACnBC,OAAO,GAAG;EACZ,CAAC,GAAGjE,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;EAC5B,MAAMQ,aAAa,GAAGjE,gBAAgB,CAACgE,OAAO,CAAC;EAC/C,MAAME,UAAU,GAAGJ,cAAc,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU;EAC3E,MAAMK,OAAO,GAAGZ,QAAQ,CAACQ,WAAW,GAAGG,UAAU,GAAGJ,cAAc,CAAC;EACnE,MAAMM,kBAAkB,GAAGnE,gBAAgB,CAAC,MAAMmC,QAAQ,CAACiC,eAAe,CAAC;IACzEF,OAAO,EAAE,CAAC,CAACR,qBAAqB,GAAG,OAAOvB,QAAQ,CAACkC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGlC,QAAQ,CAACkC,SAAS,CAACH,OAAO,CAAC,CAAC,KAAK,IAAI,GAAGR,qBAAqB,GAAG,IAAI,IAAIQ,OAAO,GAAGA,OAAO,CAACI,cAAc,KAAK,OAAOnC,QAAQ,CAACoC,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGpC,QAAQ,CAACoC,kBAAkB,CAACjB,QAAQ,CAACrC,QAAQ,CAAC,CAAC,CAAC;IACnS0C,QAAQ;IACRC,YAAY;IACZ3B;EACF,CAAC,CAAC,CAAC;EACH,MAAMuC,IAAI,GAAGX,cAAc,KAAK,UAAU,GAAG;IAC3CrC,CAAC;IACDG,CAAC;IACDF,KAAK,EAAEe,KAAK,CAACvB,QAAQ,CAACQ,KAAK;IAC3BG,MAAM,EAAEY,KAAK,CAACvB,QAAQ,CAACW;EACzB,CAAC,GAAGY,KAAK,CAACxB,SAAS;EACnB,MAAMyD,YAAY,GAAG,OAAOtC,QAAQ,CAACuC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvC,QAAQ,CAACuC,eAAe,CAACpB,QAAQ,CAACrC,QAAQ,CAAC,CAAC;EACpH,MAAM0D,WAAW,GAAG,CAAC,OAAOxC,QAAQ,CAACkC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGlC,QAAQ,CAACkC,SAAS,CAACI,YAAY,CAAC,CAAC,IAAI,CAAC,OAAOtC,QAAQ,CAACyC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGzC,QAAQ,CAACyC,QAAQ,CAACH,YAAY,CAAC,CAAC,KAAK;IACvLjD,CAAC,EAAE,CAAC;IACJG,CAAC,EAAE;EACL,CAAC,GAAG;IACFH,CAAC,EAAE,CAAC;IACJG,CAAC,EAAE;EACL,CAAC;EACD,MAAMkD,iBAAiB,GAAG7E,gBAAgB,CAACmC,QAAQ,CAAC2C,qDAAqD,GAAG,MAAM3C,QAAQ,CAAC2C,qDAAqD,CAAC;IAC/KxB,QAAQ;IACRkB,IAAI;IACJC,YAAY;IACZxC;EACF,CAAC,CAAC,GAAGuC,IAAI,CAAC;EACV,OAAO;IACLO,GAAG,EAAE,CAACZ,kBAAkB,CAACY,GAAG,GAAGF,iBAAiB,CAACE,GAAG,GAAGf,aAAa,CAACe,GAAG,IAAIJ,WAAW,CAAChD,CAAC;IACzFqD,MAAM,EAAE,CAACH,iBAAiB,CAACG,MAAM,GAAGb,kBAAkB,CAACa,MAAM,GAAGhB,aAAa,CAACgB,MAAM,IAAIL,WAAW,CAAChD,CAAC;IACrGsD,IAAI,EAAE,CAACd,kBAAkB,CAACc,IAAI,GAAGJ,iBAAiB,CAACI,IAAI,GAAGjB,aAAa,CAACiB,IAAI,IAAIN,WAAW,CAACnD,CAAC;IAC7F0D,KAAK,EAAE,CAACL,iBAAiB,CAACK,KAAK,GAAGf,kBAAkB,CAACe,KAAK,GAAGlB,aAAa,CAACkB,KAAK,IAAIP,WAAW,CAACnD;EAClG,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM2D,KAAK,GAAG1B,OAAO,KAAK;EACxBV,IAAI,EAAE,OAAO;EACbU,OAAO;EACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;IACd,MAAM;MACJhC,CAAC;MACDG,CAAC;MACDb,SAAS;MACT0B,KAAK;MACLL,QAAQ;MACRmB,QAAQ;MACRX;IACF,CAAC,GAAGa,KAAK;IACT;IACA,MAAM;MACJU,OAAO;MACPH,OAAO,GAAG;IACZ,CAAC,GAAGjE,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,IAAIU,OAAO,IAAI,IAAI,EAAE;MACnB,OAAO,CAAC,CAAC;IACX;IACA,MAAMF,aAAa,GAAGjE,gBAAgB,CAACgE,OAAO,CAAC;IAC/C,MAAMjC,MAAM,GAAG;MACbN,CAAC;MACDG;IACF,CAAC;IACD,MAAMyD,IAAI,GAAG1F,gBAAgB,CAACoB,SAAS,CAAC;IACxC,MAAMgC,MAAM,GAAGnD,aAAa,CAACyF,IAAI,CAAC;IAClC,MAAMC,eAAe,GAAG,MAAMlD,QAAQ,CAACmD,aAAa,CAACpB,OAAO,CAAC;IAC7D,MAAMqB,OAAO,GAAGH,IAAI,KAAK,GAAG;IAC5B,MAAMI,OAAO,GAAGD,OAAO,GAAG,KAAK,GAAG,MAAM;IACxC,MAAME,OAAO,GAAGF,OAAO,GAAG,QAAQ,GAAG,OAAO;IAC5C,MAAMG,UAAU,GAAGH,OAAO,GAAG,cAAc,GAAG,aAAa;IAC3D,MAAMI,OAAO,GAAGnD,KAAK,CAACxB,SAAS,CAAC8B,MAAM,CAAC,GAAGN,KAAK,CAACxB,SAAS,CAACoE,IAAI,CAAC,GAAGtD,MAAM,CAACsD,IAAI,CAAC,GAAG5C,KAAK,CAACvB,QAAQ,CAAC6B,MAAM,CAAC;IACvG,MAAM8C,SAAS,GAAG9D,MAAM,CAACsD,IAAI,CAAC,GAAG5C,KAAK,CAACxB,SAAS,CAACoE,IAAI,CAAC;IACtD,MAAMS,iBAAiB,GAAG,OAAO1D,QAAQ,CAACuC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGvC,QAAQ,CAACuC,eAAe,CAACR,OAAO,CAAC,CAAC;IAC/G,IAAI4B,UAAU,GAAGD,iBAAiB,GAAGA,iBAAiB,CAACH,UAAU,CAAC,GAAG,CAAC;;IAEtE;IACA,IAAI,CAACI,UAAU,IAAI,EAAE,OAAO3D,QAAQ,CAACkC,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGlC,QAAQ,CAACkC,SAAS,CAACwB,iBAAiB,CAAC,CAAC,CAAC,EAAE;MACzGC,UAAU,GAAGxC,QAAQ,CAACrC,QAAQ,CAACyE,UAAU,CAAC,IAAIlD,KAAK,CAACvB,QAAQ,CAAC6B,MAAM,CAAC;IACtE;IACA,MAAMiD,iBAAiB,GAAGJ,OAAO,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC;;IAErD;IACA;IACA,MAAMI,sBAAsB,GAAGF,UAAU,GAAG,CAAC,GAAGT,eAAe,CAACvC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;IAC/E,MAAMmD,UAAU,GAAGhG,GAAG,CAAC+D,aAAa,CAACwB,OAAO,CAAC,EAAEQ,sBAAsB,CAAC;IACtE,MAAME,UAAU,GAAGjG,GAAG,CAAC+D,aAAa,CAACyB,OAAO,CAAC,EAAEO,sBAAsB,CAAC;;IAEtE;IACA;IACA,MAAMG,KAAK,GAAGF,UAAU;IACxB,MAAMvF,GAAG,GAAGoF,UAAU,GAAGT,eAAe,CAACvC,MAAM,CAAC,GAAGoD,UAAU;IAC7D,MAAME,MAAM,GAAGN,UAAU,GAAG,CAAC,GAAGT,eAAe,CAACvC,MAAM,CAAC,GAAG,CAAC,GAAGiD,iBAAiB;IAC/E,MAAMM,MAAM,GAAGnG,KAAK,CAACiG,KAAK,EAAEC,MAAM,EAAE1F,GAAG,CAAC;;IAExC;IACA;IACA;IACA;IACA,MAAM4F,eAAe,GAAG,CAAC3D,cAAc,CAACwC,KAAK,IAAItF,YAAY,CAACiB,SAAS,CAAC,IAAI,IAAI,IAAIsF,MAAM,KAAKC,MAAM,IAAI7D,KAAK,CAACxB,SAAS,CAAC8B,MAAM,CAAC,GAAG,CAAC,IAAIsD,MAAM,GAAGD,KAAK,GAAGF,UAAU,GAAGC,UAAU,CAAC,GAAGb,eAAe,CAACvC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;IACnN,MAAMyD,eAAe,GAAGD,eAAe,GAAGF,MAAM,GAAGD,KAAK,GAAGC,MAAM,GAAGD,KAAK,GAAGC,MAAM,GAAG1F,GAAG,GAAG,CAAC;IAC5F,OAAO;MACL,CAAC0E,IAAI,GAAGtD,MAAM,CAACsD,IAAI,CAAC,GAAGmB,eAAe;MACtCpD,IAAI,EAAE;QACJ,CAACiC,IAAI,GAAGiB,MAAM;QACdG,YAAY,EAAEJ,MAAM,GAAGC,MAAM,GAAGE,eAAe;QAC/C,IAAID,eAAe,IAAI;UACrBC;QACF,CAAC;MACH,CAAC;MACDnD,KAAK,EAAEkD;IACT,CAAC;EACH;AACF,CAAC,CAAC;AAEF,SAASG,gBAAgBA,CAACC,SAAS,EAAEC,aAAa,EAAEC,iBAAiB,EAAE;EACrE,MAAMC,kCAAkC,GAAGH,SAAS,GAAG,CAAC,GAAGE,iBAAiB,CAACvE,MAAM,CAACvB,SAAS,IAAIjB,YAAY,CAACiB,SAAS,CAAC,KAAK4F,SAAS,CAAC,EAAE,GAAGE,iBAAiB,CAACvE,MAAM,CAACvB,SAAS,IAAIjB,YAAY,CAACiB,SAAS,CAAC,KAAK4F,SAAS,CAAC,CAAC,GAAGE,iBAAiB,CAACvE,MAAM,CAACvB,SAAS,IAAIlB,OAAO,CAACkB,SAAS,CAAC,KAAKA,SAAS,CAAC;EACnS,OAAO+F,kCAAkC,CAACxE,MAAM,CAACvB,SAAS,IAAI;IAC5D,IAAI4F,SAAS,EAAE;MACb,OAAO7G,YAAY,CAACiB,SAAS,CAAC,KAAK4F,SAAS,KAAKC,aAAa,GAAGtG,6BAA6B,CAACS,SAAS,CAAC,KAAKA,SAAS,GAAG,KAAK,CAAC;IAClI;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgG,aAAa,GAAG,SAAAA,CAAUrD,OAAO,EAAE;EACvC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLV,IAAI,EAAE,eAAe;IACrBU,OAAO;IACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;MACd,IAAIuD,qBAAqB,EAAEC,sBAAsB,EAAEC,qBAAqB;MACxE,MAAM;QACJzE,KAAK;QACLG,cAAc;QACd7B,SAAS;QACTqB,QAAQ;QACRmB;MACF,CAAC,GAAGE,KAAK;MACT,MAAM;QACJ0D,SAAS,GAAG,KAAK;QACjBR,SAAS;QACTE,iBAAiB,GAAGzG,UAAU;QAC9BwG,aAAa,GAAG,IAAI;QACpB,GAAGQ;MACL,CAAC,GAAGrH,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;MAC5B,MAAM4D,YAAY,GAAGV,SAAS,KAAKW,SAAS,IAAIT,iBAAiB,KAAKzG,UAAU,GAAGsG,gBAAgB,CAACC,SAAS,IAAI,IAAI,EAAEC,aAAa,EAAEC,iBAAiB,CAAC,GAAGA,iBAAiB;MAC5K,MAAMU,QAAQ,GAAG,MAAM/D,cAAc,CAACC,KAAK,EAAE2D,qBAAqB,CAAC;MACnE,MAAMI,YAAY,GAAG,CAAC,CAACR,qBAAqB,GAAGpE,cAAc,CAACmE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,qBAAqB,CAACS,KAAK,KAAK,CAAC;MACjI,MAAMC,gBAAgB,GAAGL,YAAY,CAACG,YAAY,CAAC;MACnD,IAAIE,gBAAgB,IAAI,IAAI,EAAE;QAC5B,OAAO,CAAC,CAAC;MACX;MACA,MAAMC,cAAc,GAAGtH,iBAAiB,CAACqH,gBAAgB,EAAEjF,KAAK,EAAE,OAAOL,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC,CAAC;;MAE9I;MACA,IAAIH,SAAS,KAAK2G,gBAAgB,EAAE;QAClC,OAAO;UACLrE,KAAK,EAAE;YACLtC,SAAS,EAAEsG,YAAY,CAAC,CAAC;UAC3B;QACF,CAAC;MACH;MACA,MAAMO,gBAAgB,GAAG,CAACL,QAAQ,CAAC1H,OAAO,CAAC6H,gBAAgB,CAAC,CAAC,EAAEH,QAAQ,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEJ,QAAQ,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACxH,MAAME,YAAY,GAAG,CAAC,IAAI,CAAC,CAACZ,sBAAsB,GAAGrE,cAAc,CAACmE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,sBAAsB,CAACa,SAAS,KAAK,EAAE,CAAC,EAAE;QAC9I/G,SAAS,EAAE2G,gBAAgB;QAC3BI,SAAS,EAAEF;MACb,CAAC,CAAC;MACF,MAAMG,aAAa,GAAGV,YAAY,CAACG,YAAY,GAAG,CAAC,CAAC;;MAEpD;MACA,IAAIO,aAAa,EAAE;QACjB,OAAO;UACL3E,IAAI,EAAE;YACJqE,KAAK,EAAED,YAAY,GAAG,CAAC;YACvBM,SAAS,EAAED;UACb,CAAC;UACDxE,KAAK,EAAE;YACLtC,SAAS,EAAEgH;UACb;QACF,CAAC;MACH;MACA,MAAMC,2BAA2B,GAAGH,YAAY,CAACI,GAAG,CAACC,CAAC,IAAI;QACxD,MAAMvB,SAAS,GAAG7G,YAAY,CAACoI,CAAC,CAACnH,SAAS,CAAC;QAC3C,OAAO,CAACmH,CAAC,CAACnH,SAAS,EAAE4F,SAAS,IAAIQ,SAAS;QAC3C;QACAe,CAAC,CAACJ,SAAS,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,EAAE,CAAC,CAAC;QACtD;QACAJ,CAAC,CAACJ,SAAS,CAAC,CAAC,CAAC,EAAEI,CAAC,CAACJ,SAAS,CAAC;MAC9B,CAAC,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,MAAMC,2BAA2B,GAAGV,2BAA2B,CAAC1F,MAAM,CAAC4F,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACxF;MACA;MACArI,YAAY,CAACoI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACS,KAAK,CAACL,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,CAAC;MAC/C,MAAMM,cAAc,GAAG,CAAC,CAAC1B,qBAAqB,GAAGwB,2BAA2B,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGxB,qBAAqB,CAAC,CAAC,CAAC,KAAKc,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClK,IAAIY,cAAc,KAAK7H,SAAS,EAAE;QAChC,OAAO;UACLqC,IAAI,EAAE;YACJqE,KAAK,EAAED,YAAY,GAAG,CAAC;YACvBM,SAAS,EAAED;UACb,CAAC;UACDxE,KAAK,EAAE;YACLtC,SAAS,EAAE6H;UACb;QACF,CAAC;MACH;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAG,SAAAA,CAAUnF,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLV,IAAI,EAAE,MAAM;IACZU,OAAO;IACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;MACd,IAAIqF,qBAAqB,EAAEC,oBAAoB;MAC/C,MAAM;QACJhI,SAAS;QACT6B,cAAc;QACdH,KAAK;QACLa,gBAAgB;QAChBlB,QAAQ;QACRmB;MACF,CAAC,GAAGE,KAAK;MACT,MAAM;QACJuF,QAAQ,EAAEC,aAAa,GAAG,IAAI;QAC9B9B,SAAS,EAAE+B,cAAc,GAAG,IAAI;QAChCC,kBAAkB,EAAEC,2BAA2B;QAC/CC,gBAAgB,GAAG,SAAS;QAC5BC,yBAAyB,GAAG,MAAM;QAClCC,aAAa,GAAG,IAAI;QACpB,GAAGnC;MACL,CAAC,GAAGrH,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;;MAE5B;MACA;MACA;MACA;MACA,IAAI,CAACqF,qBAAqB,GAAGlG,cAAc,CAACwC,KAAK,KAAK,IAAI,IAAI0D,qBAAqB,CAACtC,eAAe,EAAE;QACnG,OAAO,CAAC,CAAC;MACX;MACA,MAAMlF,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;MAC/B,MAAMyI,eAAe,GAAG9J,WAAW,CAAC4D,gBAAgB,CAAC;MACrD,MAAMmG,eAAe,GAAG5J,OAAO,CAACyD,gBAAgB,CAAC,KAAKA,gBAAgB;MACtE,MAAMtC,GAAG,GAAG,OAAOoB,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC;MACvF,MAAMiI,kBAAkB,GAAGC,2BAA2B,KAAKK,eAAe,IAAI,CAACF,aAAa,GAAG,CAAChJ,oBAAoB,CAAC+C,gBAAgB,CAAC,CAAC,GAAG9C,qBAAqB,CAAC8C,gBAAgB,CAAC,CAAC;MAClL,MAAMoG,4BAA4B,GAAGJ,yBAAyB,KAAK,MAAM;MACzE,IAAI,CAACF,2BAA2B,IAAIM,4BAA4B,EAAE;QAChEP,kBAAkB,CAACQ,IAAI,CAAC,GAAGlJ,yBAAyB,CAAC6C,gBAAgB,EAAEiG,aAAa,EAAED,yBAAyB,EAAEtI,GAAG,CAAC,CAAC;MACxH;MACA,MAAMZ,UAAU,GAAG,CAACkD,gBAAgB,EAAE,GAAG6F,kBAAkB,CAAC;MAC5D,MAAM5B,QAAQ,GAAG,MAAM/D,cAAc,CAACC,KAAK,EAAE2D,qBAAqB,CAAC;MACnE,MAAMU,SAAS,GAAG,EAAE;MACpB,IAAI8B,aAAa,GAAG,CAAC,CAACb,oBAAoB,GAAGnG,cAAc,CAACiG,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,oBAAoB,CAACjB,SAAS,KAAK,EAAE;MAC1H,IAAImB,aAAa,EAAE;QACjBnB,SAAS,CAAC6B,IAAI,CAACpC,QAAQ,CAACjG,IAAI,CAAC,CAAC;MAChC;MACA,IAAI4H,cAAc,EAAE;QAClB,MAAMxI,KAAK,GAAGL,iBAAiB,CAACU,SAAS,EAAE0B,KAAK,EAAEzB,GAAG,CAAC;QACtD8G,SAAS,CAAC6B,IAAI,CAACpC,QAAQ,CAAC7G,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE6G,QAAQ,CAAC7G,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD;MACAkJ,aAAa,GAAG,CAAC,GAAGA,aAAa,EAAE;QACjC7I,SAAS;QACT+G;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACA,SAAS,CAACa,KAAK,CAACrH,IAAI,IAAIA,IAAI,IAAI,CAAC,CAAC,EAAE;QACvC,IAAIuI,qBAAqB,EAAEC,qBAAqB;QAChD,MAAMC,SAAS,GAAG,CAAC,CAAC,CAACF,qBAAqB,GAAGjH,cAAc,CAACiG,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,qBAAqB,CAACpC,KAAK,KAAK,CAAC,IAAI,CAAC;QAC3H,MAAMM,aAAa,GAAG3H,UAAU,CAAC2J,SAAS,CAAC;QAC3C,IAAIhC,aAAa,EAAE;UACjB,MAAMiC,uBAAuB,GAAGd,cAAc,KAAK,WAAW,GAAGM,eAAe,KAAK9J,WAAW,CAACqI,aAAa,CAAC,GAAG,KAAK;UACvH,IAAI,CAACiC,uBAAuB;UAC5B;UACA;UACAJ,aAAa,CAACjB,KAAK,CAACT,CAAC,IAAIA,CAAC,CAACJ,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIpI,WAAW,CAACwI,CAAC,CAACnH,SAAS,CAAC,KAAKyI,eAAe,CAAC,EAAE;YAC5F;YACA,OAAO;cACLpG,IAAI,EAAE;gBACJqE,KAAK,EAAEsC,SAAS;gBAChBjC,SAAS,EAAE8B;cACb,CAAC;cACDvG,KAAK,EAAE;gBACLtC,SAAS,EAAEgH;cACb;YACF,CAAC;UACH;QACF;;QAEA;QACA;QACA,IAAIa,cAAc,GAAG,CAACkB,qBAAqB,GAAGF,aAAa,CAACtH,MAAM,CAAC4F,CAAC,IAAIA,CAAC,CAACJ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACV,SAAS,CAAC,CAAC,CAAC,GAAGW,CAAC,CAACX,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgC,qBAAqB,CAAC/I,SAAS;;QAEnM;QACA,IAAI,CAAC6H,cAAc,EAAE;UACnB,QAAQS,gBAAgB;YACtB,KAAK,SAAS;cACZ;gBACE,IAAIY,sBAAsB;gBAC1B,MAAMlJ,SAAS,GAAG,CAACkJ,sBAAsB,GAAGL,aAAa,CAACtH,MAAM,CAAC4F,CAAC,IAAI;kBACpE,IAAIwB,4BAA4B,EAAE;oBAChC,MAAMQ,eAAe,GAAGxK,WAAW,CAACwI,CAAC,CAACnH,SAAS,CAAC;oBAChD,OAAOmJ,eAAe,KAAKV,eAAe;oBAC1C;oBACA;oBACAU,eAAe,KAAK,GAAG;kBACzB;kBACA,OAAO,IAAI;gBACb,CAAC,CAAC,CAACjC,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,CAACnH,SAAS,EAAEmH,CAAC,CAACJ,SAAS,CAACxF,MAAM,CAACiF,QAAQ,IAAIA,QAAQ,GAAG,CAAC,CAAC,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEd,QAAQ,KAAKc,GAAG,GAAGd,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwB,sBAAsB,CAAC,CAAC,CAAC;gBAClM,IAAIlJ,SAAS,EAAE;kBACb6H,cAAc,GAAG7H,SAAS;gBAC5B;gBACA;cACF;YACF,KAAK,kBAAkB;cACrB6H,cAAc,GAAGtF,gBAAgB;cACjC;UACJ;QACF;QACA,IAAIvC,SAAS,KAAK6H,cAAc,EAAE;UAChC,OAAO;YACLvF,KAAK,EAAE;cACLtC,SAAS,EAAE6H;YACb;UACF,CAAC;QACH;MACF;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;AAED,SAASuB,cAAcA,CAAC5C,QAAQ,EAAE9C,IAAI,EAAE;EACtC,OAAO;IACLO,GAAG,EAAEuC,QAAQ,CAACvC,GAAG,GAAGP,IAAI,CAAC5C,MAAM;IAC/BsD,KAAK,EAAEoC,QAAQ,CAACpC,KAAK,GAAGV,IAAI,CAAC/C,KAAK;IAClCuD,MAAM,EAAEsC,QAAQ,CAACtC,MAAM,GAAGR,IAAI,CAAC5C,MAAM;IACrCqD,IAAI,EAAEqC,QAAQ,CAACrC,IAAI,GAAGT,IAAI,CAAC/C;EAC7B,CAAC;AACH;AACA,SAAS0I,qBAAqBA,CAAC7C,QAAQ,EAAE;EACvC,OAAO7G,KAAK,CAAC2J,IAAI,CAAC/I,IAAI,IAAIiG,QAAQ,CAACjG,IAAI,CAAC,IAAI,CAAC,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgJ,IAAI,GAAG,SAAAA,CAAU5G,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLV,IAAI,EAAE,MAAM;IACZU,OAAO;IACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;MACd,MAAM;QACJhB;MACF,CAAC,GAAGgB,KAAK;MACT,MAAM;QACJvB,QAAQ,GAAG,iBAAiB;QAC5B,GAAGkF;MACL,CAAC,GAAGrH,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;MAC5B,QAAQvB,QAAQ;QACd,KAAK,iBAAiB;UACpB;YACE,MAAMqF,QAAQ,GAAG,MAAM/D,cAAc,CAACC,KAAK,EAAE;cAC3C,GAAG2D,qBAAqB;cACxBtD,cAAc,EAAE;YAClB,CAAC,CAAC;YACF,MAAMyG,OAAO,GAAGJ,cAAc,CAAC5C,QAAQ,EAAE9E,KAAK,CAACxB,SAAS,CAAC;YACzD,OAAO;cACLmC,IAAI,EAAE;gBACJoH,sBAAsB,EAAED,OAAO;gBAC/BE,eAAe,EAAEL,qBAAqB,CAACG,OAAO;cAChD;YACF,CAAC;UACH;QACF,KAAK,SAAS;UACZ;YACE,MAAMhD,QAAQ,GAAG,MAAM/D,cAAc,CAACC,KAAK,EAAE;cAC3C,GAAG2D,qBAAqB;cACxBrD,WAAW,EAAE;YACf,CAAC,CAAC;YACF,MAAMwG,OAAO,GAAGJ,cAAc,CAAC5C,QAAQ,EAAE9E,KAAK,CAACvB,QAAQ,CAAC;YACxD,OAAO;cACLkC,IAAI,EAAE;gBACJsH,cAAc,EAAEH,OAAO;gBACvBI,OAAO,EAAEP,qBAAqB,CAACG,OAAO;cACxC;YACF,CAAC;UACH;QACF;UACE;YACE,OAAO,CAAC,CAAC;UACX;MACJ;IACF;EACF,CAAC;AACH,CAAC;AAED,SAASK,eAAeA,CAACnI,KAAK,EAAE;EAC9B,MAAMoI,IAAI,GAAG3K,GAAG,CAAC,GAAGuC,KAAK,CAACwF,GAAG,CAACxD,IAAI,IAAIA,IAAI,CAACS,IAAI,CAAC,CAAC;EACjD,MAAM4F,IAAI,GAAG5K,GAAG,CAAC,GAAGuC,KAAK,CAACwF,GAAG,CAACxD,IAAI,IAAIA,IAAI,CAACO,GAAG,CAAC,CAAC;EAChD,MAAM+F,IAAI,GAAGpK,GAAG,CAAC,GAAG8B,KAAK,CAACwF,GAAG,CAACxD,IAAI,IAAIA,IAAI,CAACU,KAAK,CAAC,CAAC;EAClD,MAAM6F,IAAI,GAAGrK,GAAG,CAAC,GAAG8B,KAAK,CAACwF,GAAG,CAACxD,IAAI,IAAIA,IAAI,CAACQ,MAAM,CAAC,CAAC;EACnD,OAAO;IACLxD,CAAC,EAAEoJ,IAAI;IACPjJ,CAAC,EAAEkJ,IAAI;IACPpJ,KAAK,EAAEqJ,IAAI,GAAGF,IAAI;IAClBhJ,MAAM,EAAEmJ,IAAI,GAAGF;EACjB,CAAC;AACH;AACA,SAASG,cAAcA,CAACxI,KAAK,EAAE;EAC7B,MAAMyI,WAAW,GAAGzI,KAAK,CAAC0F,KAAK,CAAC,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5G,CAAC,GAAG6G,CAAC,CAAC7G,CAAC,CAAC;EAC3D,MAAMuJ,MAAM,GAAG,EAAE;EACjB,IAAIC,QAAQ,GAAG,IAAI;EACnB,KAAK,IAAItI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoI,WAAW,CAACnI,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,MAAM2B,IAAI,GAAGyG,WAAW,CAACpI,CAAC,CAAC;IAC3B,IAAI,CAACsI,QAAQ,IAAI3G,IAAI,CAAC7C,CAAC,GAAGwJ,QAAQ,CAACxJ,CAAC,GAAGwJ,QAAQ,CAACvJ,MAAM,GAAG,CAAC,EAAE;MAC1DsJ,MAAM,CAACxB,IAAI,CAAC,CAAClF,IAAI,CAAC,CAAC;IACrB,CAAC,MAAM;MACL0G,MAAM,CAACA,MAAM,CAACpI,MAAM,GAAG,CAAC,CAAC,CAAC4G,IAAI,CAAClF,IAAI,CAAC;IACtC;IACA2G,QAAQ,GAAG3G,IAAI;EACjB;EACA,OAAO0G,MAAM,CAAClD,GAAG,CAACxD,IAAI,IAAIxE,gBAAgB,CAAC2K,eAAe,CAACnG,IAAI,CAAC,CAAC,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4G,MAAM,GAAG,SAAAA,CAAU3H,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLV,IAAI,EAAE,QAAQ;IACdU,OAAO;IACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;MACd,MAAM;QACJ1C,SAAS;QACTwC,QAAQ;QACRd,KAAK;QACLL,QAAQ;QACRF;MACF,CAAC,GAAGuB,KAAK;MACT;MACA;MACA;MACA,MAAM;QACJO,OAAO,GAAG,CAAC;QACXvC,CAAC;QACDG;MACF,CAAC,GAAG7B,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;MAC5B,MAAM6H,iBAAiB,GAAGC,KAAK,CAACC,IAAI,CAAC,CAAC,OAAOpJ,QAAQ,CAACqJ,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrJ,QAAQ,CAACqJ,cAAc,CAAClI,QAAQ,CAACtC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;MAC5I,MAAMyK,WAAW,GAAGT,cAAc,CAACK,iBAAiB,CAAC;MACrD,MAAMK,QAAQ,GAAG1L,gBAAgB,CAAC2K,eAAe,CAACU,iBAAiB,CAAC,CAAC;MACrE,MAAMrH,aAAa,GAAGjE,gBAAgB,CAACgE,OAAO,CAAC;MAC/C,SAAS4H,qBAAqBA,CAAA,EAAG;QAC/B;QACA,IAAIF,WAAW,CAAC3I,MAAM,KAAK,CAAC,IAAI2I,WAAW,CAAC,CAAC,CAAC,CAACxG,IAAI,GAAGwG,WAAW,CAAC,CAAC,CAAC,CAACvG,KAAK,IAAI1D,CAAC,IAAI,IAAI,IAAIG,CAAC,IAAI,IAAI,EAAE;UACpG;UACA,OAAO8J,WAAW,CAACG,IAAI,CAACpH,IAAI,IAAIhD,CAAC,GAAGgD,IAAI,CAACS,IAAI,GAAGjB,aAAa,CAACiB,IAAI,IAAIzD,CAAC,GAAGgD,IAAI,CAACU,KAAK,GAAGlB,aAAa,CAACkB,KAAK,IAAIvD,CAAC,GAAG6C,IAAI,CAACO,GAAG,GAAGf,aAAa,CAACe,GAAG,IAAIpD,CAAC,GAAG6C,IAAI,CAACQ,MAAM,GAAGhB,aAAa,CAACgB,MAAM,CAAC,IAAI0G,QAAQ;QACvM;;QAEA;QACA,IAAID,WAAW,CAAC3I,MAAM,IAAI,CAAC,EAAE;UAC3B,IAAIrD,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG,EAAE;YAClC,MAAM+K,SAAS,GAAGJ,WAAW,CAAC,CAAC,CAAC;YAChC,MAAMK,QAAQ,GAAGL,WAAW,CAACA,WAAW,CAAC3I,MAAM,GAAG,CAAC,CAAC;YACpD,MAAMiJ,KAAK,GAAGnM,OAAO,CAACkB,SAAS,CAAC,KAAK,KAAK;YAC1C,MAAMiE,GAAG,GAAG8G,SAAS,CAAC9G,GAAG;YACzB,MAAMC,MAAM,GAAG8G,QAAQ,CAAC9G,MAAM;YAC9B,MAAMC,IAAI,GAAG8G,KAAK,GAAGF,SAAS,CAAC5G,IAAI,GAAG6G,QAAQ,CAAC7G,IAAI;YACnD,MAAMC,KAAK,GAAG6G,KAAK,GAAGF,SAAS,CAAC3G,KAAK,GAAG4G,QAAQ,CAAC5G,KAAK;YACtD,MAAMzD,KAAK,GAAGyD,KAAK,GAAGD,IAAI;YAC1B,MAAMrD,MAAM,GAAGoD,MAAM,GAAGD,GAAG;YAC3B,OAAO;cACLA,GAAG;cACHC,MAAM;cACNC,IAAI;cACJC,KAAK;cACLzD,KAAK;cACLG,MAAM;cACNJ,CAAC,EAAEyD,IAAI;cACPtD,CAAC,EAAEoD;YACL,CAAC;UACH;UACA,MAAMiH,UAAU,GAAGpM,OAAO,CAACkB,SAAS,CAAC,KAAK,MAAM;UAChD,MAAMmL,QAAQ,GAAGvL,GAAG,CAAC,GAAG+K,WAAW,CAACzD,GAAG,CAACxD,IAAI,IAAIA,IAAI,CAACU,KAAK,CAAC,CAAC;UAC5D,MAAMgH,OAAO,GAAGjM,GAAG,CAAC,GAAGwL,WAAW,CAACzD,GAAG,CAACxD,IAAI,IAAIA,IAAI,CAACS,IAAI,CAAC,CAAC;UAC1D,MAAMkH,YAAY,GAAGV,WAAW,CAACpJ,MAAM,CAACmC,IAAI,IAAIwH,UAAU,GAAGxH,IAAI,CAACS,IAAI,KAAKiH,OAAO,GAAG1H,IAAI,CAACU,KAAK,KAAK+G,QAAQ,CAAC;UAC7G,MAAMlH,GAAG,GAAGoH,YAAY,CAAC,CAAC,CAAC,CAACpH,GAAG;UAC/B,MAAMC,MAAM,GAAGmH,YAAY,CAACA,YAAY,CAACrJ,MAAM,GAAG,CAAC,CAAC,CAACkC,MAAM;UAC3D,MAAMC,IAAI,GAAGiH,OAAO;UACpB,MAAMhH,KAAK,GAAG+G,QAAQ;UACtB,MAAMxK,KAAK,GAAGyD,KAAK,GAAGD,IAAI;UAC1B,MAAMrD,MAAM,GAAGoD,MAAM,GAAGD,GAAG;UAC3B,OAAO;YACLA,GAAG;YACHC,MAAM;YACNC,IAAI;YACJC,KAAK;YACLzD,KAAK;YACLG,MAAM;YACNJ,CAAC,EAAEyD,IAAI;YACPtD,CAAC,EAAEoD;UACL,CAAC;QACH;QACA,OAAO2G,QAAQ;MACjB;MACA,MAAMU,UAAU,GAAG,MAAMjK,QAAQ,CAACM,eAAe,CAAC;QAChDzB,SAAS,EAAE;UACT2K;QACF,CAAC;QACD1K,QAAQ,EAAEqC,QAAQ,CAACrC,QAAQ;QAC3BgB;MACF,CAAC,CAAC;MACF,IAAIO,KAAK,CAACxB,SAAS,CAACQ,CAAC,KAAK4K,UAAU,CAACpL,SAAS,CAACQ,CAAC,IAAIgB,KAAK,CAACxB,SAAS,CAACW,CAAC,KAAKyK,UAAU,CAACpL,SAAS,CAACW,CAAC,IAAIa,KAAK,CAACxB,SAAS,CAACS,KAAK,KAAK2K,UAAU,CAACpL,SAAS,CAACS,KAAK,IAAIe,KAAK,CAACxB,SAAS,CAACY,MAAM,KAAKwK,UAAU,CAACpL,SAAS,CAACY,MAAM,EAAE;QAClN,OAAO;UACLwB,KAAK,EAAE;YACLZ,KAAK,EAAE4J;UACT;QACF,CAAC;MACH;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;;AAED;AACA;;AAEA,eAAeC,oBAAoBA,CAAC7I,KAAK,EAAEC,OAAO,EAAE;EAClD,MAAM;IACJ3C,SAAS;IACTqB,QAAQ;IACRmB;EACF,CAAC,GAAGE,KAAK;EACT,MAAMzC,GAAG,GAAG,OAAOoB,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC;EACvF,MAAMI,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;EAC/B,MAAM4F,SAAS,GAAG7G,YAAY,CAACiB,SAAS,CAAC;EACzC,MAAMQ,UAAU,GAAG7B,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG;EACjD,MAAMwL,aAAa,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAAClL,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC7D,MAAMmL,cAAc,GAAGzL,GAAG,IAAIO,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;EACjD,MAAMmL,QAAQ,GAAG3M,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;;EAEzC;EACA,IAAI;IACFuF,QAAQ;IACR7B,SAAS;IACT/F;EACF,CAAC,GAAG,OAAOsL,QAAQ,KAAK,QAAQ,GAAG;IACjC1D,QAAQ,EAAE0D,QAAQ;IAClBvF,SAAS,EAAE,CAAC;IACZ/F,aAAa,EAAE;EACjB,CAAC,GAAG;IACF4H,QAAQ,EAAE0D,QAAQ,CAAC1D,QAAQ,IAAI,CAAC;IAChC7B,SAAS,EAAEuF,QAAQ,CAACvF,SAAS,IAAI,CAAC;IAClC/F,aAAa,EAAEsL,QAAQ,CAACtL;EAC1B,CAAC;EACD,IAAIuF,SAAS,IAAI,OAAOvF,aAAa,KAAK,QAAQ,EAAE;IAClD+F,SAAS,GAAGR,SAAS,KAAK,KAAK,GAAGvF,aAAa,GAAG,CAAC,CAAC,GAAGA,aAAa;EACtE;EACA,OAAOG,UAAU,GAAG;IAClBE,CAAC,EAAE0F,SAAS,GAAGsF,cAAc;IAC7B7K,CAAC,EAAEoH,QAAQ,GAAGuD;EAChB,CAAC,GAAG;IACF9K,CAAC,EAAEuH,QAAQ,GAAGuD,aAAa;IAC3B3K,CAAC,EAAEuF,SAAS,GAAGsF;EACjB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMnG,MAAM,GAAG,SAAAA,CAAU5C,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC;EACb;EACA,OAAO;IACLV,IAAI,EAAE,QAAQ;IACdU,OAAO;IACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;MACd,IAAIkJ,qBAAqB,EAAE7D,qBAAqB;MAChD,MAAM;QACJrH,CAAC;QACDG,CAAC;QACDb,SAAS;QACT6B;MACF,CAAC,GAAGa,KAAK;MACT,MAAMmJ,UAAU,GAAG,MAAMN,oBAAoB,CAAC7I,KAAK,EAAEC,OAAO,CAAC;;MAE7D;MACA;MACA,IAAI3C,SAAS,MAAM,CAAC4L,qBAAqB,GAAG/J,cAAc,CAAC0D,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqG,qBAAqB,CAAC5L,SAAS,CAAC,IAAI,CAAC+H,qBAAqB,GAAGlG,cAAc,CAACwC,KAAK,KAAK,IAAI,IAAI0D,qBAAqB,CAACtC,eAAe,EAAE;QACzN,OAAO,CAAC,CAAC;MACX;MACA,OAAO;QACL/E,CAAC,EAAEA,CAAC,GAAGmL,UAAU,CAACnL,CAAC;QACnBG,CAAC,EAAEA,CAAC,GAAGgL,UAAU,CAAChL,CAAC;QACnBwB,IAAI,EAAE;UACJ,GAAGwJ,UAAU;UACb7L;QACF;MACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAM8L,KAAK,GAAG,SAAAA,CAAUnJ,OAAO,EAAE;EAC/B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLV,IAAI,EAAE,OAAO;IACbU,OAAO;IACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;MACd,MAAM;QACJhC,CAAC;QACDG,CAAC;QACDb;MACF,CAAC,GAAG0C,KAAK;MACT,MAAM;QACJuF,QAAQ,EAAEC,aAAa,GAAG,IAAI;QAC9B9B,SAAS,EAAE+B,cAAc,GAAG,KAAK;QACjC4D,OAAO,GAAG;UACR7J,EAAE,EAAEnC,IAAI,IAAI;YACV,IAAI;cACFW,CAAC;cACDG;YACF,CAAC,GAAGd,IAAI;YACR,OAAO;cACLW,CAAC;cACDG;YACF,CAAC;UACH;QACF,CAAC;QACD,GAAGwF;MACL,CAAC,GAAGrH,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;MAC5B,MAAM1B,MAAM,GAAG;QACbN,CAAC;QACDG;MACF,CAAC;MACD,MAAM2F,QAAQ,GAAG,MAAM/D,cAAc,CAACC,KAAK,EAAE2D,qBAAqB,CAAC;MACnE,MAAMD,SAAS,GAAGzH,WAAW,CAACG,OAAO,CAACkB,SAAS,CAAC,CAAC;MACjD,MAAMiI,QAAQ,GAAGpI,eAAe,CAACuG,SAAS,CAAC;MAC3C,IAAI4F,aAAa,GAAGhL,MAAM,CAACiH,QAAQ,CAAC;MACpC,IAAIgE,cAAc,GAAGjL,MAAM,CAACoF,SAAS,CAAC;MACtC,IAAI8B,aAAa,EAAE;QACjB,MAAMgE,OAAO,GAAGjE,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;QACjD,MAAMkE,OAAO,GAAGlE,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACrD,MAAM9I,GAAG,GAAG6M,aAAa,GAAGxF,QAAQ,CAAC0F,OAAO,CAAC;QAC7C,MAAMtM,GAAG,GAAGoM,aAAa,GAAGxF,QAAQ,CAAC2F,OAAO,CAAC;QAC7CH,aAAa,GAAG5M,KAAK,CAACD,GAAG,EAAE6M,aAAa,EAAEpM,GAAG,CAAC;MAChD;MACA,IAAIuI,cAAc,EAAE;QAClB,MAAM+D,OAAO,GAAG9F,SAAS,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;QAClD,MAAM+F,OAAO,GAAG/F,SAAS,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACtD,MAAMjH,GAAG,GAAG8M,cAAc,GAAGzF,QAAQ,CAAC0F,OAAO,CAAC;QAC9C,MAAMtM,GAAG,GAAGqM,cAAc,GAAGzF,QAAQ,CAAC2F,OAAO,CAAC;QAC9CF,cAAc,GAAG7M,KAAK,CAACD,GAAG,EAAE8M,cAAc,EAAErM,GAAG,CAAC;MAClD;MACA,MAAMwM,aAAa,GAAGL,OAAO,CAAC7J,EAAE,CAAC;QAC/B,GAAGQ,KAAK;QACR,CAACuF,QAAQ,GAAG+D,aAAa;QACzB,CAAC5F,SAAS,GAAG6F;MACf,CAAC,CAAC;MACF,OAAO;QACL,GAAGG,aAAa;QAChB/J,IAAI,EAAE;UACJ3B,CAAC,EAAE0L,aAAa,CAAC1L,CAAC,GAAGA,CAAC;UACtBG,CAAC,EAAEuL,aAAa,CAACvL,CAAC,GAAGA,CAAC;UACtBwL,OAAO,EAAE;YACP,CAACpE,QAAQ,GAAGC,aAAa;YACzB,CAAC9B,SAAS,GAAG+B;UACf;QACF;MACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD;AACA;AACA;AACA,MAAMmE,UAAU,GAAG,SAAAA,CAAU3J,OAAO,EAAE;EACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLA,OAAO;IACPT,EAAEA,CAACQ,KAAK,EAAE;MACR,MAAM;QACJhC,CAAC;QACDG,CAAC;QACDb,SAAS;QACT0B,KAAK;QACLG;MACF,CAAC,GAAGa,KAAK;MACT,MAAM;QACJ6C,MAAM,GAAG,CAAC;QACV0C,QAAQ,EAAEC,aAAa,GAAG,IAAI;QAC9B9B,SAAS,EAAE+B,cAAc,GAAG;MAC9B,CAAC,GAAGnJ,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;MAC5B,MAAM1B,MAAM,GAAG;QACbN,CAAC;QACDG;MACF,CAAC;MACD,MAAMuF,SAAS,GAAGzH,WAAW,CAACqB,SAAS,CAAC;MACxC,MAAMiI,QAAQ,GAAGpI,eAAe,CAACuG,SAAS,CAAC;MAC3C,IAAI4F,aAAa,GAAGhL,MAAM,CAACiH,QAAQ,CAAC;MACpC,IAAIgE,cAAc,GAAGjL,MAAM,CAACoF,SAAS,CAAC;MACtC,MAAMmG,SAAS,GAAGvN,QAAQ,CAACuG,MAAM,EAAE7C,KAAK,CAAC;MACzC,MAAM8J,cAAc,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAG;QACrDtE,QAAQ,EAAEsE,SAAS;QACnBnG,SAAS,EAAE;MACb,CAAC,GAAG;QACF6B,QAAQ,EAAE,CAAC;QACX7B,SAAS,EAAE,CAAC;QACZ,GAAGmG;MACL,CAAC;MACD,IAAIrE,aAAa,EAAE;QACjB,MAAMuE,GAAG,GAAGxE,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;QACjD,MAAMyE,QAAQ,GAAGhL,KAAK,CAACxB,SAAS,CAAC+H,QAAQ,CAAC,GAAGvG,KAAK,CAACvB,QAAQ,CAACsM,GAAG,CAAC,GAAGD,cAAc,CAACvE,QAAQ;QAC1F,MAAM0E,QAAQ,GAAGjL,KAAK,CAACxB,SAAS,CAAC+H,QAAQ,CAAC,GAAGvG,KAAK,CAACxB,SAAS,CAACuM,GAAG,CAAC,GAAGD,cAAc,CAACvE,QAAQ;QAC3F,IAAI+D,aAAa,GAAGU,QAAQ,EAAE;UAC5BV,aAAa,GAAGU,QAAQ;QAC1B,CAAC,MAAM,IAAIV,aAAa,GAAGW,QAAQ,EAAE;UACnCX,aAAa,GAAGW,QAAQ;QAC1B;MACF;MACA,IAAIxE,cAAc,EAAE;QAClB,IAAIyD,qBAAqB,EAAEgB,sBAAsB;QACjD,MAAMH,GAAG,GAAGxE,QAAQ,KAAK,GAAG,GAAG,OAAO,GAAG,QAAQ;QACjD,MAAM4E,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAACpB,QAAQ,CAAC3M,OAAO,CAACkB,SAAS,CAAC,CAAC;QACjE,MAAM0M,QAAQ,GAAGhL,KAAK,CAACxB,SAAS,CAACkG,SAAS,CAAC,GAAG1E,KAAK,CAACvB,QAAQ,CAACsM,GAAG,CAAC,IAAII,YAAY,GAAG,CAAC,CAACjB,qBAAqB,GAAG/J,cAAc,CAAC0D,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqG,qBAAqB,CAACxF,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAIyG,YAAY,GAAG,CAAC,GAAGL,cAAc,CAACpG,SAAS,CAAC;QACnP,MAAMuG,QAAQ,GAAGjL,KAAK,CAACxB,SAAS,CAACkG,SAAS,CAAC,GAAG1E,KAAK,CAACxB,SAAS,CAACuM,GAAG,CAAC,IAAII,YAAY,GAAG,CAAC,GAAG,CAAC,CAACD,sBAAsB,GAAG/K,cAAc,CAAC0D,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqH,sBAAsB,CAACxG,SAAS,CAAC,KAAK,CAAC,CAAC,IAAIyG,YAAY,GAAGL,cAAc,CAACpG,SAAS,GAAG,CAAC,CAAC;QACtP,IAAI6F,cAAc,GAAGS,QAAQ,EAAE;UAC7BT,cAAc,GAAGS,QAAQ;QAC3B,CAAC,MAAM,IAAIT,cAAc,GAAGU,QAAQ,EAAE;UACpCV,cAAc,GAAGU,QAAQ;QAC3B;MACF;MACA,OAAO;QACL,CAAC1E,QAAQ,GAAG+D,aAAa;QACzB,CAAC5F,SAAS,GAAG6F;MACf,CAAC;IACH;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,IAAI,GAAG,SAAAA,CAAUnK,OAAO,EAAE;EAC9B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,OAAO;IACLV,IAAI,EAAE,MAAM;IACZU,OAAO;IACP,MAAMT,EAAEA,CAACQ,KAAK,EAAE;MACd,IAAIqK,qBAAqB,EAAEC,sBAAsB;MACjD,MAAM;QACJhN,SAAS;QACT0B,KAAK;QACLL,QAAQ;QACRmB;MACF,CAAC,GAAGE,KAAK;MACT,MAAM;QACJuK,KAAK,GAAGA,CAAA,KAAM,CAAC,CAAC;QAChB,GAAG5G;MACL,CAAC,GAAGrH,QAAQ,CAAC2D,OAAO,EAAED,KAAK,CAAC;MAC5B,MAAM8D,QAAQ,GAAG,MAAM/D,cAAc,CAACC,KAAK,EAAE2D,qBAAqB,CAAC;MACnE,MAAM9F,IAAI,GAAGzB,OAAO,CAACkB,SAAS,CAAC;MAC/B,MAAM4F,SAAS,GAAG7G,YAAY,CAACiB,SAAS,CAAC;MACzC,MAAMyE,OAAO,GAAG9F,WAAW,CAACqB,SAAS,CAAC,KAAK,GAAG;MAC9C,MAAM;QACJW,KAAK;QACLG;MACF,CAAC,GAAGY,KAAK,CAACvB,QAAQ;MAClB,IAAI+M,UAAU;MACd,IAAIC,SAAS;MACb,IAAI5M,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,QAAQ,EAAE;QACvC2M,UAAU,GAAG3M,IAAI;QACjB4M,SAAS,GAAGvH,SAAS,MAAM,CAAC,OAAOvE,QAAQ,CAACI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAACe,QAAQ,CAACrC,QAAQ,CAAC,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;MAChJ,CAAC,MAAM;QACLgN,SAAS,GAAG5M,IAAI;QAChB2M,UAAU,GAAGtH,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ;MACrD;MACA,MAAMwH,qBAAqB,GAAGtM,MAAM,GAAG0F,QAAQ,CAACvC,GAAG,GAAGuC,QAAQ,CAACtC,MAAM;MACrE,MAAMmJ,oBAAoB,GAAG1M,KAAK,GAAG6F,QAAQ,CAACrC,IAAI,GAAGqC,QAAQ,CAACpC,KAAK;MACnE,MAAMkJ,uBAAuB,GAAGnO,GAAG,CAAC2B,MAAM,GAAG0F,QAAQ,CAAC0G,UAAU,CAAC,EAAEE,qBAAqB,CAAC;MACzF,MAAMG,sBAAsB,GAAGpO,GAAG,CAACwB,KAAK,GAAG6F,QAAQ,CAAC2G,SAAS,CAAC,EAAEE,oBAAoB,CAAC;MACrF,MAAMG,OAAO,GAAG,CAAC9K,KAAK,CAACb,cAAc,CAACiK,KAAK;MAC3C,IAAI2B,eAAe,GAAGH,uBAAuB;MAC7C,IAAII,cAAc,GAAGH,sBAAsB;MAC3C,IAAI,CAACR,qBAAqB,GAAGrK,KAAK,CAACb,cAAc,CAACiK,KAAK,KAAK,IAAI,IAAIiB,qBAAqB,CAACV,OAAO,CAAC3L,CAAC,EAAE;QACnGgN,cAAc,GAAGL,oBAAoB;MACvC;MACA,IAAI,CAACL,sBAAsB,GAAGtK,KAAK,CAACb,cAAc,CAACiK,KAAK,KAAK,IAAI,IAAIkB,sBAAsB,CAACX,OAAO,CAACxL,CAAC,EAAE;QACrG4M,eAAe,GAAGL,qBAAqB;MACzC;MACA,IAAII,OAAO,IAAI,CAAC5H,SAAS,EAAE;QACzB,MAAM+H,IAAI,GAAG/N,GAAG,CAAC4G,QAAQ,CAACrC,IAAI,EAAE,CAAC,CAAC;QAClC,MAAMyJ,IAAI,GAAGhO,GAAG,CAAC4G,QAAQ,CAACpC,KAAK,EAAE,CAAC,CAAC;QACnC,MAAMyJ,IAAI,GAAGjO,GAAG,CAAC4G,QAAQ,CAACvC,GAAG,EAAE,CAAC,CAAC;QACjC,MAAM6J,IAAI,GAAGlO,GAAG,CAAC4G,QAAQ,CAACtC,MAAM,EAAE,CAAC,CAAC;QACpC,IAAIO,OAAO,EAAE;UACXiJ,cAAc,GAAG/M,KAAK,GAAG,CAAC,IAAIgN,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,GAAGD,IAAI,GAAGC,IAAI,GAAGhO,GAAG,CAAC4G,QAAQ,CAACrC,IAAI,EAAEqC,QAAQ,CAACpC,KAAK,CAAC,CAAC;QAC5G,CAAC,MAAM;UACLqJ,eAAe,GAAG3M,MAAM,GAAG,CAAC,IAAI+M,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,GAAGD,IAAI,GAAGC,IAAI,GAAGlO,GAAG,CAAC4G,QAAQ,CAACvC,GAAG,EAAEuC,QAAQ,CAACtC,MAAM,CAAC,CAAC;QAC9G;MACF;MACA,MAAM+I,KAAK,CAAC;QACV,GAAGvK,KAAK;QACRgL,cAAc;QACdD;MACF,CAAC,CAAC;MACF,MAAMM,cAAc,GAAG,MAAM1M,QAAQ,CAACmD,aAAa,CAAChC,QAAQ,CAACrC,QAAQ,CAAC;MACtE,IAAIQ,KAAK,KAAKoN,cAAc,CAACpN,KAAK,IAAIG,MAAM,KAAKiN,cAAc,CAACjN,MAAM,EAAE;QACtE,OAAO;UACLwB,KAAK,EAAE;YACLZ,KAAK,EAAE;UACT;QACF,CAAC;MACH;MACA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;AACH,CAAC;AAED,SAAS2C,KAAK,EAAE2B,aAAa,EAAE/E,eAAe,EAAEwB,cAAc,EAAEqF,IAAI,EAAEyB,IAAI,EAAEe,MAAM,EAAEgC,UAAU,EAAE/G,MAAM,EAAEuG,KAAK,EAAEgB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}