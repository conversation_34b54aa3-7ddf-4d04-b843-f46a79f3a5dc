{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport apply from './_apply.js';\nimport baseRest from './_baseRest.js';\nimport customDefaultsMerge from './_customDefaultsMerge.js';\nimport mergeWith from './mergeWith.js';\n\n/**\n * This method is like `_.defaults` except that it recursively assigns\n * default properties.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaults\n * @example\n *\n * _.defaultsDeep({ 'a': { 'b': 2 } }, { 'a': { 'b': 1, 'c': 3 } });\n * // => { 'a': { 'b': 2, 'c': 3 } }\n */\nvar defaultsDeep = baseRest(function (args) {\n  args.push(undefined, customDefaultsMerge);\n  return apply(mergeWith, undefined, args);\n});\nexport default defaultsDeep;", "map": {"version": 3, "names": ["apply", "baseRest", "customDefaultsMerge", "mergeWith", "defaultsDeep", "args", "push", "undefined"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/defaultsDeep.js"], "sourcesContent": ["import apply from './_apply.js';\nimport baseRest from './_baseRest.js';\nimport customDefaultsMerge from './_customDefaultsMerge.js';\nimport mergeWith from './mergeWith.js';\n\n/**\n * This method is like `_.defaults` except that it recursively assigns\n * default properties.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 3.10.0\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaults\n * @example\n *\n * _.defaultsDeep({ 'a': { 'b': 2 } }, { 'a': { 'b': 1, 'c': 3 } });\n * // => { 'a': { 'b': 2, 'c': 3 } }\n */\nvar defaultsDeep = baseRest(function(args) {\n  args.push(undefined, customDefaultsMerge);\n  return apply(mergeWith, undefined, args);\n});\n\nexport default defaultsDeep;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,mBAAmB,MAAM,2BAA2B;AAC3D,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAGH,QAAQ,CAAC,UAASI,IAAI,EAAE;EACzCA,IAAI,CAACC,IAAI,CAACC,SAAS,EAAEL,mBAAmB,CAAC;EACzC,OAAOF,KAAK,CAACG,SAAS,EAAEI,SAAS,EAAEF,IAAI,CAAC;AAC1C,CAAC,CAAC;AAEF,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}