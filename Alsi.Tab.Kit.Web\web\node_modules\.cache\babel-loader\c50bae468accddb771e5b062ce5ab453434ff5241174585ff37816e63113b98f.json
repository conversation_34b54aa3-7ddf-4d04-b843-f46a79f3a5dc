{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { getFixedColumnsClass, getFixedColumnOffset, ensurePosition } from '../util.mjs';\nimport useMapState from './mapState-helper.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nfunction useStyle(props) {\n  const {\n    columns\n  } = useMapState();\n  const ns = useNamespace(\"table\");\n  const getCellClasses = (columns2, cellIndex) => {\n    const column = columns2[cellIndex];\n    const classes = [ns.e(\"cell\"), column.id, column.align, column.labelClassName, ...getFixedColumnsClass(ns.b(), cellIndex, column.fixed, props.store)];\n    if (column.className) {\n      classes.push(column.className);\n    }\n    if (!column.children) {\n      classes.push(ns.is(\"leaf\"));\n    }\n    return classes;\n  };\n  const getCellStyles = (column, cellIndex) => {\n    const fixedStyle = getFixedColumnOffset(cellIndex, column.fixed, props.store);\n    ensurePosition(fixedStyle, \"left\");\n    ensurePosition(fixedStyle, \"right\");\n    return fixedStyle;\n  };\n  return {\n    getCellClasses,\n    getCellStyles,\n    columns\n  };\n}\nexport { useStyle as default };", "map": {"version": 3, "names": ["useStyle", "props", "columns", "useMapState", "ns", "useNamespace", "getCellClasses", "columns2", "cellIndex", "column", "classes", "e", "id", "align", "labelClassName", "getFixedColumnsClass", "b", "fixed", "store", "className", "push", "children", "is", "getCellStyles", "fixedStyle", "getFixedColumnOffset", "ensurePosition"], "sources": ["../../../../../../../packages/components/table/src/table-footer/style-helper.ts"], "sourcesContent": ["import { useNamespace } from '@element-plus/hooks'\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport useMapState from './mapState-helper'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableFooter } from '.'\n\nfunction useStyle<T>(props: TableFooter<T>) {\n  const { columns } = useMapState()\n  const ns = useNamespace('table')\n\n  const getCellClasses = (columns: TableColumnCtx<T>[], cellIndex: number) => {\n    const column = columns[cellIndex]\n    const classes = [\n      ns.e('cell'),\n      column.id,\n      column.align,\n      column.labelClassName,\n      ...getFixedColumnsClass(ns.b(), cellIndex, column.fixed, props.store),\n    ]\n    if (column.className) {\n      classes.push(column.className)\n    }\n    if (!column.children) {\n      classes.push(ns.is('leaf'))\n    }\n    return classes\n  }\n\n  const getCellStyles = (column: TableColumnCtx<T>, cellIndex: number) => {\n    const fixedStyle = getFixedColumnOffset(\n      cellIndex,\n      column.fixed,\n      props.store\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return fixedStyle\n  }\n\n  return {\n    getCellClasses,\n    getCellStyles,\n    columns,\n  }\n}\n\nexport default useStyle\n"], "mappings": ";;;;AAOA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAM;IAAEC;EAAO,CAAE,GAAGC,WAAW,EAAE;EACjC,MAAMC,EAAE,GAAGC,YAAY,CAAC,OAAO,CAAC;EAChC,MAAMC,cAAc,GAAGA,CAACC,QAAQ,EAAEC,SAAS,KAAK;IAC9C,MAAMC,MAAM,GAAGF,QAAQ,CAACC,SAAS,CAAC;IAClC,MAAME,OAAO,GAAG,CACdN,EAAE,CAACO,CAAC,CAAC,MAAM,CAAC,EACZF,MAAM,CAACG,EAAE,EACTH,MAAM,CAACI,KAAK,EACZJ,MAAM,CAACK,cAAc,EACrB,GAAGC,oBAAoB,CAACX,EAAE,CAACY,CAAC,EAAE,EAAER,SAAS,EAAEC,MAAM,CAACQ,KAAK,EAAEhB,KAAK,CAACiB,KAAK,CAAC,CACtE;IACD,IAAIT,MAAM,CAACU,SAAS,EAAE;MACpBT,OAAO,CAACU,IAAI,CAACX,MAAM,CAACU,SAAS,CAAC;IACpC;IACI,IAAI,CAACV,MAAM,CAACY,QAAQ,EAAE;MACpBX,OAAO,CAACU,IAAI,CAAChB,EAAE,CAACkB,EAAE,CAAC,MAAM,CAAC,CAAC;IACjC;IACI,OAAOZ,OAAO;EAClB,CAAG;EACD,MAAMa,aAAa,GAAGA,CAACd,MAAM,EAAED,SAAS,KAAK;IAC3C,MAAMgB,UAAU,GAAGC,oBAAoB,CAACjB,SAAS,EAAEC,MAAM,CAACQ,KAAK,EAAEhB,KAAK,CAACiB,KAAK,CAAC;IAC7EQ,cAAc,CAACF,UAAU,EAAE,MAAM,CAAC;IAClCE,cAAc,CAACF,UAAU,EAAE,OAAO,CAAC;IACnC,OAAOA,UAAU;EACrB,CAAG;EACD,OAAO;IACLlB,cAAc;IACdiB,aAAa;IACbrB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}