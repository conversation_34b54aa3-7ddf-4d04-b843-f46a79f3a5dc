{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsEndIndex from './_charsEndIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\nimport trimmedEndIndex from './_trimmedEndIndex.js';\n\n/**\n * Removes trailing whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimEnd('  abc  ');\n * // => '  abc'\n *\n * _.trimEnd('-_-abc-_-', '_-');\n * // => '-_-abc'\n */\nfunction trimEnd(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.slice(0, trimmedEndIndex(string) + 1);\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n    end = charsEndIndex(strSymbols, stringToArray(chars)) + 1;\n  return castSlice(strSymbols, 0, end).join('');\n}\nexport default trimEnd;", "map": {"version": 3, "names": ["baseToString", "castSlice", "charsEndIndex", "stringToArray", "toString", "trimmedEndIndex", "trimEnd", "string", "chars", "guard", "undefined", "slice", "strSymbols", "end", "join"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/trimEnd.js"], "sourcesContent": ["import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsEndIndex from './_charsEndIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\nimport trimmedEndIndex from './_trimmedEndIndex.js';\n\n/**\n * Removes trailing whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimEnd('  abc  ');\n * // => '  abc'\n *\n * _.trimEnd('-_-abc-_-', '_-');\n * // => '-_-abc'\n */\nfunction trimEnd(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.slice(0, trimmedEndIndex(string) + 1);\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n      end = charsEndIndex(strSymbols, stringToArray(chars)) + 1;\n\n  return castSlice(strSymbols, 0, end).join('');\n}\n\nexport default trimEnd;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,eAAe,MAAM,uBAAuB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACrCF,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;EACzB,IAAIA,MAAM,KAAKE,KAAK,IAAID,KAAK,KAAKE,SAAS,CAAC,EAAE;IAC5C,OAAOH,MAAM,CAACI,KAAK,CAAC,CAAC,EAAEN,eAAe,CAACE,MAAM,CAAC,GAAG,CAAC,CAAC;EACrD;EACA,IAAI,CAACA,MAAM,IAAI,EAAEC,KAAK,GAAGR,YAAY,CAACQ,KAAK,CAAC,CAAC,EAAE;IAC7C,OAAOD,MAAM;EACf;EACA,IAAIK,UAAU,GAAGT,aAAa,CAACI,MAAM,CAAC;IAClCM,GAAG,GAAGX,aAAa,CAACU,UAAU,EAAET,aAAa,CAACK,KAAK,CAAC,CAAC,GAAG,CAAC;EAE7D,OAAOP,SAAS,CAACW,UAAU,EAAE,CAAC,EAAEC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAC/C;AAEA,eAAeR,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}