{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Creates an array that is the composition of partially applied arguments,\n * placeholders, and provided arguments into a single array of arguments.\n *\n * @private\n * @param {Array} args The provided arguments.\n * @param {Array} partials The arguments to prepend to those provided.\n * @param {Array} holders The `partials` placeholder indexes.\n * @params {boolean} [isCurried] Specify composing for a curried function.\n * @returns {Array} Returns the new array of composed arguments.\n */\nfunction composeArgs(args, partials, holders, isCurried) {\n  var argsIndex = -1,\n    argsLength = args.length,\n    holdersLength = holders.length,\n    leftIndex = -1,\n    leftLength = partials.length,\n    rangeLength = nativeMax(argsLength - holdersLength, 0),\n    result = Array(leftLength + rangeLength),\n    isUncurried = !isCurried;\n  while (++leftIndex < leftLength) {\n    result[leftIndex] = partials[leftIndex];\n  }\n  while (++argsIndex < holdersLength) {\n    if (isUncurried || argsIndex < argsLength) {\n      result[holders[argsIndex]] = args[argsIndex];\n    }\n  }\n  while (rangeLength--) {\n    result[leftIndex++] = args[argsIndex++];\n  }\n  return result;\n}\nexport default composeArgs;", "map": {"version": 3, "names": ["nativeMax", "Math", "max", "compose<PERSON><PERSON>s", "args", "partials", "holders", "is<PERSON><PERSON><PERSON>", "argsIndex", "arg<PERSON><PERSON><PERSON><PERSON>", "length", "holders<PERSON><PERSON><PERSON>", "leftIndex", "left<PERSON><PERSON><PERSON>", "rangeLength", "result", "Array", "isUncurried"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_composeArgs.js"], "sourcesContent": ["/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Creates an array that is the composition of partially applied arguments,\n * placeholders, and provided arguments into a single array of arguments.\n *\n * @private\n * @param {Array} args The provided arguments.\n * @param {Array} partials The arguments to prepend to those provided.\n * @param {Array} holders The `partials` placeholder indexes.\n * @params {boolean} [isCurried] Specify composing for a curried function.\n * @returns {Array} Returns the new array of composed arguments.\n */\nfunction composeArgs(args, partials, holders, isCurried) {\n  var argsIndex = -1,\n      argsLength = args.length,\n      holdersLength = holders.length,\n      leftIndex = -1,\n      leftLength = partials.length,\n      rangeLength = nativeMax(argsLength - holdersLength, 0),\n      result = Array(leftLength + rangeLength),\n      isUncurried = !isCurried;\n\n  while (++leftIndex < leftLength) {\n    result[leftIndex] = partials[leftIndex];\n  }\n  while (++argsIndex < holdersLength) {\n    if (isUncurried || argsIndex < argsLength) {\n      result[holders[argsIndex]] = args[argsIndex];\n    }\n  }\n  while (rangeLength--) {\n    result[leftIndex++] = args[argsIndex++];\n  }\n  return result;\n}\n\nexport default composeArgs;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;EACvD,IAAIC,SAAS,GAAG,CAAC,CAAC;IACdC,UAAU,GAAGL,IAAI,CAACM,MAAM;IACxBC,aAAa,GAAGL,OAAO,CAACI,MAAM;IAC9BE,SAAS,GAAG,CAAC,CAAC;IACdC,UAAU,GAAGR,QAAQ,CAACK,MAAM;IAC5BI,WAAW,GAAGd,SAAS,CAACS,UAAU,GAAGE,aAAa,EAAE,CAAC,CAAC;IACtDI,MAAM,GAAGC,KAAK,CAACH,UAAU,GAAGC,WAAW,CAAC;IACxCG,WAAW,GAAG,CAACV,SAAS;EAE5B,OAAO,EAAEK,SAAS,GAAGC,UAAU,EAAE;IAC/BE,MAAM,CAACH,SAAS,CAAC,GAAGP,QAAQ,CAACO,SAAS,CAAC;EACzC;EACA,OAAO,EAAEJ,SAAS,GAAGG,aAAa,EAAE;IAClC,IAAIM,WAAW,IAAIT,SAAS,GAAGC,UAAU,EAAE;MACzCM,MAAM,CAACT,OAAO,CAACE,SAAS,CAAC,CAAC,GAAGJ,IAAI,CAACI,SAAS,CAAC;IAC9C;EACF;EACA,OAAOM,WAAW,EAAE,EAAE;IACpBC,MAAM,CAACH,SAAS,EAAE,CAAC,GAAGR,IAAI,CAACI,SAAS,EAAE,CAAC;EACzC;EACA,OAAOO,MAAM;AACf;AAEA,eAAeZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}