{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nimport { isString } from '@vue/shared';\nconst mentionDropdownProps = buildProps({\n  options: {\n    type: definePropType(Array),\n    default: () => []\n  },\n  loading: Boolean,\n  disabled: Boolean,\n  contentId: String,\n  ariaLabel: String\n});\nconst mentionDropdownEmits = {\n  select: option => isString(option.value)\n};\nexport { mentionDropdownEmits, mentionDropdownProps };", "map": {"version": 3, "names": ["mentionDropdownProps", "buildProps", "options", "type", "definePropType", "Array", "default", "loading", "Boolean", "disabled", "contentId", "String", "aria<PERSON><PERSON><PERSON>", "mentionDropdownEmits", "select", "option", "isString", "value"], "sources": ["../../../../../../packages/components/mention/src/mention-dropdown.ts"], "sourcesContent": ["import { buildProps, definePropType, isString } from '@element-plus/utils'\n\nimport type { MentionOption } from './types'\n\nexport const mentionDropdownProps = buildProps({\n  options: {\n    type: definePropType<MentionOption[]>(Array),\n    default: () => [],\n  },\n  loading: Boolean,\n  disabled: Boolean,\n  contentId: String,\n  ariaLabel: String,\n})\n\nexport const mentionDropdownEmits = {\n  select: (option: MentionOption) => isString(option.value),\n}\n"], "mappings": ";;AACY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7CC,OAAO,EAAE;IACPC,IAAI,EAAEC,cAAc,CAACC,KAAK,CAAC;IAC3BC,OAAO,EAAEA,CAAA,KAAM;EACnB,CAAG;EACDC,OAAO,EAAEC,OAAO;EAChBC,QAAQ,EAAED,OAAO;EACjBE,SAAS,EAAEC,MAAM;EACjBC,SAAS,EAAED;AACb,CAAC;AACW,MAACE,oBAAoB,GAAG;EAClCC,MAAM,EAAGC,MAAM,IAAKC,QAAQ,CAACD,MAAM,CAACE,KAAK;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}