{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport { defineComponent, h, Comment } from 'vue';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nfunction isVNodeEmpty(vnodes) {\n  return !!(vnodes == null ? void 0 : vnodes.every(vnode => vnode.type === Comment));\n}\nvar NodeContent = defineComponent({\n  name: \"NodeContent\",\n  setup() {\n    const ns = useNamespace(\"cascader-node\");\n    return {\n      ns\n    };\n  },\n  render() {\n    const {\n      ns\n    } = this;\n    const {\n      node,\n      panel\n    } = this.$parent;\n    const {\n      data,\n      label: nodeLabel\n    } = node;\n    const {\n      renderLabelFn\n    } = panel;\n    const label = () => {\n      let renderLabel = renderLabelFn == null ? void 0 : renderLabelFn({\n        node,\n        data\n      });\n      if (isVNodeEmpty(renderLabel)) {\n        renderLabel = nodeLabel;\n      }\n      return renderLabel != null ? renderLabel : nodeLabel;\n    };\n    return h(\"span\", {\n      class: ns.e(\"label\")\n    }, label());\n  }\n});\nexport { NodeContent as default };", "map": {"version": 3, "names": ["isVNodeEmpty", "vnodes", "every", "vnode", "type", "Comment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineComponent", "name", "setup", "ns", "useNamespace", "render", "node", "panel", "$parent", "data", "label", "nodeLabel", "renderLabelFn", "renderLabel", "h", "class", "e"], "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.ts"], "sourcesContent": ["// @ts-nocheck\nimport { Comment, defineComponent, h } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { VNode } from 'vue'\n\nfunction isVNodeEmpty(vnodes?: VNode[]) {\n  return !!vnodes?.every((vnode) => vnode.type === Comment)\n}\n\nexport default defineComponent({\n  name: 'NodeContent',\n  setup() {\n    const ns = useNamespace('cascader-node')\n    return {\n      ns,\n    }\n  },\n  render() {\n    const { ns } = this\n    const { node, panel } = this.$parent\n    const { data, label: nodeLabel } = node\n    const { renderLabelFn } = panel\n    const label = () => {\n      let renderLabel = renderLabelFn?.({ node, data })\n      if (isVNodeEmpty(renderLabel)) {\n        renderLabel = nodeLabel\n      }\n      return renderLabel ?? nodeLabel\n    }\n    return h('span', { class: ns.e('label') }, label())\n  },\n})\n"], "mappings": ";;;;AAEA,SAASA,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAO,CAAC,EAAEA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,KAAK,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,KAAKC,OAAO,CAAC,CAAC;AACtF;AACA,IAAAC,WAAA,GAAeC,eAAe,CAAC;EAC7BC,IAAI,EAAE,aAAa;EACnBC,KAAKA,CAAA,EAAG;IACN,MAAMC,EAAE,GAAGC,YAAY,CAAC,eAAe,CAAC;IACxC,OAAO;MACLD;IACN,CAAK;EACL,CAAG;EACDE,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEF;IAAE,CAAE,GAAG,IAAI;IACnB,MAAM;MAAEG,IAAI;MAAEC;IAAK,CAAE,GAAG,IAAI,CAACC,OAAO;IACpC,MAAM;MAAEC,IAAI;MAAEC,KAAK,EAAEC;IAAS,CAAE,GAAGL,IAAI;IACvC,MAAM;MAAEM;IAAa,CAAE,GAAGL,KAAK;IAC/B,MAAMG,KAAK,GAAGA,CAAA,KAAM;MAClB,IAAIG,WAAW,GAAGD,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC;QAAEN,IAAI;QAAEG;MAAI,CAAE,CAAC;MAChF,IAAIhB,YAAY,CAACoB,WAAW,CAAC,EAAE;QAC7BA,WAAW,GAAGF,SAAS;MAC/B;MACM,OAAOE,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAGF,SAAS;IAC1D,CAAK;IACD,OAAOG,CAAC,CAAC,MAAM,EAAE;MAAEC,KAAK,EAAEZ,EAAE,CAACa,CAAC,CAAC,OAAO;IAAC,CAAE,EAAEN,KAAK,EAAE,CAAC;EACvD;AACA,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}