{"ast": null, "code": "import { defineComponent, inject, h, renderSlot } from 'vue';\nimport { ElText } from '../../text/index.mjs';\nimport { NODE_INSTANCE_INJECTION_KEY, ROOT_TREE_INJECTION_KEY } from './tokens.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst _sfc_main = defineComponent({\n  name: \"ElTreeNodeContent\",\n  props: {\n    node: {\n      type: Object,\n      required: true\n    },\n    renderContent: Function\n  },\n  setup(props) {\n    const ns = useNamespace(\"tree\");\n    const nodeInstance = inject(NODE_INSTANCE_INJECTION_KEY);\n    const tree = inject(ROOT_TREE_INJECTION_KEY);\n    return () => {\n      const node = props.node;\n      const {\n        data,\n        store\n      } = node;\n      return props.renderContent ? props.renderContent(h, {\n        _self: nodeInstance,\n        node,\n        data,\n        store\n      }) : renderSlot(tree.ctx.slots, \"default\", {\n        node,\n        data\n      }, () => [h(ElText, {\n        tag: \"span\",\n        truncated: true,\n        class: ns.be(\"node\", \"label\")\n      }, () => [node.label])]);\n    };\n  }\n});\nvar NodeContent = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"tree-node-content.vue\"]]);\nexport { NodeContent as default };", "map": {"version": 3, "names": ["_sfc_main", "defineComponent", "name", "props", "node", "type", "Object", "required", "renderContent", "Function", "setup", "ns", "useNamespace", "nodeInstance", "inject", "NODE_INSTANCE_INJECTION_KEY", "tree", "ROOT_TREE_INJECTION_KEY", "data", "store", "h", "_self", "renderSlot", "ctx", "slots", "ElText", "tag", "truncated", "class", "be", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_export_sfc"], "sources": ["../../../../../../packages/components/tree/src/tree-node-content.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport { defineComponent, h, inject, renderSlot } from 'vue'\nimport ElText from '@element-plus/components/text'\n\nimport { useNamespace } from '@element-plus/hooks'\nimport { NODE_INSTANCE_INJECTION_KEY, ROOT_TREE_INJECTION_KEY } from './tokens'\nimport type { ComponentInternalInstance } from 'vue'\nimport type { RootTreeType } from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTreeNodeContent',\n  props: {\n    node: {\n      type: Object,\n      required: true,\n    },\n    renderContent: Function,\n  },\n  setup(props) {\n    const ns = useNamespace('tree')\n    const nodeInstance = inject<ComponentInternalInstance>(\n      NODE_INSTANCE_INJECTION_KEY\n    )\n    const tree = inject<RootTreeType>(ROOT_TREE_INJECTION_KEY)!\n    return () => {\n      const node = props.node\n      const { data, store } = node\n      return props.renderContent\n        ? props.renderContent(h, { _self: nodeInstance, node, data, store })\n        : renderSlot(tree.ctx.slots, 'default', { node, data }, () => [\n            h(\n              ElText,\n              { tag: 'span', truncated: true, class: ns.be('node', 'label') },\n              () => [node.label]\n            ),\n          ])\n    }\n  },\n})\n</script>\n"], "mappings": ";;;;;AASA,MAAKA,SAAA,GAAaC,eAAa;EAC7BC,IAAM;EACNC,KAAO;IACLC,IAAM;MACJC,IAAM,EAAAC,MAAA;MACNC,QAAU;IAAA,CACZ;IACAC,aAAe,EAAAC;EAAA,CACjB;EACAC,MAAMP,KAAO;IACL,MAAAQ,EAAA,GAAKC,YAAA,CAAa,MAAM;IAC9B,MAAMC,YAAe,GAAAC,MAAA,CAAAC,2BAAA;IACnB,MAAAC,IAAA,GAAAF,MAAA,CAAAG,uBAAA;IACF;MACM,MAAAb,IAAA,GAAAD,KAA4B,CAAuBC,IAAA;MACzD,MAAa;QAAAc,IAAA;QAAAC;MAAA,IAAAf,IAAA;MACX,OAAAD,KAAA,CAAaK,aAAM,GAAAL,KAAA,CAAAK,aAAA,CAAAY,CAAA;QAAAC,KAAA,EAAAR,YAAA;QAAAT,IAAA;QAAAc,IAAA;QAAAC;MAAA,KAAAG,UAAA,CAAAN,IAAA,CAAAO,GAAA,CAAAC,KAAA;QAAApB,IAAA;QAAAc;MAAA,UACbE,CAAA,CAAAK,MAAQ;QAAAC,GAAA,QAAU;QAAAC,SAAA;QAAAC,KAAA,EAAAjB,EAAA,CAAAkB,EAAA;MAAA,UAAAzB,IAAA,CAAA0B,KAAA,GACjB;IAEyD,CAC1D;EAAA;AACE,CACA;AACiB,IAAAC,WAAA,GACnB,eAAAC,WAAA,CAAAhC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}