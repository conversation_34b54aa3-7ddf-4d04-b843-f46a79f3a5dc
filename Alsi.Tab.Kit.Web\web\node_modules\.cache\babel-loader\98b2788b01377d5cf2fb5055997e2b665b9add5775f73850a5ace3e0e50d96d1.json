{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseWhile from './_baseWhile.js';\n\n/**\n * Creates a slice of `array` excluding elements dropped from the end.\n * Elements are dropped until `predicate` returns falsey. The predicate is\n * invoked with three arguments: (value, index, array).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': true },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': false }\n * ];\n *\n * _.dropRightWhile(users, function(o) { return !o.active; });\n * // => objects for ['barney']\n *\n * // The `_.matches` iteratee shorthand.\n * _.dropRightWhile(users, { 'user': 'pebbles', 'active': false });\n * // => objects for ['barney', 'fred']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.dropRightWhile(users, ['active', false]);\n * // => objects for ['barney']\n *\n * // The `_.property` iteratee shorthand.\n * _.dropRightWhile(users, 'active');\n * // => objects for ['barney', 'fred', 'pebbles']\n */\nfunction dropRightWhile(array, predicate) {\n  return array && array.length ? baseWhile(array, baseIteratee(predicate, 3), true, true) : [];\n}\nexport default dropRightWhile;", "map": {"version": 3, "names": ["baseIteratee", "<PERSON><PERSON><PERSON><PERSON>", "dropRightWhile", "array", "predicate", "length"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/dropRightWhile.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseWhile from './_baseWhile.js';\n\n/**\n * Creates a slice of `array` excluding elements dropped from the end.\n * Elements are dropped until `predicate` returns falsey. The predicate is\n * invoked with three arguments: (value, index, array).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': true },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': false }\n * ];\n *\n * _.dropRightWhile(users, function(o) { return !o.active; });\n * // => objects for ['barney']\n *\n * // The `_.matches` iteratee shorthand.\n * _.dropRightWhile(users, { 'user': 'pebbles', 'active': false });\n * // => objects for ['barney', 'fred']\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.dropRightWhile(users, ['active', false]);\n * // => objects for ['barney']\n *\n * // The `_.property` iteratee shorthand.\n * _.dropRightWhile(users, 'active');\n * // => objects for ['barney', 'fred', 'pebbles']\n */\nfunction dropRightWhile(array, predicate) {\n  return (array && array.length)\n    ? baseWhile(array, baseIteratee(predicate, 3), true, true)\n    : [];\n}\n\nexport default dropRightWhile;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACxC,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBJ,SAAS,CAACE,KAAK,EAAEH,YAAY,CAACI,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,GACxD,EAAE;AACR;AAEA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}