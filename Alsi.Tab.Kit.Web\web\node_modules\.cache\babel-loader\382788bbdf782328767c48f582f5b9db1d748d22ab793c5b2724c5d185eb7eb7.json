{"ast": null, "code": "import baseOrderBy from './_baseOrderBy.js';\nimport isArray from './isArray.js';\n\n/**\n * This method is like `_.sortBy` except that it allows specifying the sort\n * orders of the iteratees to sort by. If `orders` is unspecified, all values\n * are sorted in ascending order. Otherwise, specify an order of \"desc\" for\n * descending or \"asc\" for ascending sort order of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Array[]|Function[]|Object[]|string[]} [iteratees=[_.identity]]\n *  The iteratees to sort by.\n * @param {string[]} [orders] The sort orders of `iteratees`.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.reduce`.\n * @returns {Array} Returns the new sorted array.\n * @example\n *\n * var users = [\n *   { 'user': 'fred',   'age': 48 },\n *   { 'user': 'barney', 'age': 34 },\n *   { 'user': 'fred',   'age': 40 },\n *   { 'user': 'barney', 'age': 36 }\n * ];\n *\n * // Sort by `user` in ascending order and by `age` in descending order.\n * _.orderBy(users, ['user', 'age'], ['asc', 'desc']);\n * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 40]]\n */\nfunction orderBy(collection, iteratees, orders, guard) {\n  if (collection == null) {\n    return [];\n  }\n  if (!isArray(iteratees)) {\n    iteratees = iteratees == null ? [] : [iteratees];\n  }\n  orders = guard ? undefined : orders;\n  if (!isArray(orders)) {\n    orders = orders == null ? [] : [orders];\n  }\n  return baseOrderBy(collection, iteratees, orders);\n}\nexport default orderBy;", "map": {"version": 3, "names": ["baseOrderBy", "isArray", "orderBy", "collection", "iteratees", "orders", "guard", "undefined"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/orderBy.js"], "sourcesContent": ["import baseOrderBy from './_baseOrderBy.js';\nimport isArray from './isArray.js';\n\n/**\n * This method is like `_.sortBy` except that it allows specifying the sort\n * orders of the iteratees to sort by. If `orders` is unspecified, all values\n * are sorted in ascending order. Otherwise, specify an order of \"desc\" for\n * descending or \"asc\" for ascending sort order of corresponding values.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Array[]|Function[]|Object[]|string[]} [iteratees=[_.identity]]\n *  The iteratees to sort by.\n * @param {string[]} [orders] The sort orders of `iteratees`.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.reduce`.\n * @returns {Array} Returns the new sorted array.\n * @example\n *\n * var users = [\n *   { 'user': 'fred',   'age': 48 },\n *   { 'user': 'barney', 'age': 34 },\n *   { 'user': 'fred',   'age': 40 },\n *   { 'user': 'barney', 'age': 36 }\n * ];\n *\n * // Sort by `user` in ascending order and by `age` in descending order.\n * _.orderBy(users, ['user', 'age'], ['asc', 'desc']);\n * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 40]]\n */\nfunction orderBy(collection, iteratees, orders, guard) {\n  if (collection == null) {\n    return [];\n  }\n  if (!isArray(iteratees)) {\n    iteratees = iteratees == null ? [] : [iteratees];\n  }\n  orders = guard ? undefined : orders;\n  if (!isArray(orders)) {\n    orders = orders == null ? [] : [orders];\n  }\n  return baseOrderBy(collection, iteratees, orders);\n}\n\nexport default orderBy;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACrD,IAAIH,UAAU,IAAI,IAAI,EAAE;IACtB,OAAO,EAAE;EACX;EACA,IAAI,CAACF,OAAO,CAACG,SAAS,CAAC,EAAE;IACvBA,SAAS,GAAGA,SAAS,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,SAAS,CAAC;EAClD;EACAC,MAAM,GAAGC,KAAK,GAAGC,SAAS,GAAGF,MAAM;EACnC,IAAI,CAACJ,OAAO,CAACI,MAAM,CAAC,EAAE;IACpBA,MAAM,GAAGA,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,MAAM,CAAC;EACzC;EACA,OAAOL,WAAW,CAACG,UAAU,EAAEC,SAAS,EAAEC,MAAM,CAAC;AACnD;AAEA,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}