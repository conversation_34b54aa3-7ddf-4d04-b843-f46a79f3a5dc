using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using System.IO;
using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services;
using Alsi.Tab.Kit.Web.Dto;

namespace Alsi.Tab.Kit.Web.Controllers
{
    public class DataLogConvertController : WebControllerBase
    {
        private readonly DataLogConvertService _dataLogConvertService;

        public DataLogConvertController()
        {
            _dataLogConvertService = new DataLogConvertService();
        }

        [HttpPost]
        [ActionName("analyze")]
        public IHttpActionResult Analyze([FromBody] FilePathDto dto)
        {
            try
            {
                if (dto == null || string.IsNullOrEmpty(dto.FilePath) || !File.Exists(dto.FilePath))
                {
                    return BadRequest("文件路径无效或文件不存在");
                }

                var fileInfo = _dataLogConvertService.AnalyzeFile(dto.FilePath);
                return Ok(fileInfo);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [ActionName("preview")]
        public IHttpActionResult Preview([FromBody] DataLogProcessRequestDto dto)
        {
            try
            {
                if (dto == null || string.IsNullOrEmpty(dto.SourceFilePath))
                {
                    return BadRequest("请求参数无效");
                }

                var request = _mapper.Map<DataLogProcessRequestDto, DataLogProcessRequest>(dto);
                var preview = _dataLogConvertService.PreviewProcess(request);
                return Ok(preview);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [ActionName("process")]
        public IHttpActionResult Process([FromBody] DataLogProcessRequestDto dto)
        {
            try
            {
                if (dto == null || string.IsNullOrEmpty(dto.SourceFilePath))
                {
                    return BadRequest("请求参数无效");
                }

                var request = _mapper.Map<DataLogProcessRequestDto, DataLogProcessRequest>(dto);
                var result = _dataLogConvertService.StartProcessAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpGet]
        [ActionName("progress")]
        public IHttpActionResult Progress(string taskId)
        {
            try
            {
                if (string.IsNullOrEmpty(taskId))
                {
                    return BadRequest("任务ID不能为空");
                }

                var progress = _dataLogConvertService.GetProgress(taskId);
                if (progress == null)
                {
                    // 添加调试信息
                    return BadRequest($"找不到任务ID为 {taskId} 的进度信息");
                }

                return Ok(progress);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [ActionName("cancel")]
        public IHttpActionResult Cancel([FromBody] TaskIdDto dto)
        {
            try
            {
                if (dto == null || string.IsNullOrEmpty(dto.TaskId))
                {
                    return BadRequest("任务ID不能为空");
                }

                var result = _dataLogConvertService.CancelProcess(dto.TaskId);
                return Ok(new { Success = result });
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [ActionName("open-folder")]
        public IHttpActionResult OpenFolder([FromBody] FilePathDto dto)
        {
            try
            {
                if (dto == null || string.IsNullOrEmpty(dto.FilePath))
                {
                    return BadRequest("文件路径不能为空");
                }

                _dataLogConvertService.OpenFileInExplorer(dto.FilePath);
                return Ok(new { Success = true });
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }
    }
}