{"ast": null, "code": "import { tooltipV2RootProps } from './root.mjs';\nimport { tooltipV2TriggerProps } from './trigger.mjs';\nimport { tooltipV2ArrowProps } from './arrow.mjs';\nimport { tooltipV2ContentProps } from './content2.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst tooltipV2Props = buildProps({\n  ...tooltipV2RootProps,\n  ...tooltipV2ArrowProps,\n  ...tooltipV2TriggerProps,\n  ...tooltipV2ContentProps,\n  alwaysOn: Boolean,\n  fullTransition: Boolean,\n  transitionProps: {\n    type: definePropType(Object),\n    default: null\n  },\n  teleported: Boolean,\n  to: {\n    type: definePropType([String, Object]),\n    default: \"body\"\n  }\n});\nexport { tooltipV2Props };", "map": {"version": 3, "names": ["tooltipV2Props", "buildProps", "tooltipV2RootProps", "tooltipV2ArrowProps", "tooltipV2TriggerProps", "tooltipV2ContentProps", "alwaysOn", "Boolean", "fullTransition", "transitionProps", "type", "definePropType", "Object", "default", "teleported", "to", "String"], "sources": ["../../../../../../packages/components/tooltip-v2/src/tooltip.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { tooltipV2RootProps } from './root'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2ArrowProps } from './arrow'\nimport { tooltipV2ContentProps } from './content'\n\nimport type { ExtractPropTypes, TransitionProps } from 'vue'\n\nexport const tooltipV2Props = buildProps({\n  ...tooltipV2RootProps,\n  ...tooltipV2ArrowProps,\n  ...tooltipV2TriggerProps,\n  ...tooltipV2ContentProps,\n  alwaysOn: Boolean,\n  fullTransition: Boolean,\n  transitionProps: {\n    type: definePropType<TransitionProps | null>(Object),\n    default: null,\n  },\n  teleported: Boolean,\n  to: {\n    type: definePropType<string | HTMLElement>([String, Object]),\n    default: 'body',\n  },\n} as const)\n\nexport type TooltipV2Props = ExtractPropTypes<typeof tooltipV2Props>\n"], "mappings": ";;;;;AAKY,MAACA,cAAc,GAAGC,UAAU,CAAC;EACvC,GAAGC,kBAAkB;EACrB,GAAGC,mBAAmB;EACtB,GAAGC,qBAAqB;EACxB,GAAGC,qBAAqB;EACxBC,QAAQ,EAAEC,OAAO;EACjBC,cAAc,EAAED,OAAO;EACvBE,eAAe,EAAE;IACfC,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,OAAO,EAAE;EACb,CAAG;EACDC,UAAU,EAAEP,OAAO;EACnBQ,EAAE,EAAE;IACFL,IAAI,EAAEC,cAAc,CAAC,CAACK,MAAM,EAAEJ,MAAM,CAAC,CAAC;IACtCC,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}