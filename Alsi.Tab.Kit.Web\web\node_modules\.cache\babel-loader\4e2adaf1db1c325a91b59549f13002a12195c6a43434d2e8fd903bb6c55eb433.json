{"ast": null, "code": "import baseIsMatch from './_baseIsMatch.js';\nimport getMatchData from './_getMatchData.js';\nimport matchesStrictComparable from './_matchesStrictComparable.js';\n\n/**\n * The base implementation of `_.matches` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property values to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseMatches(source) {\n  var matchData = getMatchData(source);\n  if (matchData.length == 1 && matchData[0][2]) {\n    return matchesStrictComparable(matchData[0][0], matchData[0][1]);\n  }\n  return function (object) {\n    return object === source || baseIsMatch(object, source, matchData);\n  };\n}\nexport default baseMatches;", "map": {"version": 3, "names": ["baseIsMatch", "getMatchData", "matchesStrictComparable", "baseMatches", "source", "matchData", "length", "object"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_baseMatches.js"], "sourcesContent": ["import baseIsMatch from './_baseIsMatch.js';\nimport getMatchData from './_getMatchData.js';\nimport matchesStrictComparable from './_matchesStrictComparable.js';\n\n/**\n * The base implementation of `_.matches` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property values to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseMatches(source) {\n  var matchData = getMatchData(source);\n  if (matchData.length == 1 && matchData[0][2]) {\n    return matchesStrictComparable(matchData[0][0], matchData[0][1]);\n  }\n  return function(object) {\n    return object === source || baseIsMatch(object, source, matchData);\n  };\n}\n\nexport default baseMatches;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,uBAAuB,MAAM,+BAA+B;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC3B,IAAIC,SAAS,GAAGJ,YAAY,CAACG,MAAM,CAAC;EACpC,IAAIC,SAAS,CAACC,MAAM,IAAI,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5C,OAAOH,uBAAuB,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE;EACA,OAAO,UAASE,MAAM,EAAE;IACtB,OAAOA,MAAM,KAAKH,MAAM,IAAIJ,WAAW,CAACO,MAAM,EAAEH,MAAM,EAAEC,SAAS,CAAC;EACpE,CAAC;AACH;AAEA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}