{"ast": null, "code": "/**\n * The base implementation of `_.propertyOf` without support for deep paths.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Function} Returns the new accessor function.\n */\nfunction basePropertyOf(object) {\n  return function (key) {\n    return object == null ? undefined : object[key];\n  };\n}\nexport default basePropertyOf;", "map": {"version": 3, "names": ["basePropertyOf", "object", "key", "undefined"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_basePropertyOf.js"], "sourcesContent": ["/**\n * The base implementation of `_.propertyOf` without support for deep paths.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Function} Returns the new accessor function.\n */\nfunction basePropertyOf(object) {\n  return function(key) {\n    return object == null ? undefined : object[key];\n  };\n}\n\nexport default basePropertyOf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAcA,CAACC,MAAM,EAAE;EAC9B,OAAO,UAASC,GAAG,EAAE;IACnB,OAAOD,MAAM,IAAI,IAAI,GAAGE,SAAS,GAAGF,MAAM,CAACC,GAAG,CAAC;EACjD,CAAC;AACH;AAEA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}