{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nimport { TinyColor } from './index.js';\nexport function random(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  // Check if we need to generate multiple colors\n  if (options.count !== undefined && options.count !== null) {\n    var totalColors = options.count;\n    var colors = [];\n    options.count = undefined;\n    while (totalColors > colors.length) {\n      // Since we're generating multiple colors,\n      // incremement the seed. Otherwise we'd just\n      // generate the same color each time...\n      options.count = null;\n      if (options.seed) {\n        options.seed += 1;\n      }\n      colors.push(random(options));\n    }\n    options.count = totalColors;\n    return colors;\n  }\n  // First we pick a hue (H)\n  var h = pickHue(options.hue, options.seed);\n  // Then use H to determine saturation (S)\n  var s = pickSaturation(h, options);\n  // Then use S and H to determine brightness (B).\n  var v = pickBrightness(h, s, options);\n  var res = {\n    h: h,\n    s: s,\n    v: v\n  };\n  if (options.alpha !== undefined) {\n    res.a = options.alpha;\n  }\n  // Then we return the HSB color in the desired format\n  return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n  var hueRange = getHueRange(hue);\n  var res = randomWithin(hueRange, seed);\n  // Instead of storing red as two seperate ranges,\n  // we group them, using negative numbers\n  if (res < 0) {\n    res = 360 + res;\n  }\n  return res;\n}\nfunction pickSaturation(hue, options) {\n  if (options.hue === 'monochrome') {\n    return 0;\n  }\n  if (options.luminosity === 'random') {\n    return randomWithin([0, 100], options.seed);\n  }\n  var saturationRange = getColorInfo(hue).saturationRange;\n  var sMin = saturationRange[0];\n  var sMax = saturationRange[1];\n  switch (options.luminosity) {\n    case 'bright':\n      sMin = 55;\n      break;\n    case 'dark':\n      sMin = sMax - 10;\n      break;\n    case 'light':\n      sMax = 55;\n      break;\n    default:\n      break;\n  }\n  return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n  var bMin = getMinimumBrightness(H, S);\n  var bMax = 100;\n  switch (options.luminosity) {\n    case 'dark':\n      bMax = bMin + 20;\n      break;\n    case 'light':\n      bMin = (bMax + bMin) / 2;\n      break;\n    case 'random':\n      bMin = 0;\n      bMax = 100;\n      break;\n    default:\n      break;\n  }\n  return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n  var lowerBounds = getColorInfo(H).lowerBounds;\n  for (var i = 0; i < lowerBounds.length - 1; i++) {\n    var s1 = lowerBounds[i][0];\n    var v1 = lowerBounds[i][1];\n    var s2 = lowerBounds[i + 1][0];\n    var v2 = lowerBounds[i + 1][1];\n    if (S >= s1 && S <= s2) {\n      var m = (v2 - v1) / (s2 - s1);\n      var b = v1 - m * s1;\n      return m * S + b;\n    }\n  }\n  return 0;\n}\nfunction getHueRange(colorInput) {\n  var num = parseInt(colorInput, 10);\n  if (!Number.isNaN(num) && num < 360 && num > 0) {\n    return [num, num];\n  }\n  if (typeof colorInput === 'string') {\n    var namedColor = bounds.find(function (n) {\n      return n.name === colorInput;\n    });\n    if (namedColor) {\n      var color = defineColor(namedColor);\n      if (color.hueRange) {\n        return color.hueRange;\n      }\n    }\n    var parsed = new TinyColor(colorInput);\n    if (parsed.isValid) {\n      var hue = parsed.toHsv().h;\n      return [hue, hue];\n    }\n  }\n  return [0, 360];\n}\nfunction getColorInfo(hue) {\n  // Maps red colors to make picking hue easier\n  if (hue >= 334 && hue <= 360) {\n    hue -= 360;\n  }\n  for (var _i = 0, bounds_1 = bounds; _i < bounds_1.length; _i++) {\n    var bound = bounds_1[_i];\n    var color = defineColor(bound);\n    if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n      return color;\n    }\n  }\n  throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n  if (seed === undefined) {\n    return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n  }\n  // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n  var max = range[1] || 1;\n  var min = range[0] || 0;\n  seed = (seed * 9301 + 49297) % 233280;\n  var rnd = seed / 233280.0;\n  return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n  var sMin = bound.lowerBounds[0][0];\n  var sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n  var bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n  var bMax = bound.lowerBounds[0][1];\n  return {\n    name: bound.name,\n    hueRange: bound.hueRange,\n    lowerBounds: bound.lowerBounds,\n    saturationRange: [sMin, sMax],\n    brightnessRange: [bMin, bMax]\n  };\n}\n/**\n * @hidden\n */\nexport var bounds = [{\n  name: 'monochrome',\n  hueRange: null,\n  lowerBounds: [[0, 0], [100, 0]]\n}, {\n  name: 'red',\n  hueRange: [-26, 18],\n  lowerBounds: [[20, 100], [30, 92], [40, 89], [50, 85], [60, 78], [70, 70], [80, 60], [90, 55], [100, 50]]\n}, {\n  name: 'orange',\n  hueRange: [19, 46],\n  lowerBounds: [[20, 100], [30, 93], [40, 88], [50, 86], [60, 85], [70, 70], [100, 70]]\n}, {\n  name: 'yellow',\n  hueRange: [47, 62],\n  lowerBounds: [[25, 100], [40, 94], [50, 89], [60, 86], [70, 84], [80, 82], [90, 80], [100, 75]]\n}, {\n  name: 'green',\n  hueRange: [63, 178],\n  lowerBounds: [[30, 100], [40, 90], [50, 85], [60, 81], [70, 74], [80, 64], [90, 50], [100, 40]]\n}, {\n  name: 'blue',\n  hueRange: [179, 257],\n  lowerBounds: [[20, 100], [30, 86], [40, 80], [50, 74], [60, 60], [70, 52], [80, 44], [90, 39], [100, 35]]\n}, {\n  name: 'purple',\n  hueRange: [258, 282],\n  lowerBounds: [[20, 100], [30, 87], [40, 79], [50, 70], [60, 65], [70, 59], [80, 52], [90, 45], [100, 42]]\n}, {\n  name: 'pink',\n  hueRange: [283, 334],\n  lowerBounds: [[20, 100], [30, 90], [40, 86], [60, 84], [80, 80], [90, 75], [100, 73]]\n}];", "map": {"version": 3, "names": ["TinyColor", "random", "options", "count", "undefined", "totalColors", "colors", "length", "seed", "push", "h", "pickHue", "hue", "s", "pickSaturation", "v", "pickBrightness", "res", "alpha", "a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getHueRange", "randomWithin", "luminosity", "saturationRange", "getColorInfo", "sMin", "sMax", "H", "S", "bMin", "getMinimumBrightness", "bMax", "lowerBounds", "i", "s1", "v1", "s2", "v2", "m", "b", "colorInput", "num", "parseInt", "Number", "isNaN", "namedColor", "bounds", "find", "n", "name", "color", "defineColor", "parsed", "<PERSON><PERSON><PERSON><PERSON>", "toHsv", "_i", "bounds_1", "bound", "Error", "range", "Math", "floor", "max", "min", "rnd", "brightnessRange"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/@ctrl/tinycolor/dist/module/random.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\n// randomColor by <PERSON> under the CC0 license\n// https://github.com/davidmerfield/randomColor/\nimport { TinyColor } from './index.js';\nexport function random(options) {\n    if (options === void 0) { options = {}; }\n    // Check if we need to generate multiple colors\n    if (options.count !== undefined &&\n        options.count !== null) {\n        var totalColors = options.count;\n        var colors = [];\n        options.count = undefined;\n        while (totalColors > colors.length) {\n            // Since we're generating multiple colors,\n            // incremement the seed. Otherwise we'd just\n            // generate the same color each time...\n            options.count = null;\n            if (options.seed) {\n                options.seed += 1;\n            }\n            colors.push(random(options));\n        }\n        options.count = totalColors;\n        return colors;\n    }\n    // First we pick a hue (H)\n    var h = pickHue(options.hue, options.seed);\n    // Then use H to determine saturation (S)\n    var s = pickSaturation(h, options);\n    // Then use S and H to determine brightness (B).\n    var v = pickBrightness(h, s, options);\n    var res = { h: h, s: s, v: v };\n    if (options.alpha !== undefined) {\n        res.a = options.alpha;\n    }\n    // Then we return the HSB color in the desired format\n    return new TinyColor(res);\n}\nfunction pickHue(hue, seed) {\n    var hueRange = getHueRange(hue);\n    var res = randomWithin(hueRange, seed);\n    // Instead of storing red as two seperate ranges,\n    // we group them, using negative numbers\n    if (res < 0) {\n        res = 360 + res;\n    }\n    return res;\n}\nfunction pickSaturation(hue, options) {\n    if (options.hue === 'monochrome') {\n        return 0;\n    }\n    if (options.luminosity === 'random') {\n        return randomWithin([0, 100], options.seed);\n    }\n    var saturationRange = getColorInfo(hue).saturationRange;\n    var sMin = saturationRange[0];\n    var sMax = saturationRange[1];\n    switch (options.luminosity) {\n        case 'bright':\n            sMin = 55;\n            break;\n        case 'dark':\n            sMin = sMax - 10;\n            break;\n        case 'light':\n            sMax = 55;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([sMin, sMax], options.seed);\n}\nfunction pickBrightness(H, S, options) {\n    var bMin = getMinimumBrightness(H, S);\n    var bMax = 100;\n    switch (options.luminosity) {\n        case 'dark':\n            bMax = bMin + 20;\n            break;\n        case 'light':\n            bMin = (bMax + bMin) / 2;\n            break;\n        case 'random':\n            bMin = 0;\n            bMax = 100;\n            break;\n        default:\n            break;\n    }\n    return randomWithin([bMin, bMax], options.seed);\n}\nfunction getMinimumBrightness(H, S) {\n    var lowerBounds = getColorInfo(H).lowerBounds;\n    for (var i = 0; i < lowerBounds.length - 1; i++) {\n        var s1 = lowerBounds[i][0];\n        var v1 = lowerBounds[i][1];\n        var s2 = lowerBounds[i + 1][0];\n        var v2 = lowerBounds[i + 1][1];\n        if (S >= s1 && S <= s2) {\n            var m = (v2 - v1) / (s2 - s1);\n            var b = v1 - m * s1;\n            return m * S + b;\n        }\n    }\n    return 0;\n}\nfunction getHueRange(colorInput) {\n    var num = parseInt(colorInput, 10);\n    if (!Number.isNaN(num) && num < 360 && num > 0) {\n        return [num, num];\n    }\n    if (typeof colorInput === 'string') {\n        var namedColor = bounds.find(function (n) { return n.name === colorInput; });\n        if (namedColor) {\n            var color = defineColor(namedColor);\n            if (color.hueRange) {\n                return color.hueRange;\n            }\n        }\n        var parsed = new TinyColor(colorInput);\n        if (parsed.isValid) {\n            var hue = parsed.toHsv().h;\n            return [hue, hue];\n        }\n    }\n    return [0, 360];\n}\nfunction getColorInfo(hue) {\n    // Maps red colors to make picking hue easier\n    if (hue >= 334 && hue <= 360) {\n        hue -= 360;\n    }\n    for (var _i = 0, bounds_1 = bounds; _i < bounds_1.length; _i++) {\n        var bound = bounds_1[_i];\n        var color = defineColor(bound);\n        if (color.hueRange && hue >= color.hueRange[0] && hue <= color.hueRange[1]) {\n            return color;\n        }\n    }\n    throw Error('Color not found');\n}\nfunction randomWithin(range, seed) {\n    if (seed === undefined) {\n        return Math.floor(range[0] + Math.random() * (range[1] + 1 - range[0]));\n    }\n    // Seeded random algorithm from http://indiegamr.com/generate-repeatable-random-numbers-in-js/\n    var max = range[1] || 1;\n    var min = range[0] || 0;\n    seed = (seed * 9301 + 49297) % 233280;\n    var rnd = seed / 233280.0;\n    return Math.floor(min + rnd * (max - min));\n}\nfunction defineColor(bound) {\n    var sMin = bound.lowerBounds[0][0];\n    var sMax = bound.lowerBounds[bound.lowerBounds.length - 1][0];\n    var bMin = bound.lowerBounds[bound.lowerBounds.length - 1][1];\n    var bMax = bound.lowerBounds[0][1];\n    return {\n        name: bound.name,\n        hueRange: bound.hueRange,\n        lowerBounds: bound.lowerBounds,\n        saturationRange: [sMin, sMax],\n        brightnessRange: [bMin, bMax],\n    };\n}\n/**\n * @hidden\n */\nexport var bounds = [\n    {\n        name: 'monochrome',\n        hueRange: null,\n        lowerBounds: [\n            [0, 0],\n            [100, 0],\n        ],\n    },\n    {\n        name: 'red',\n        hueRange: [-26, 18],\n        lowerBounds: [\n            [20, 100],\n            [30, 92],\n            [40, 89],\n            [50, 85],\n            [60, 78],\n            [70, 70],\n            [80, 60],\n            [90, 55],\n            [100, 50],\n        ],\n    },\n    {\n        name: 'orange',\n        hueRange: [19, 46],\n        lowerBounds: [\n            [20, 100],\n            [30, 93],\n            [40, 88],\n            [50, 86],\n            [60, 85],\n            [70, 70],\n            [100, 70],\n        ],\n    },\n    {\n        name: 'yellow',\n        hueRange: [47, 62],\n        lowerBounds: [\n            [25, 100],\n            [40, 94],\n            [50, 89],\n            [60, 86],\n            [70, 84],\n            [80, 82],\n            [90, 80],\n            [100, 75],\n        ],\n    },\n    {\n        name: 'green',\n        hueRange: [63, 178],\n        lowerBounds: [\n            [30, 100],\n            [40, 90],\n            [50, 85],\n            [60, 81],\n            [70, 74],\n            [80, 64],\n            [90, 50],\n            [100, 40],\n        ],\n    },\n    {\n        name: 'blue',\n        hueRange: [179, 257],\n        lowerBounds: [\n            [20, 100],\n            [30, 86],\n            [40, 80],\n            [50, 74],\n            [60, 60],\n            [70, 52],\n            [80, 44],\n            [90, 39],\n            [100, 35],\n        ],\n    },\n    {\n        name: 'purple',\n        hueRange: [258, 282],\n        lowerBounds: [\n            [20, 100],\n            [30, 87],\n            [40, 79],\n            [50, 70],\n            [60, 65],\n            [70, 59],\n            [80, 52],\n            [90, 45],\n            [100, 42],\n        ],\n    },\n    {\n        name: 'pink',\n        hueRange: [283, 334],\n        lowerBounds: [\n            [20, 100],\n            [30, 90],\n            [40, 86],\n            [60, 84],\n            [80, 80],\n            [90, 75],\n            [100, 73],\n        ],\n    },\n];\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,YAAY;AACtC,OAAO,SAASC,MAAMA,CAACC,OAAO,EAAE;EAC5B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC;EACA,IAAIA,OAAO,CAACC,KAAK,KAAKC,SAAS,IAC3BF,OAAO,CAACC,KAAK,KAAK,IAAI,EAAE;IACxB,IAAIE,WAAW,GAAGH,OAAO,CAACC,KAAK;IAC/B,IAAIG,MAAM,GAAG,EAAE;IACfJ,OAAO,CAACC,KAAK,GAAGC,SAAS;IACzB,OAAOC,WAAW,GAAGC,MAAM,CAACC,MAAM,EAAE;MAChC;MACA;MACA;MACAL,OAAO,CAACC,KAAK,GAAG,IAAI;MACpB,IAAID,OAAO,CAACM,IAAI,EAAE;QACdN,OAAO,CAACM,IAAI,IAAI,CAAC;MACrB;MACAF,MAAM,CAACG,IAAI,CAACR,MAAM,CAACC,OAAO,CAAC,CAAC;IAChC;IACAA,OAAO,CAACC,KAAK,GAAGE,WAAW;IAC3B,OAAOC,MAAM;EACjB;EACA;EACA,IAAII,CAAC,GAAGC,OAAO,CAACT,OAAO,CAACU,GAAG,EAAEV,OAAO,CAACM,IAAI,CAAC;EAC1C;EACA,IAAIK,CAAC,GAAGC,cAAc,CAACJ,CAAC,EAAER,OAAO,CAAC;EAClC;EACA,IAAIa,CAAC,GAAGC,cAAc,CAACN,CAAC,EAAEG,CAAC,EAAEX,OAAO,CAAC;EACrC,IAAIe,GAAG,GAAG;IAAEP,CAAC,EAAEA,CAAC;IAAEG,CAAC,EAAEA,CAAC;IAAEE,CAAC,EAAEA;EAAE,CAAC;EAC9B,IAAIb,OAAO,CAACgB,KAAK,KAAKd,SAAS,EAAE;IAC7Ba,GAAG,CAACE,CAAC,GAAGjB,OAAO,CAACgB,KAAK;EACzB;EACA;EACA,OAAO,IAAIlB,SAAS,CAACiB,GAAG,CAAC;AAC7B;AACA,SAASN,OAAOA,CAACC,GAAG,EAAEJ,IAAI,EAAE;EACxB,IAAIY,QAAQ,GAAGC,WAAW,CAACT,GAAG,CAAC;EAC/B,IAAIK,GAAG,GAAGK,YAAY,CAACF,QAAQ,EAAEZ,IAAI,CAAC;EACtC;EACA;EACA,IAAIS,GAAG,GAAG,CAAC,EAAE;IACTA,GAAG,GAAG,GAAG,GAAGA,GAAG;EACnB;EACA,OAAOA,GAAG;AACd;AACA,SAASH,cAAcA,CAACF,GAAG,EAAEV,OAAO,EAAE;EAClC,IAAIA,OAAO,CAACU,GAAG,KAAK,YAAY,EAAE;IAC9B,OAAO,CAAC;EACZ;EACA,IAAIV,OAAO,CAACqB,UAAU,KAAK,QAAQ,EAAE;IACjC,OAAOD,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAEpB,OAAO,CAACM,IAAI,CAAC;EAC/C;EACA,IAAIgB,eAAe,GAAGC,YAAY,CAACb,GAAG,CAAC,CAACY,eAAe;EACvD,IAAIE,IAAI,GAAGF,eAAe,CAAC,CAAC,CAAC;EAC7B,IAAIG,IAAI,GAAGH,eAAe,CAAC,CAAC,CAAC;EAC7B,QAAQtB,OAAO,CAACqB,UAAU;IACtB,KAAK,QAAQ;MACTG,IAAI,GAAG,EAAE;MACT;IACJ,KAAK,MAAM;MACPA,IAAI,GAAGC,IAAI,GAAG,EAAE;MAChB;IACJ,KAAK,OAAO;MACRA,IAAI,GAAG,EAAE;MACT;IACJ;MACI;EACR;EACA,OAAOL,YAAY,CAAC,CAACI,IAAI,EAAEC,IAAI,CAAC,EAAEzB,OAAO,CAACM,IAAI,CAAC;AACnD;AACA,SAASQ,cAAcA,CAACY,CAAC,EAAEC,CAAC,EAAE3B,OAAO,EAAE;EACnC,IAAI4B,IAAI,GAAGC,oBAAoB,CAACH,CAAC,EAAEC,CAAC,CAAC;EACrC,IAAIG,IAAI,GAAG,GAAG;EACd,QAAQ9B,OAAO,CAACqB,UAAU;IACtB,KAAK,MAAM;MACPS,IAAI,GAAGF,IAAI,GAAG,EAAE;MAChB;IACJ,KAAK,OAAO;MACRA,IAAI,GAAG,CAACE,IAAI,GAAGF,IAAI,IAAI,CAAC;MACxB;IACJ,KAAK,QAAQ;MACTA,IAAI,GAAG,CAAC;MACRE,IAAI,GAAG,GAAG;MACV;IACJ;MACI;EACR;EACA,OAAOV,YAAY,CAAC,CAACQ,IAAI,EAAEE,IAAI,CAAC,EAAE9B,OAAO,CAACM,IAAI,CAAC;AACnD;AACA,SAASuB,oBAAoBA,CAACH,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAII,WAAW,GAAGR,YAAY,CAACG,CAAC,CAAC,CAACK,WAAW;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,CAAC1B,MAAM,GAAG,CAAC,EAAE2B,CAAC,EAAE,EAAE;IAC7C,IAAIC,EAAE,GAAGF,WAAW,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAIE,EAAE,GAAGH,WAAW,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAIG,EAAE,GAAGJ,WAAW,CAACC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAII,EAAE,GAAGL,WAAW,CAACC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAIL,CAAC,IAAIM,EAAE,IAAIN,CAAC,IAAIQ,EAAE,EAAE;MACpB,IAAIE,CAAC,GAAG,CAACD,EAAE,GAAGF,EAAE,KAAKC,EAAE,GAAGF,EAAE,CAAC;MAC7B,IAAIK,CAAC,GAAGJ,EAAE,GAAGG,CAAC,GAAGJ,EAAE;MACnB,OAAOI,CAAC,GAAGV,CAAC,GAAGW,CAAC;IACpB;EACJ;EACA,OAAO,CAAC;AACZ;AACA,SAASnB,WAAWA,CAACoB,UAAU,EAAE;EAC7B,IAAIC,GAAG,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;EAClC,IAAI,CAACG,MAAM,CAACC,KAAK,CAACH,GAAG,CAAC,IAAIA,GAAG,GAAG,GAAG,IAAIA,GAAG,GAAG,CAAC,EAAE;IAC5C,OAAO,CAACA,GAAG,EAAEA,GAAG,CAAC;EACrB;EACA,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;IAChC,IAAIK,UAAU,GAAGC,MAAM,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACC,IAAI,KAAKT,UAAU;IAAE,CAAC,CAAC;IAC5E,IAAIK,UAAU,EAAE;MACZ,IAAIK,KAAK,GAAGC,WAAW,CAACN,UAAU,CAAC;MACnC,IAAIK,KAAK,CAAC/B,QAAQ,EAAE;QAChB,OAAO+B,KAAK,CAAC/B,QAAQ;MACzB;IACJ;IACA,IAAIiC,MAAM,GAAG,IAAIrD,SAAS,CAACyC,UAAU,CAAC;IACtC,IAAIY,MAAM,CAACC,OAAO,EAAE;MAChB,IAAI1C,GAAG,GAAGyC,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC7C,CAAC;MAC1B,OAAO,CAACE,GAAG,EAAEA,GAAG,CAAC;IACrB;EACJ;EACA,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;AACnB;AACA,SAASa,YAAYA,CAACb,GAAG,EAAE;EACvB;EACA,IAAIA,GAAG,IAAI,GAAG,IAAIA,GAAG,IAAI,GAAG,EAAE;IAC1BA,GAAG,IAAI,GAAG;EACd;EACA,KAAK,IAAI4C,EAAE,GAAG,CAAC,EAAEC,QAAQ,GAAGV,MAAM,EAAES,EAAE,GAAGC,QAAQ,CAAClD,MAAM,EAAEiD,EAAE,EAAE,EAAE;IAC5D,IAAIE,KAAK,GAAGD,QAAQ,CAACD,EAAE,CAAC;IACxB,IAAIL,KAAK,GAAGC,WAAW,CAACM,KAAK,CAAC;IAC9B,IAAIP,KAAK,CAAC/B,QAAQ,IAAIR,GAAG,IAAIuC,KAAK,CAAC/B,QAAQ,CAAC,CAAC,CAAC,IAAIR,GAAG,IAAIuC,KAAK,CAAC/B,QAAQ,CAAC,CAAC,CAAC,EAAE;MACxE,OAAO+B,KAAK;IAChB;EACJ;EACA,MAAMQ,KAAK,CAAC,iBAAiB,CAAC;AAClC;AACA,SAASrC,YAAYA,CAACsC,KAAK,EAAEpD,IAAI,EAAE;EAC/B,IAAIA,IAAI,KAAKJ,SAAS,EAAE;IACpB,OAAOyD,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC,GAAGC,IAAI,CAAC5D,MAAM,CAAC,CAAC,IAAI2D,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3E;EACA;EACA,IAAIG,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;EACvB,IAAII,GAAG,GAAGJ,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;EACvBpD,IAAI,GAAG,CAACA,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,MAAM;EACrC,IAAIyD,GAAG,GAAGzD,IAAI,GAAG,QAAQ;EACzB,OAAOqD,IAAI,CAACC,KAAK,CAACE,GAAG,GAAGC,GAAG,IAAIF,GAAG,GAAGC,GAAG,CAAC,CAAC;AAC9C;AACA,SAASZ,WAAWA,CAACM,KAAK,EAAE;EACxB,IAAIhC,IAAI,GAAGgC,KAAK,CAACzB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC,IAAIN,IAAI,GAAG+B,KAAK,CAACzB,WAAW,CAACyB,KAAK,CAACzB,WAAW,CAAC1B,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,IAAIuB,IAAI,GAAG4B,KAAK,CAACzB,WAAW,CAACyB,KAAK,CAACzB,WAAW,CAAC1B,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7D,IAAIyB,IAAI,GAAG0B,KAAK,CAACzB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC,OAAO;IACHiB,IAAI,EAAEQ,KAAK,CAACR,IAAI;IAChB9B,QAAQ,EAAEsC,KAAK,CAACtC,QAAQ;IACxBa,WAAW,EAAEyB,KAAK,CAACzB,WAAW;IAC9BT,eAAe,EAAE,CAACE,IAAI,EAAEC,IAAI,CAAC;IAC7BuC,eAAe,EAAE,CAACpC,IAAI,EAAEE,IAAI;EAChC,CAAC;AACL;AACA;AACA;AACA;AACA,OAAO,IAAIe,MAAM,GAAG,CAChB;EACIG,IAAI,EAAE,YAAY;EAClB9B,QAAQ,EAAE,IAAI;EACda,WAAW,EAAE,CACT,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAAC,GAAG,EAAE,CAAC,CAAC;AAEhB,CAAC,EACD;EACIiB,IAAI,EAAE,KAAK;EACX9B,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;EACnBa,WAAW,EAAE,CACT,CAAC,EAAE,EAAE,GAAG,CAAC,EACT,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC;AAEjB,CAAC,EACD;EACIiB,IAAI,EAAE,QAAQ;EACd9B,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBa,WAAW,EAAE,CACT,CAAC,EAAE,EAAE,GAAG,CAAC,EACT,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC;AAEjB,CAAC,EACD;EACIiB,IAAI,EAAE,QAAQ;EACd9B,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBa,WAAW,EAAE,CACT,CAAC,EAAE,EAAE,GAAG,CAAC,EACT,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC;AAEjB,CAAC,EACD;EACIiB,IAAI,EAAE,OAAO;EACb9B,QAAQ,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC;EACnBa,WAAW,EAAE,CACT,CAAC,EAAE,EAAE,GAAG,CAAC,EACT,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC;AAEjB,CAAC,EACD;EACIiB,IAAI,EAAE,MAAM;EACZ9B,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACpBa,WAAW,EAAE,CACT,CAAC,EAAE,EAAE,GAAG,CAAC,EACT,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC;AAEjB,CAAC,EACD;EACIiB,IAAI,EAAE,QAAQ;EACd9B,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACpBa,WAAW,EAAE,CACT,CAAC,EAAE,EAAE,GAAG,CAAC,EACT,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC;AAEjB,CAAC,EACD;EACIiB,IAAI,EAAE,MAAM;EACZ9B,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACpBa,WAAW,EAAE,CACT,CAAC,EAAE,EAAE,GAAG,CAAC,EACT,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,EAAE,EAAE,EAAE,CAAC,EACR,CAAC,GAAG,EAAE,EAAE,CAAC;AAEjB,CAAC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}