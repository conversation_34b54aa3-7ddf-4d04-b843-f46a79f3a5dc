{"ast": null, "code": "const calcColumnStyle = (column, fixedColumn, fixed) => {\n  var _a;\n  const flex = {\n    flexGrow: 0,\n    flexShrink: 0,\n    ...(fixed ? {} : {\n      flexGrow: column.flexGrow || 0,\n      flexShrink: column.flexShrink || 1\n    })\n  };\n  if (!fixed) {\n    flex.flexShrink = 1;\n  }\n  const style = {\n    ...((_a = column.style) != null ? _a : {}),\n    ...flex,\n    flexBasis: \"auto\",\n    width: column.width\n  };\n  if (!fixedColumn) {\n    if (column.maxWidth) style.maxWidth = column.maxWidth;\n    if (column.minWidth) style.minWidth = column.minWidth;\n  }\n  return style;\n};\nexport { calcColumnStyle };", "map": {"version": 3, "names": ["calcColumnStyle", "column", "fixedColumn", "fixed", "_a", "flex", "flexGrow", "flexShrink", "style", "flexBasis", "width", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>"], "sources": ["../../../../../../../packages/components/table-v2/src/composables/utils.ts"], "sourcesContent": ["import type { CSSProperties } from 'vue'\nimport type { AnyColumns } from '../types'\n\nexport const calcColumnStyle = (\n  column: AnyColumns[number],\n  fixedColumn: boolean,\n  fixed: boolean\n): CSSProperties => {\n  const flex = {\n    flexGrow: 0,\n    flexShrink: 0,\n    ...(fixed\n      ? {}\n      : {\n          flexGrow: column.flexGrow || 0,\n          flexShrink: column.flexShrink || 1,\n        }),\n  }\n\n  if (!fixed) {\n    flex.flexShrink = 1\n  }\n\n  const style = {\n    ...(column.style ?? {}),\n    ...flex,\n    flexBasis: 'auto',\n    width: column.width,\n  }\n\n  if (!fixedColumn) {\n    if (column.maxWidth) style.maxWidth = column.maxWidth\n    if (column.minWidth) style.minWidth = column.minWidth\n  }\n\n  return style\n}\n"], "mappings": "AAAY,MAACA,eAAe,GAAGA,CAACC,MAAM,EAAEC,WAAW,EAAEC,KAAK,KAAK;EAC7D,IAAIC,EAAE;EACN,MAAMC,IAAI,GAAG;IACXC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE,CAAC;IACb,IAAGJ,KAAK,GAAG,EAAE,GAAG;MACdG,QAAQ,EAAEL,MAAM,CAACK,QAAQ,IAAI,CAAC;MAC9BC,UAAU,EAAEN,MAAM,CAACM,UAAU,IAAI;IACvC,CAAK;EACL,CAAG;EACD,IAAI,CAACJ,KAAK,EAAE;IACVE,IAAI,CAACE,UAAU,GAAG,CAAC;EACvB;EACE,MAAMC,KAAK,GAAG;IACZ,IAAG,CAACJ,EAAE,GAAGH,MAAM,CAACO,KAAK,KAAK,IAAI,GAAGJ,EAAE,GAAG,EAAE;IACxC,GAAGC,IAAI;IACPI,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAET,MAAM,CAACS;EAClB,CAAG;EACD,IAAI,CAACR,WAAW,EAAE;IAChB,IAAID,MAAM,CAACU,QAAQ,EACjBH,KAAK,CAACG,QAAQ,GAAGV,MAAM,CAACU,QAAQ;IAClC,IAAIV,MAAM,CAACW,QAAQ,EACjBJ,KAAK,CAACI,QAAQ,GAAGX,MAAM,CAACW,QAAQ;EACtC;EACE,OAAOJ,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}