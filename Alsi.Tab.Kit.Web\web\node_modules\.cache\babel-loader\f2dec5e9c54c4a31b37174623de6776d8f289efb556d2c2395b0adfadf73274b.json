{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\nimport isPlainObject from './isPlainObject.js';\n\n/** `Object#toString` result references. */\nvar domExcTag = '[object DOMException]',\n  errorTag = '[object Error]';\n\n/**\n * Checks if `value` is an `Error`, `EvalError`, `RangeError`, `ReferenceError`,\n * `SyntaxError`, `TypeError`, or `URIError` object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an error object, else `false`.\n * @example\n *\n * _.isError(new Error);\n * // => true\n *\n * _.isError(Error);\n * // => false\n */\nfunction isError(value) {\n  if (!isObjectLike(value)) {\n    return false;\n  }\n  var tag = baseGetTag(value);\n  return tag == errorTag || tag == domExcTag || typeof value.message == 'string' && typeof value.name == 'string' && !isPlainObject(value);\n}\nexport default isError;", "map": {"version": 3, "names": ["baseGetTag", "isObjectLike", "isPlainObject", "domExcTag", "errorTag", "isError", "value", "tag", "message", "name"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/isError.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\nimport isPlainObject from './isPlainObject.js';\n\n/** `Object#toString` result references. */\nvar domExcTag = '[object DOMException]',\n    errorTag = '[object Error]';\n\n/**\n * Checks if `value` is an `Error`, `EvalError`, `RangeError`, `ReferenceError`,\n * `SyntaxError`, `TypeError`, or `URIError` object.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an error object, else `false`.\n * @example\n *\n * _.isError(new Error);\n * // => true\n *\n * _.isError(Error);\n * // => false\n */\nfunction isError(value) {\n  if (!isObjectLike(value)) {\n    return false;\n  }\n  var tag = baseGetTag(value);\n  return tag == errorTag || tag == domExcTag ||\n    (typeof value.message == 'string' && typeof value.name == 'string' && !isPlainObject(value));\n}\n\nexport default isError;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;;AAE9C;AACA,IAAIC,SAAS,GAAG,uBAAuB;EACnCC,QAAQ,GAAG,gBAAgB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAI,CAACL,YAAY,CAACK,KAAK,CAAC,EAAE;IACxB,OAAO,KAAK;EACd;EACA,IAAIC,GAAG,GAAGP,UAAU,CAACM,KAAK,CAAC;EAC3B,OAAOC,GAAG,IAAIH,QAAQ,IAAIG,GAAG,IAAIJ,SAAS,IACvC,OAAOG,KAAK,CAACE,OAAO,IAAI,QAAQ,IAAI,OAAOF,KAAK,CAACG,IAAI,IAAI,QAAQ,IAAI,CAACP,aAAa,CAACI,KAAK,CAAE;AAChG;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}