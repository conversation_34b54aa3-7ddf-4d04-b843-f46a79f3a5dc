{"ast": null, "code": "import { computed } from 'vue';\nconst usePropsAlias = props => {\n  const initProps = {\n    label: \"label\",\n    key: \"key\",\n    disabled: \"disabled\"\n  };\n  return computed(() => ({\n    ...initProps,\n    ...props.props\n  }));\n};\nexport { usePropsAlias };", "map": {"version": 3, "names": ["usePropsAlias", "props", "initProps", "label", "key", "disabled", "computed"], "sources": ["../../../../../../../packages/components/transfer/src/composables/use-props-alias.ts"], "sourcesContent": ["import { computed } from 'vue'\n\nimport type { TransferPropsAlias } from '../transfer'\n\nexport const usePropsAlias = (props: { props: TransferPropsAlias }) => {\n  const initProps: Required<TransferPropsAlias> = {\n    label: 'label',\n    key: 'key',\n    disabled: 'disabled',\n  }\n\n  return computed(() => ({\n    ...initProps,\n    ...props.props,\n  }))\n}\n"], "mappings": ";AACY,MAACA,aAAa,GAAIC,KAAK,IAAK;EACtC,MAAMC,SAAS,GAAG;IAChBC,KAAK,EAAE,OAAO;IACdC,GAAG,EAAE,KAAK;IACVC,QAAQ,EAAE;EACd,CAAG;EACD,OAAOC,QAAQ,CAAC,OAAO;IACrB,GAAGJ,SAAS;IACZ,GAAGD,KAAK,CAACA;EACb,CAAG,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}