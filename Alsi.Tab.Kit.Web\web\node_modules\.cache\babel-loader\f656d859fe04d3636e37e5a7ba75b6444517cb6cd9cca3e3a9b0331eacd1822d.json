{"ast": null, "code": "import coreJsData from './_coreJsData.js';\nimport isFunction from './isFunction.js';\nimport stubFalse from './stubFalse.js';\n\n/**\n * Checks if `func` is capable of being masked.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `func` is maskable, else `false`.\n */\nvar isMaskable = coreJsData ? isFunction : stubFalse;\nexport default isMaskable;", "map": {"version": 3, "names": ["coreJsData", "isFunction", "stubFalse", "isMaskable"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_isMaskable.js"], "sourcesContent": ["import coreJsData from './_coreJsData.js';\nimport isFunction from './isFunction.js';\nimport stubFalse from './stubFalse.js';\n\n/**\n * Checks if `func` is capable of being masked.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `func` is maskable, else `false`.\n */\nvar isMaskable = coreJsData ? isFunction : stubFalse;\n\nexport default isMaskable;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAGH,UAAU,GAAGC,UAAU,GAAGC,SAAS;AAEpD,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}