{"ast": null, "code": "import baseDifference from './_baseDifference.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseUniq from './_baseUniq.js';\n\n/**\n * The base implementation of methods like `_.xor`, without support for\n * iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of values.\n */\nfunction baseXor(arrays, iteratee, comparator) {\n  var length = arrays.length;\n  if (length < 2) {\n    return length ? baseUniq(arrays[0]) : [];\n  }\n  var index = -1,\n    result = Array(length);\n  while (++index < length) {\n    var array = arrays[index],\n      othIndex = -1;\n    while (++othIndex < length) {\n      if (othIndex != index) {\n        result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);\n      }\n    }\n  }\n  return baseUniq(baseFlatten(result, 1), iteratee, comparator);\n}\nexport default baseXor;", "map": {"version": 3, "names": ["baseDifference", "baseFlatten", "baseUniq", "baseXor", "arrays", "iteratee", "comparator", "length", "index", "result", "Array", "array", "othIndex"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_baseXor.js"], "sourcesContent": ["import baseDifference from './_baseDifference.js';\nimport baseFlatten from './_baseFlatten.js';\nimport baseUniq from './_baseUniq.js';\n\n/**\n * The base implementation of methods like `_.xor`, without support for\n * iteratee shorthands, that accepts an array of arrays to inspect.\n *\n * @private\n * @param {Array} arrays The arrays to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of values.\n */\nfunction baseXor(arrays, iteratee, comparator) {\n  var length = arrays.length;\n  if (length < 2) {\n    return length ? baseUniq(arrays[0]) : [];\n  }\n  var index = -1,\n      result = Array(length);\n\n  while (++index < length) {\n    var array = arrays[index],\n        othIndex = -1;\n\n    while (++othIndex < length) {\n      if (othIndex != index) {\n        result[index] = baseDifference(result[index] || array, arrays[othIndex], iteratee, comparator);\n      }\n    }\n  }\n  return baseUniq(baseFlatten(result, 1), iteratee, comparator);\n}\n\nexport default baseXor;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7C,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM;EAC1B,IAAIA,MAAM,GAAG,CAAC,EAAE;IACd,OAAOA,MAAM,GAAGL,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;EAC1C;EACA,IAAII,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGC,KAAK,CAACH,MAAM,CAAC;EAE1B,OAAO,EAAEC,KAAK,GAAGD,MAAM,EAAE;IACvB,IAAII,KAAK,GAAGP,MAAM,CAACI,KAAK,CAAC;MACrBI,QAAQ,GAAG,CAAC,CAAC;IAEjB,OAAO,EAAEA,QAAQ,GAAGL,MAAM,EAAE;MAC1B,IAAIK,QAAQ,IAAIJ,KAAK,EAAE;QACrBC,MAAM,CAACD,KAAK,CAAC,GAAGR,cAAc,CAACS,MAAM,CAACD,KAAK,CAAC,IAAIG,KAAK,EAAEP,MAAM,CAACQ,QAAQ,CAAC,EAAEP,QAAQ,EAAEC,UAAU,CAAC;MAChG;IACF;EACF;EACA,OAAOJ,QAAQ,CAACD,WAAW,CAACQ,MAAM,EAAE,CAAC,CAAC,EAAEJ,QAAQ,EAAEC,UAAU,CAAC;AAC/D;AAEA,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}