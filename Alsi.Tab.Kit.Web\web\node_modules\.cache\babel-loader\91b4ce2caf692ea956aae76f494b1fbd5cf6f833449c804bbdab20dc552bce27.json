{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, computed } from 'vue';\nimport { useProps } from './useProps.mjs';\nfunction useAllowCreate(props, states) {\n  const {\n    aliasProps,\n    getLabel,\n    getValue\n  } = useProps(props);\n  const createOptionCount = ref(0);\n  const cachedSelectedOption = ref();\n  const enableAllowCreateMode = computed(() => {\n    return props.allowCreate && props.filterable;\n  });\n  function hasExistingOption(query) {\n    const hasOption = option => getLabel(option) === query;\n    return props.options && props.options.some(hasOption) || states.createdOptions.some(hasOption);\n  }\n  function selectNewOption(option) {\n    if (!enableAllowCreateMode.value) {\n      return;\n    }\n    if (props.multiple && option.created) {\n      createOptionCount.value++;\n    } else {\n      cachedSelectedOption.value = option;\n    }\n  }\n  function createNewOption(query) {\n    if (enableAllowCreateMode.value) {\n      if (query && query.length > 0) {\n        if (hasExistingOption(query)) {\n          return;\n        }\n        const newOption = {\n          [aliasProps.value.value]: query,\n          [aliasProps.value.label]: query,\n          created: true,\n          [aliasProps.value.disabled]: false\n        };\n        if (states.createdOptions.length >= createOptionCount.value) {\n          states.createdOptions[createOptionCount.value] = newOption;\n        } else {\n          states.createdOptions.push(newOption);\n        }\n      } else {\n        if (props.multiple) {\n          states.createdOptions.length = createOptionCount.value;\n        } else {\n          const selectedOption = cachedSelectedOption.value;\n          states.createdOptions.length = 0;\n          if (selectedOption && selectedOption.created) {\n            states.createdOptions.push(selectedOption);\n          }\n        }\n      }\n    }\n  }\n  function removeNewOption(option) {\n    if (!enableAllowCreateMode.value || !option || !option.created || option.created && props.reserveKeyword && states.inputValue === getLabel(option)) {\n      return;\n    }\n    const idx = states.createdOptions.findIndex(it => getValue(it) === getValue(option));\n    if (~idx) {\n      states.createdOptions.splice(idx, 1);\n      createOptionCount.value--;\n    }\n  }\n  function clearAllNewOption() {\n    if (enableAllowCreateMode.value) {\n      states.createdOptions.length = 0;\n      createOptionCount.value = 0;\n    }\n  }\n  return {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption\n  };\n}\nexport { useAllowCreate };", "map": {"version": 3, "names": ["useAllowCreate", "props", "states", "aliasProps", "get<PERSON><PERSON><PERSON>", "getValue", "useProps", "createOptionCount", "ref", "cachedSelectedOption", "enableAllowCreateMode", "computed", "allowCreate", "filterable", "hasExistingOption", "query", "hasOption", "option", "options", "some", "createdOptions", "selectNewOption", "value", "multiple", "created", "createNewOption", "length", "newOption", "label", "disabled", "push", "selectedOption", "removeNewOption", "reserveKeyword", "inputValue", "idx", "findIndex", "it", "splice", "clearAllNewOption"], "sources": ["../../../../../../packages/components/select-v2/src/useAllowCreate.ts"], "sourcesContent": ["import { computed, ref } from 'vue'\nimport { useProps } from './useProps'\nimport type { SelectV2Props } from './token'\nimport type { Option, SelectStates } from './select.types'\n\nexport function useAllowCreate(props: SelectV2Props, states: SelectStates) {\n  const { aliasProps, getLabel, getValue } = useProps(props)\n\n  const createOptionCount = ref(0)\n  const cachedSelectedOption = ref<Option>()\n\n  const enableAllowCreateMode = computed(() => {\n    return props.allowCreate && props.filterable\n  })\n\n  function hasExistingOption(query: string) {\n    const hasOption = (option: Option) => getLabel(option) === query\n    return (\n      (props.options && props.options.some(hasOption)) ||\n      states.createdOptions.some(hasOption)\n    )\n  }\n\n  function selectNewOption(option: Option) {\n    if (!enableAllowCreateMode.value) {\n      return\n    }\n    if (props.multiple && option.created) {\n      createOptionCount.value++\n    } else {\n      cachedSelectedOption.value = option\n    }\n  }\n\n  function createNewOption(query: string) {\n    if (enableAllowCreateMode.value) {\n      if (query && query.length > 0) {\n        if (hasExistingOption(query)) {\n          return\n        }\n        const newOption = {\n          [aliasProps.value.value]: query,\n          [aliasProps.value.label]: query,\n          created: true,\n          [aliasProps.value.disabled]: false,\n        }\n        if (states.createdOptions.length >= createOptionCount.value) {\n          states.createdOptions[createOptionCount.value] = newOption\n        } else {\n          states.createdOptions.push(newOption)\n        }\n      } else {\n        if (props.multiple) {\n          states.createdOptions.length = createOptionCount.value\n        } else {\n          const selectedOption = cachedSelectedOption.value\n          states.createdOptions.length = 0\n          if (selectedOption && selectedOption.created) {\n            states.createdOptions.push(selectedOption)\n          }\n        }\n      }\n    }\n  }\n\n  function removeNewOption(option: Option) {\n    if (\n      !enableAllowCreateMode.value ||\n      !option ||\n      !option.created ||\n      (option.created &&\n        props.reserveKeyword &&\n        states.inputValue === getLabel(option))\n    ) {\n      return\n    }\n    const idx = states.createdOptions.findIndex(\n      (it) => getValue(it) === getValue(option)\n    )\n    if (~idx) {\n      states.createdOptions.splice(idx, 1)\n      createOptionCount.value--\n    }\n  }\n\n  function clearAllNewOption() {\n    if (enableAllowCreateMode.value) {\n      states.createdOptions.length = 0\n      createOptionCount.value = 0\n    }\n  }\n\n  return {\n    createNewOption,\n    removeNewOption,\n    selectNewOption,\n    clearAllNewOption,\n  }\n}\n"], "mappings": ";;;;;AAEO,SAASA,cAAcA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC5C,MAAM;IAAEC,UAAU;IAAEC,QAAQ;IAAEC;EAAQ,CAAE,GAAGC,QAAQ,CAACL,KAAK,CAAC;EAC1D,MAAMM,iBAAiB,GAAGC,GAAG,CAAC,CAAC,CAAC;EAChC,MAAMC,oBAAoB,GAAGD,GAAG,EAAE;EAClC,MAAME,qBAAqB,GAAGC,QAAQ,CAAC,MAAM;IAC3C,OAAOV,KAAK,CAACW,WAAW,IAAIX,KAAK,CAACY,UAAU;EAChD,CAAG,CAAC;EACF,SAASC,iBAAiBA,CAACC,KAAK,EAAE;IAChC,MAAMC,SAAS,GAAIC,MAAM,IAAKb,QAAQ,CAACa,MAAM,CAAC,KAAKF,KAAK;IACxD,OAAOd,KAAK,CAACiB,OAAO,IAAIjB,KAAK,CAACiB,OAAO,CAACC,IAAI,CAACH,SAAS,CAAC,IAAId,MAAM,CAACkB,cAAc,CAACD,IAAI,CAACH,SAAS,CAAC;EAClG;EACE,SAASK,eAAeA,CAACJ,MAAM,EAAE;IAC/B,IAAI,CAACP,qBAAqB,CAACY,KAAK,EAAE;MAChC;IACN;IACI,IAAIrB,KAAK,CAACsB,QAAQ,IAAIN,MAAM,CAACO,OAAO,EAAE;MACpCjB,iBAAiB,CAACe,KAAK,EAAE;IAC/B,CAAK,MAAM;MACLb,oBAAoB,CAACa,KAAK,GAAGL,MAAM;IACzC;EACA;EACE,SAASQ,eAAeA,CAACV,KAAK,EAAE;IAC9B,IAAIL,qBAAqB,CAACY,KAAK,EAAE;MAC/B,IAAIP,KAAK,IAAIA,KAAK,CAACW,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAIZ,iBAAiB,CAACC,KAAK,CAAC,EAAE;UAC5B;QACV;QACQ,MAAMY,SAAS,GAAG;UAChB,CAACxB,UAAU,CAACmB,KAAK,CAACA,KAAK,GAAGP,KAAK;UAC/B,CAACZ,UAAU,CAACmB,KAAK,CAACM,KAAK,GAAGb,KAAK;UAC/BS,OAAO,EAAE,IAAI;UACb,CAACrB,UAAU,CAACmB,KAAK,CAACO,QAAQ,GAAG;QACvC,CAAS;QACD,IAAI3B,MAAM,CAACkB,cAAc,CAACM,MAAM,IAAInB,iBAAiB,CAACe,KAAK,EAAE;UAC3DpB,MAAM,CAACkB,cAAc,CAACb,iBAAiB,CAACe,KAAK,CAAC,GAAGK,SAAS;QACpE,CAAS,MAAM;UACLzB,MAAM,CAACkB,cAAc,CAACU,IAAI,CAACH,SAAS,CAAC;QAC/C;MACA,CAAO,MAAM;QACL,IAAI1B,KAAK,CAACsB,QAAQ,EAAE;UAClBrB,MAAM,CAACkB,cAAc,CAACM,MAAM,GAAGnB,iBAAiB,CAACe,KAAK;QAChE,CAAS,MAAM;UACL,MAAMS,cAAc,GAAGtB,oBAAoB,CAACa,KAAK;UACjDpB,MAAM,CAACkB,cAAc,CAACM,MAAM,GAAG,CAAC;UAChC,IAAIK,cAAc,IAAIA,cAAc,CAACP,OAAO,EAAE;YAC5CtB,MAAM,CAACkB,cAAc,CAACU,IAAI,CAACC,cAAc,CAAC;UACtD;QACA;MACA;IACA;EACA;EACE,SAASC,eAAeA,CAACf,MAAM,EAAE;IAC/B,IAAI,CAACP,qBAAqB,CAACY,KAAK,IAAI,CAACL,MAAM,IAAI,CAACA,MAAM,CAACO,OAAO,IAAIP,MAAM,CAACO,OAAO,IAAIvB,KAAK,CAACgC,cAAc,IAAI/B,MAAM,CAACgC,UAAU,KAAK9B,QAAQ,CAACa,MAAM,CAAC,EAAE;MAClJ;IACN;IACI,MAAMkB,GAAG,GAAGjC,MAAM,CAACkB,cAAc,CAACgB,SAAS,CAAEC,EAAE,IAAKhC,QAAQ,CAACgC,EAAE,CAAC,KAAKhC,QAAQ,CAACY,MAAM,CAAC,CAAC;IACtF,IAAI,CAACkB,GAAG,EAAE;MACRjC,MAAM,CAACkB,cAAc,CAACkB,MAAM,CAACH,GAAG,EAAE,CAAC,CAAC;MACpC5B,iBAAiB,CAACe,KAAK,EAAE;IAC/B;EACA;EACE,SAASiB,iBAAiBA,CAAA,EAAG;IAC3B,IAAI7B,qBAAqB,CAACY,KAAK,EAAE;MAC/BpB,MAAM,CAACkB,cAAc,CAACM,MAAM,GAAG,CAAC;MAChCnB,iBAAiB,CAACe,KAAK,GAAG,CAAC;IACjC;EACA;EACE,OAAO;IACLG,eAAe;IACfO,eAAe;IACfX,eAAe;IACfkB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}