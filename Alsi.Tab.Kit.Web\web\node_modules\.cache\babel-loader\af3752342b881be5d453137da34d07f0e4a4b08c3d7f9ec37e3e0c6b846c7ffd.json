{"ast": null, "code": "/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that negates the result of the predicate `func`. The\n * `func` predicate is invoked with the `this` binding and arguments of the\n * created function.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} predicate The predicate to negate.\n * @returns {Function} Returns the new negated function.\n * @example\n *\n * function isEven(n) {\n *   return n % 2 == 0;\n * }\n *\n * _.filter([1, 2, 3, 4, 5, 6], _.negate(isEven));\n * // => [1, 3, 5]\n */\nfunction negate(predicate) {\n  if (typeof predicate != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  return function () {\n    var args = arguments;\n    switch (args.length) {\n      case 0:\n        return !predicate.call(this);\n      case 1:\n        return !predicate.call(this, args[0]);\n      case 2:\n        return !predicate.call(this, args[0], args[1]);\n      case 3:\n        return !predicate.call(this, args[0], args[1], args[2]);\n    }\n    return !predicate.apply(this, args);\n  };\n}\nexport default negate;", "map": {"version": 3, "names": ["FUNC_ERROR_TEXT", "negate", "predicate", "TypeError", "args", "arguments", "length", "call", "apply"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/negate.js"], "sourcesContent": ["/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that negates the result of the predicate `func`. The\n * `func` predicate is invoked with the `this` binding and arguments of the\n * created function.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} predicate The predicate to negate.\n * @returns {Function} Returns the new negated function.\n * @example\n *\n * function isEven(n) {\n *   return n % 2 == 0;\n * }\n *\n * _.filter([1, 2, 3, 4, 5, 6], _.negate(isEven));\n * // => [1, 3, 5]\n */\nfunction negate(predicate) {\n  if (typeof predicate != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  return function() {\n    var args = arguments;\n    switch (args.length) {\n      case 0: return !predicate.call(this);\n      case 1: return !predicate.call(this, args[0]);\n      case 2: return !predicate.call(this, args[0], args[1]);\n      case 3: return !predicate.call(this, args[0], args[1], args[2]);\n    }\n    return !predicate.apply(this, args);\n  };\n}\n\nexport default negate;\n"], "mappings": "AAAA;AACA,IAAIA,eAAe,GAAG,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,SAAS,EAAE;EACzB,IAAI,OAAOA,SAAS,IAAI,UAAU,EAAE;IAClC,MAAM,IAAIC,SAAS,CAACH,eAAe,CAAC;EACtC;EACA,OAAO,YAAW;IAChB,IAAII,IAAI,GAAGC,SAAS;IACpB,QAAQD,IAAI,CAACE,MAAM;MACjB,KAAK,CAAC;QAAE,OAAO,CAACJ,SAAS,CAACK,IAAI,CAAC,IAAI,CAAC;MACpC,KAAK,CAAC;QAAE,OAAO,CAACL,SAAS,CAACK,IAAI,CAAC,IAAI,EAAEH,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7C,KAAK,CAAC;QAAE,OAAO,CAACF,SAAS,CAACK,IAAI,CAAC,IAAI,EAAEH,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MACtD,KAAK,CAAC;QAAE,OAAO,CAACF,SAAS,CAACK,IAAI,CAAC,IAAI,EAAEH,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IACjE;IACA,OAAO,CAACF,SAAS,CAACM,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;EACrC,CAAC;AACH;AAEA,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}