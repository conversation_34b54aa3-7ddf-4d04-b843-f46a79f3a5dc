{"ast": null, "code": "import LodashWrapper from './_LodashWrapper.js';\n\n/**\n * Executes the chain sequence and returns the wrapped result.\n *\n * @name commit\n * @memberOf _\n * @since 3.2.0\n * @category Seq\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var array = [1, 2];\n * var wrapped = _(array).push(3);\n *\n * console.log(array);\n * // => [1, 2]\n *\n * wrapped = wrapped.commit();\n * console.log(array);\n * // => [1, 2, 3]\n *\n * wrapped.last();\n * // => 3\n *\n * console.log(array);\n * // => [1, 2, 3]\n */\nfunction wrapperCommit() {\n  return new LodashWrapper(this.value(), this.__chain__);\n}\nexport default wrapperCommit;", "map": {"version": 3, "names": ["LodashWrapper", "wrapperCommit", "value", "__chain__"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/commit.js"], "sourcesContent": ["import LodashWrapper from './_LodashWrapper.js';\n\n/**\n * Executes the chain sequence and returns the wrapped result.\n *\n * @name commit\n * @memberOf _\n * @since 3.2.0\n * @category Seq\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var array = [1, 2];\n * var wrapped = _(array).push(3);\n *\n * console.log(array);\n * // => [1, 2]\n *\n * wrapped = wrapped.commit();\n * console.log(array);\n * // => [1, 2, 3]\n *\n * wrapped.last();\n * // => 3\n *\n * console.log(array);\n * // => [1, 2, 3]\n */\nfunction wrapperCommit() {\n  return new LodashWrapper(this.value(), this.__chain__);\n}\n\nexport default wrapperCommit;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAA,EAAG;EACvB,OAAO,IAAID,aAAa,CAAC,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,SAAS,CAAC;AACxD;AAEA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}