{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { getCurrentInstance, onBeforeMount, onMounted, onUpdated, onUnmounted, computed } from 'vue';\nfunction useLayoutObserver(root) {\n  const instance = getCurrentInstance();\n  onBeforeMount(() => {\n    tableLayout.value.addObserver(instance);\n  });\n  onMounted(() => {\n    onColumnsChange(tableLayout.value);\n    onScrollableChange(tableLayout.value);\n  });\n  onUpdated(() => {\n    onColumnsChange(tableLayout.value);\n    onScrollableChange(tableLayout.value);\n  });\n  onUnmounted(() => {\n    tableLayout.value.removeObserver(instance);\n  });\n  const tableLayout = computed(() => {\n    const layout = root.layout;\n    if (!layout) {\n      throw new Error(\"Can not find table layout.\");\n    }\n    return layout;\n  });\n  const onColumnsChange = layout => {\n    var _a;\n    const cols = ((_a = root.vnode.el) == null ? void 0 : _a.querySelectorAll(\"colgroup > col\")) || [];\n    if (!cols.length) return;\n    const flattenColumns = layout.getFlattenColumns();\n    const columnsMap = {};\n    flattenColumns.forEach(column => {\n      columnsMap[column.id] = column;\n    });\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i];\n      const name = col.getAttribute(\"name\");\n      const column = columnsMap[name];\n      if (column) {\n        col.setAttribute(\"width\", column.realWidth || column.width);\n      }\n    }\n  };\n  const onScrollableChange = layout => {\n    var _a, _b;\n    const cols = ((_a = root.vnode.el) == null ? void 0 : _a.querySelectorAll(\"colgroup > col[name=gutter]\")) || [];\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i];\n      col.setAttribute(\"width\", layout.scrollY.value ? layout.gutterWidth : \"0\");\n    }\n    const ths = ((_b = root.vnode.el) == null ? void 0 : _b.querySelectorAll(\"th.gutter\")) || [];\n    for (let i = 0, j = ths.length; i < j; i++) {\n      const th = ths[i];\n      th.style.width = layout.scrollY.value ? `${layout.gutterWidth}px` : \"0\";\n      th.style.display = layout.scrollY.value ? \"\" : \"none\";\n    }\n  };\n  return {\n    tableLayout: tableLayout.value,\n    onColumnsChange,\n    onScrollableChange\n  };\n}\nexport { useLayoutObserver as default };", "map": {"version": 3, "names": ["useLayoutObserver", "root", "instance", "getCurrentInstance", "onBeforeMount", "tableLayout", "value", "addObserver", "onMounted", "onColumnsChange", "onScrollableChange", "onUpdated", "onUnmounted", "removeObserver", "computed", "layout", "Error", "_a", "cols", "vnode", "el", "querySelectorAll", "length", "flattenColumns", "getFlattenColumns", "columnsMap", "for<PERSON>ach", "column", "id", "i", "j", "col", "name", "getAttribute", "setAttribute", "realWidth", "width", "_b", "scrollY", "gutterWidth", "ths", "th", "style", "display"], "sources": ["../../../../../../packages/components/table/src/layout-observer.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  computed,\n  getCurrentInstance,\n  onBeforeMount,\n  onMounted,\n  onUnmounted,\n  onUpdated,\n} from 'vue'\n\nimport type { TableHeader } from './table-header'\nimport type TableLayout from './table-layout'\nimport type { Table } from './table/defaults'\n\nfunction useLayoutObserver<T>(root: Table<T>) {\n  const instance = getCurrentInstance() as TableHeader\n  onBeforeMount(() => {\n    tableLayout.value.addObserver(instance)\n  })\n  onMounted(() => {\n    onColumnsChange(tableLayout.value)\n    onScrollableChange(tableLayout.value)\n  })\n  onUpdated(() => {\n    onColumnsChange(tableLayout.value)\n    onScrollableChange(tableLayout.value)\n  })\n  onUnmounted(() => {\n    tableLayout.value.removeObserver(instance)\n  })\n  const tableLayout = computed(() => {\n    const layout = root.layout as TableLayout<T>\n    if (!layout) {\n      throw new Error('Can not find table layout.')\n    }\n    return layout\n  })\n  const onColumnsChange = (layout: TableLayout<T>) => {\n    const cols = root.vnode.el?.querySelectorAll('colgroup > col') || []\n    if (!cols.length) return\n    const flattenColumns = layout.getFlattenColumns()\n    const columnsMap = {}\n    flattenColumns.forEach((column) => {\n      columnsMap[column.id] = column\n    })\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i]\n      const name = col.getAttribute('name')\n      const column = columnsMap[name]\n      if (column) {\n        col.setAttribute('width', column.realWidth || column.width)\n      }\n    }\n  }\n\n  const onScrollableChange = (layout: TableLayout<T>) => {\n    const cols =\n      root.vnode.el?.querySelectorAll('colgroup > col[name=gutter]') || []\n    for (let i = 0, j = cols.length; i < j; i++) {\n      const col = cols[i]\n      col.setAttribute('width', layout.scrollY.value ? layout.gutterWidth : '0')\n    }\n    const ths = root.vnode.el?.querySelectorAll('th.gutter') || []\n    for (let i = 0, j = ths.length; i < j; i++) {\n      const th = ths[i]\n      th.style.width = layout.scrollY.value ? `${layout.gutterWidth}px` : '0'\n      th.style.display = layout.scrollY.value ? '' : 'none'\n    }\n  }\n\n  return {\n    tableLayout: tableLayout.value,\n    onColumnsChange,\n    onScrollableChange,\n  }\n}\n\nexport default useLayoutObserver\n"], "mappings": ";;;AAQA,SAASA,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrCC,aAAa,CAAC,MAAM;IAClBC,WAAW,CAACC,KAAK,CAACC,WAAW,CAACL,QAAQ,CAAC;EAC3C,CAAG,CAAC;EACFM,SAAS,CAAC,MAAM;IACdC,eAAe,CAACJ,WAAW,CAACC,KAAK,CAAC;IAClCI,kBAAkB,CAACL,WAAW,CAACC,KAAK,CAAC;EACzC,CAAG,CAAC;EACFK,SAAS,CAAC,MAAM;IACdF,eAAe,CAACJ,WAAW,CAACC,KAAK,CAAC;IAClCI,kBAAkB,CAACL,WAAW,CAACC,KAAK,CAAC;EACzC,CAAG,CAAC;EACFM,WAAW,CAAC,MAAM;IAChBP,WAAW,CAACC,KAAK,CAACO,cAAc,CAACX,QAAQ,CAAC;EAC9C,CAAG,CAAC;EACF,MAAMG,WAAW,GAAGS,QAAQ,CAAC,MAAM;IACjC,MAAMC,MAAM,GAAGd,IAAI,CAACc,MAAM;IAC1B,IAAI,CAACA,MAAM,EAAE;MACX,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;IACnD;IACI,OAAOD,MAAM;EACjB,CAAG,CAAC;EACF,MAAMN,eAAe,GAAIM,MAAM,IAAK;IAClC,IAAIE,EAAE;IACN,MAAMC,IAAI,GAAG,CAAC,CAACD,EAAE,GAAGhB,IAAI,CAACkB,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACI,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,EAAE;IAClG,IAAI,CAACH,IAAI,CAACI,MAAM,EACd;IACF,MAAMC,cAAc,GAAGR,MAAM,CAACS,iBAAiB,EAAE;IACjD,MAAMC,UAAU,GAAG,EAAE;IACrBF,cAAc,CAACG,OAAO,CAAEC,MAAM,IAAK;MACjCF,UAAU,CAACE,MAAM,CAACC,EAAE,CAAC,GAAGD,MAAM;IACpC,CAAK,CAAC;IACF,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGZ,IAAI,CAACI,MAAM,EAAEO,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC3C,MAAME,GAAG,GAAGb,IAAI,CAACW,CAAC,CAAC;MACnB,MAAMG,IAAI,GAAGD,GAAG,CAACE,YAAY,CAAC,MAAM,CAAC;MACrC,MAAMN,MAAM,GAAGF,UAAU,CAACO,IAAI,CAAC;MAC/B,IAAIL,MAAM,EAAE;QACVI,GAAG,CAACG,YAAY,CAAC,OAAO,EAAEP,MAAM,CAACQ,SAAS,IAAIR,MAAM,CAACS,KAAK,CAAC;MACnE;IACA;EACA,CAAG;EACD,MAAM1B,kBAAkB,GAAIK,MAAM,IAAK;IACrC,IAAIE,EAAE,EAAEoB,EAAE;IACV,MAAMnB,IAAI,GAAG,CAAC,CAACD,EAAE,GAAGhB,IAAI,CAACkB,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,EAAE,CAACI,gBAAgB,CAAC,6BAA6B,CAAC,KAAK,EAAE;IAC/G,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGZ,IAAI,CAACI,MAAM,EAAEO,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC3C,MAAME,GAAG,GAAGb,IAAI,CAACW,CAAC,CAAC;MACnBE,GAAG,CAACG,YAAY,CAAC,OAAO,EAAEnB,MAAM,CAACuB,OAAO,CAAChC,KAAK,GAAGS,MAAM,CAACwB,WAAW,GAAG,GAAG,CAAC;IAChF;IACI,MAAMC,GAAG,GAAG,CAAC,CAACH,EAAE,GAAGpC,IAAI,CAACkB,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiB,EAAE,CAAChB,gBAAgB,CAAC,WAAW,CAAC,KAAK,EAAE;IAC5F,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGU,GAAG,CAAClB,MAAM,EAAEO,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC1C,MAAMY,EAAE,GAAGD,GAAG,CAACX,CAAC,CAAC;MACjBY,EAAE,CAACC,KAAK,CAACN,KAAK,GAAGrB,MAAM,CAACuB,OAAO,CAAChC,KAAK,GAAG,GAAGS,MAAM,CAACwB,WAAW,IAAI,GAAG,GAAG;MACvEE,EAAE,CAACC,KAAK,CAACC,OAAO,GAAG5B,MAAM,CAACuB,OAAO,CAAChC,KAAK,GAAG,EAAE,GAAG,MAAM;IAC3D;EACA,CAAG;EACD,OAAO;IACLD,WAAW,EAAEA,WAAW,CAACC,KAAK;IAC9BG,eAAe;IACfC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}