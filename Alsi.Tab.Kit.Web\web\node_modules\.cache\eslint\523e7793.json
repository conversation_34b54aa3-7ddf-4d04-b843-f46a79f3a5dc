[{"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts": "1", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts": "2", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts": "3", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue": "4", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts": "5", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts": "6", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue": "7", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue": "8", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogViewerView.vue": "9", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue": "10"}, {"size": 3949, "mtime": 1750310353373, "results": "11", "hashOfConfig": "12"}, {"size": 2910, "mtime": 1750232224429, "results": "13", "hashOfConfig": "12"}, {"size": 4116, "mtime": 1750239708438, "results": "14", "hashOfConfig": "12"}, {"size": 4258, "mtime": 1750311950856, "results": "15", "hashOfConfig": "12"}, {"size": 145, "mtime": 1750231265797, "results": "16", "hashOfConfig": "12"}, {"size": 827, "mtime": 1750309910813, "results": "17", "hashOfConfig": "12"}, {"size": 3904, "mtime": 1750311880761, "results": "18", "hashOfConfig": "12"}, {"size": 6401, "mtime": 1750312031368, "results": "19", "hashOfConfig": "12"}, {"size": 16953, "mtime": 1750312368774, "results": "20", "hashOfConfig": "12"}, {"size": 18433, "mtime": 1750312401184, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "abycao", {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\main.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\utils\\errorHandler.ts", ["42", "43", "44"], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\api\\appApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\App.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\store\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\router\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\HomeView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\AboutView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogViewerView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\web\\src\\views\\LogConverterView.vue", ["45"], {"ruleId": "46", "severity": 1, "message": "47", "line": 5, "column": 36, "nodeType": "48", "messageId": "49", "endLine": 5, "endColumn": 39, "suggestions": "50"}, {"ruleId": "46", "severity": 1, "message": "47", "line": 37, "column": 35, "nodeType": "48", "messageId": "49", "endLine": 37, "endColumn": 38, "suggestions": "51"}, {"ruleId": "46", "severity": 1, "message": "47", "line": 61, "column": 32, "nodeType": "48", "messageId": "49", "endLine": 61, "endColumn": 35, "suggestions": "52"}, {"ruleId": "53", "severity": 1, "message": "54", "line": 358, "column": 68, "nodeType": "55", "messageId": "56", "endLine": 358, "endColumn": 88, "suggestions": "57"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["58", "59"], ["60", "61"], ["62", "63"], "@typescript-eslint/no-non-null-assertion", "Forbidden non-null assertion.", "TSNonNullExpression", "noNonNull", ["64"], {"messageId": "65", "fix": "66", "desc": "67"}, {"messageId": "68", "fix": "69", "desc": "70"}, {"messageId": "65", "fix": "71", "desc": "67"}, {"messageId": "68", "fix": "72", "desc": "70"}, {"messageId": "65", "fix": "73", "desc": "67"}, {"messageId": "68", "fix": "74", "desc": "70"}, {"messageId": "75", "fix": "76", "desc": "77"}, "suggestUnknown", {"range": "78", "text": "79"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "78", "text": "80"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "81", "text": "79"}, {"range": "81", "text": "80"}, {"range": "82", "text": "79"}, {"range": "82", "text": "80"}, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", {"range": "83", "text": "84"}, "Consider using the optional chain operator `?.` instead. This operator includes runtime checks, so it is safer than the compile-only non-null assertion operator.", [140, 143], "unknown", "never", [931, 934], [1479, 1482], [10761, 10762], "?"]