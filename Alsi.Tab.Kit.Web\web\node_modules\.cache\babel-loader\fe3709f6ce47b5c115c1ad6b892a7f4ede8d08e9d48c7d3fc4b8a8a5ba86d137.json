{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { getCurrentInstance, inject, ref, unref, watch } from 'vue';\nimport dayjs from 'dayjs';\nimport { isValidRange, getDefaultValue } from '../utils.mjs';\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants.mjs';\nimport { useShortcut } from './use-shortcut.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useLocale } from '../../../../hooks/use-locale/index.mjs';\nimport { isArray } from '@vue/shared';\nconst useRangePicker = (props, {\n  defaultValue,\n  defaultTime,\n  leftDate,\n  rightDate,\n  step,\n  unit,\n  onParsedValueChanged\n}) => {\n  const {\n    emit\n  } = getCurrentInstance();\n  const {\n    pickerNs\n  } = inject(ROOT_PICKER_INJECTION_KEY);\n  const drpNs = useNamespace(\"date-range-picker\");\n  const {\n    t,\n    lang\n  } = useLocale();\n  const handleShortcutClick = useShortcut(lang);\n  const minDate = ref();\n  const maxDate = ref();\n  const rangeState = ref({\n    endDate: null,\n    selecting: false\n  });\n  const handleChangeRange = val => {\n    rangeState.value = val;\n  };\n  const handleRangeConfirm = (visible = false) => {\n    const _minDate = unref(minDate);\n    const _maxDate = unref(maxDate);\n    if (isValidRange([_minDate, _maxDate])) {\n      emit(\"pick\", [_minDate, _maxDate], visible);\n    }\n  };\n  const onSelect = selecting => {\n    rangeState.value.selecting = selecting;\n    if (!selecting) {\n      rangeState.value.endDate = null;\n    }\n  };\n  const onReset = parsedValue => {\n    if (isArray(parsedValue) && parsedValue.length === 2) {\n      const [start, end] = parsedValue;\n      minDate.value = start;\n      leftDate.value = start;\n      maxDate.value = end;\n      onParsedValueChanged(unref(minDate), unref(maxDate));\n    } else {\n      restoreDefault();\n    }\n  };\n  const restoreDefault = () => {\n    let [start, end] = getDefaultValue(unref(defaultValue), {\n      lang: unref(lang),\n      step,\n      unit,\n      unlinkPanels: props.unlinkPanels\n    });\n    const getShift = day => {\n      return day.diff(day.startOf(\"d\"), \"ms\");\n    };\n    const maybeTimes = unref(defaultTime);\n    if (maybeTimes) {\n      let leftShift = 0;\n      let rightShift = 0;\n      if (isArray(maybeTimes)) {\n        const [timeStart, timeEnd] = maybeTimes.map(dayjs);\n        leftShift = getShift(timeStart);\n        rightShift = getShift(timeEnd);\n      } else {\n        const shift = getShift(dayjs(maybeTimes));\n        leftShift = shift;\n        rightShift = shift;\n      }\n      start = start.startOf(\"d\").add(leftShift, \"ms\");\n      end = end.startOf(\"d\").add(rightShift, \"ms\");\n    }\n    minDate.value = void 0;\n    maxDate.value = void 0;\n    leftDate.value = start;\n    rightDate.value = end;\n  };\n  watch(defaultValue, val => {\n    if (val) {\n      restoreDefault();\n    }\n  }, {\n    immediate: true\n  });\n  watch(() => props.parsedValue, onReset, {\n    immediate: true\n  });\n  return {\n    minDate,\n    maxDate,\n    rangeState,\n    lang,\n    ppNs: pickerNs,\n    drpNs,\n    handleChangeRange,\n    handleRangeConfirm,\n    handleShortcutClick,\n    onSelect,\n    onReset,\n    t\n  };\n};\nexport { useRangePicker };", "map": {"version": 3, "names": ["useRangePicker", "props", "defaultValue", "defaultTime", "leftDate", "rightDate", "step", "unit", "onParsedValueChanged", "emit", "getCurrentInstance", "pickerNs", "inject", "ROOT_PICKER_INJECTION_KEY", "drpNs", "useNamespace", "t", "lang", "useLocale", "handleShortcutClick", "useShortcut", "minDate", "ref", "maxDate", "rangeState", "endDate", "selecting", "handleChangeRange", "val", "value", "handleRangeConfirm", "visible", "_minDate", "unref", "_maxDate", "isValidRange", "onSelect", "onReset", "parsedValue", "isArray", "length", "start", "end", "restoreDefault", "getDefaultValue", "unlinkPanels", "getShift", "day", "diff", "startOf", "maybeTimes", "leftShift", "rightShift", "timeStart", "timeEnd", "map", "dayjs", "shift", "add", "watch", "immediate", "ppNs"], "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-range-picker.ts"], "sourcesContent": ["import { getCurrentInstance, inject, ref, unref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { isArray } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { getDefaultValue, isValidRange } from '../utils'\nimport { ROOT_PICKER_INJECTION_KEY } from '../constants'\nimport { useShortcut } from './use-shortcut'\n\nimport type { Ref } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { PanelRangeSharedProps, RangeState } from '../props/shared'\nimport type { DefaultValue } from '../utils'\n\ntype UseRangePickerProps = {\n  onParsedValueChanged: (\n    minDate: Dayjs | undefined,\n    maxDate: Dayjs | undefined\n  ) => void\n  defaultValue: Ref<DefaultValue>\n  defaultTime?: Ref<DefaultValue>\n  leftDate: Ref<Dayjs>\n  rightDate: Ref<Dayjs>\n  step?: number\n  unit: 'month' | 'year'\n}\n\nexport const useRangePicker = (\n  props: PanelRangeSharedProps,\n  {\n    defaultValue,\n    defaultTime,\n    leftDate,\n    rightDate,\n    step,\n    unit,\n\n    onParsedValueChanged,\n  }: UseRangePickerProps\n) => {\n  const { emit } = getCurrentInstance()!\n\n  const { pickerNs } = inject(ROOT_PICKER_INJECTION_KEY)!\n  const drpNs = useNamespace('date-range-picker')\n  const { t, lang } = useLocale()\n  const handleShortcutClick = useShortcut(lang)\n  const minDate = ref<Dayjs>()\n  const maxDate = ref<Dayjs>()\n  const rangeState = ref<RangeState>({\n    endDate: null,\n    selecting: false,\n  })\n\n  const handleChangeRange = (val: RangeState) => {\n    rangeState.value = val\n  }\n\n  const handleRangeConfirm = (visible = false) => {\n    const _minDate = unref(minDate)\n    const _maxDate = unref(maxDate)\n\n    if (isValidRange([_minDate, _maxDate])) {\n      emit('pick', [_minDate, _maxDate], visible)\n    }\n  }\n\n  const onSelect = (selecting: boolean) => {\n    rangeState.value.selecting = selecting\n    if (!selecting) {\n      rangeState.value.endDate = null\n    }\n  }\n\n  const onReset = (parsedValue: PanelRangeSharedProps['parsedValue']) => {\n    if (isArray(parsedValue) && parsedValue.length === 2) {\n      const [start, end] = parsedValue\n      minDate.value = start\n      leftDate.value = start\n      maxDate.value = end\n      onParsedValueChanged(unref(minDate), unref(maxDate))\n    } else {\n      restoreDefault()\n    }\n  }\n\n  const restoreDefault = () => {\n    let [start, end] = getDefaultValue(unref(defaultValue), {\n      lang: unref(lang),\n      step,\n      unit,\n      unlinkPanels: props.unlinkPanels,\n    })\n    const getShift = (day: Dayjs) => {\n      return day.diff(day.startOf('d'), 'ms')\n    }\n    const maybeTimes = unref(defaultTime)\n    if (maybeTimes) {\n      let leftShift = 0\n      let rightShift = 0\n      if (isArray(maybeTimes)) {\n        const [timeStart, timeEnd] = maybeTimes.map(dayjs)\n        leftShift = getShift(timeStart)\n        rightShift = getShift(timeEnd)\n      } else {\n        const shift = getShift(dayjs(maybeTimes))\n        leftShift = shift\n        rightShift = shift\n      }\n      start = start.startOf('d').add(leftShift, 'ms')\n      end = end.startOf('d').add(rightShift, 'ms')\n    }\n\n    minDate.value = undefined\n    maxDate.value = undefined\n    leftDate.value = start\n    rightDate.value = end\n  }\n\n  watch(\n    defaultValue,\n    (val) => {\n      if (val) {\n        restoreDefault()\n      }\n    },\n    { immediate: true }\n  )\n\n  watch(() => props.parsedValue, onReset, { immediate: true })\n\n  return {\n    minDate,\n    maxDate,\n    rangeState,\n    lang,\n    ppNs: pickerNs,\n    drpNs,\n\n    handleChangeRange,\n    handleRangeConfirm,\n    handleShortcutClick,\n    onSelect,\n    onReset,\n    t,\n  }\n}\n"], "mappings": ";;;;;;;;;;AAOY,MAACA,cAAc,GAAGA,CAACC,KAAK,EAAE;EACpCC,YAAY;EACZC,WAAW;EACXC,QAAQ;EACRC,SAAS;EACTC,IAAI;EACJC,IAAI;EACJC;AACF,CAAC,KAAK;EACJ,MAAM;IAAEC;EAAI,CAAE,GAAGC,kBAAkB,EAAE;EACrC,MAAM;IAAEC;EAAQ,CAAE,GAAGC,MAAM,CAACC,yBAAyB,CAAC;EACtD,MAAMC,KAAK,GAAGC,YAAY,CAAC,mBAAmB,CAAC;EAC/C,MAAM;IAAEC,CAAC;IAAEC;EAAI,CAAE,GAAGC,SAAS,EAAE;EAC/B,MAAMC,mBAAmB,GAAGC,WAAW,CAACH,IAAI,CAAC;EAC7C,MAAMI,OAAO,GAAGC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGD,GAAG,EAAE;EACrB,MAAME,UAAU,GAAGF,GAAG,CAAC;IACrBG,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE;EACf,CAAG,CAAC;EACF,MAAMC,iBAAiB,GAAIC,GAAG,IAAK;IACjCJ,UAAU,CAACK,KAAK,GAAGD,GAAG;EAC1B,CAAG;EACD,MAAME,kBAAkB,GAAGA,CAACC,OAAO,GAAG,KAAK,KAAK;IAC9C,MAAMC,QAAQ,GAAGC,KAAK,CAACZ,OAAO,CAAC;IAC/B,MAAMa,QAAQ,GAAGD,KAAK,CAACV,OAAO,CAAC;IAC/B,IAAIY,YAAY,CAAC,CAACH,QAAQ,EAAEE,QAAQ,CAAC,CAAC,EAAE;MACtCzB,IAAI,CAAC,MAAM,EAAE,CAACuB,QAAQ,EAAEE,QAAQ,CAAC,EAAEH,OAAO,CAAC;IACjD;EACA,CAAG;EACD,MAAMK,QAAQ,GAAIV,SAAS,IAAK;IAC9BF,UAAU,CAACK,KAAK,CAACH,SAAS,GAAGA,SAAS;IACtC,IAAI,CAACA,SAAS,EAAE;MACdF,UAAU,CAACK,KAAK,CAACJ,OAAO,GAAG,IAAI;IACrC;EACA,CAAG;EACD,MAAMY,OAAO,GAAIC,WAAW,IAAK;IAC/B,IAAIC,OAAO,CAACD,WAAW,CAAC,IAAIA,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;MACpD,MAAM,CAACC,KAAK,EAAEC,GAAG,CAAC,GAAGJ,WAAW;MAChCjB,OAAO,CAACQ,KAAK,GAAGY,KAAK;MACrBrC,QAAQ,CAACyB,KAAK,GAAGY,KAAK;MACtBlB,OAAO,CAACM,KAAK,GAAGa,GAAG;MACnBlC,oBAAoB,CAACyB,KAAK,CAACZ,OAAO,CAAC,EAAEY,KAAK,CAACV,OAAO,CAAC,CAAC;IAC1D,CAAK,MAAM;MACLoB,cAAc,EAAE;IACtB;EACA,CAAG;EACD,MAAMA,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACF,KAAK,EAAEC,GAAG,CAAC,GAAGE,eAAe,CAACX,KAAK,CAAC/B,YAAY,CAAC,EAAE;MACtDe,IAAI,EAAEgB,KAAK,CAAChB,IAAI,CAAC;MACjBX,IAAI;MACJC,IAAI;MACJsC,YAAY,EAAE5C,KAAK,CAAC4C;IAC1B,CAAK,CAAC;IACF,MAAMC,QAAQ,GAAIC,GAAG,IAAK;MACxB,OAAOA,GAAG,CAACC,IAAI,CAACD,GAAG,CAACE,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;IAC7C,CAAK;IACD,MAAMC,UAAU,GAAGjB,KAAK,CAAC9B,WAAW,CAAC;IACrC,IAAI+C,UAAU,EAAE;MACd,IAAIC,SAAS,GAAG,CAAC;MACjB,IAAIC,UAAU,GAAG,CAAC;MAClB,IAAIb,OAAO,CAACW,UAAU,CAAC,EAAE;QACvB,MAAM,CAACG,SAAS,EAAEC,OAAO,CAAC,GAAGJ,UAAU,CAACK,GAAG,CAACC,KAAK,CAAC;QAClDL,SAAS,GAAGL,QAAQ,CAACO,SAAS,CAAC;QAC/BD,UAAU,GAAGN,QAAQ,CAACQ,OAAO,CAAC;MACtC,CAAO,MAAM;QACL,MAAMG,KAAK,GAAGX,QAAQ,CAACU,KAAK,CAACN,UAAU,CAAC,CAAC;QACzCC,SAAS,GAAGM,KAAK;QACjBL,UAAU,GAAGK,KAAK;MAC1B;MACMhB,KAAK,GAAGA,KAAK,CAACQ,OAAO,CAAC,GAAG,CAAC,CAACS,GAAG,CAACP,SAAS,EAAE,IAAI,CAAC;MAC/CT,GAAG,GAAGA,GAAG,CAACO,OAAO,CAAC,GAAG,CAAC,CAACS,GAAG,CAACN,UAAU,EAAE,IAAI,CAAC;IAClD;IACI/B,OAAO,CAACQ,KAAK,GAAG,KAAK,CAAC;IACtBN,OAAO,CAACM,KAAK,GAAG,KAAK,CAAC;IACtBzB,QAAQ,CAACyB,KAAK,GAAGY,KAAK;IACtBpC,SAAS,CAACwB,KAAK,GAAGa,GAAG;EACzB,CAAG;EACDiB,KAAK,CAACzD,YAAY,EAAG0B,GAAG,IAAK;IAC3B,IAAIA,GAAG,EAAE;MACPe,cAAc,EAAE;IACtB;EACA,CAAG,EAAE;IAAEiB,SAAS,EAAE;EAAI,CAAE,CAAC;EACvBD,KAAK,CAAC,MAAM1D,KAAK,CAACqC,WAAW,EAAED,OAAO,EAAE;IAAEuB,SAAS,EAAE;EAAI,CAAE,CAAC;EAC5D,OAAO;IACLvC,OAAO;IACPE,OAAO;IACPC,UAAU;IACVP,IAAI;IACJ4C,IAAI,EAAElD,QAAQ;IACdG,KAAK;IACLa,iBAAiB;IACjBG,kBAAkB;IAClBX,mBAAmB;IACnBiB,QAAQ;IACRC,OAAO;IACPrB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}