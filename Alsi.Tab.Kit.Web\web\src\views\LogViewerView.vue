<template>
  <div class="log-viewer">
    <!-- 标题栏 -->
    <div class="title-bar">
      <h1>Log 查看工具</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="viewer-content">
      <!-- 顶部工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <!-- 文件选择 -->
          <el-input 
            v-model="currentFilePath" 
            placeholder="请输入日志文件路径"
            style="width: 300px;"
            @keyup.enter="loadFile"
          >
            <template #append>
              <el-button @click="selectFile">
                <font-awesome-icon icon="folder-open" />
              </el-button>
            </template>
          </el-input>
          
          <el-button type="primary" @click="loadFile" :loading="isLoading">
            <font-awesome-icon icon="file-alt" />
            加载文件
          </el-button>
        </div>

        <div class="toolbar-right">
          <!-- 搜索框 -->
          <el-input 
            v-model="searchQuery" 
            placeholder="搜索日志内容..."
            style="width: 250px;"
            clearable
            @input="onSearchInput"
          >
            <template #prefix>
              <font-awesome-icon icon="search" />
            </template>
          </el-input>

          <!-- 过滤选项 -->
          <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px;" @change="applyFilters">
            <el-option label="全部" value="" />
            <el-option label="ERROR" value="ERROR" />
            <el-option label="WARN" value="WARN" />
            <el-option label="INFO" value="INFO" />
            <el-option label="DEBUG" value="DEBUG" />
          </el-select>

          <!-- 工具按钮 -->
          <el-button @click="exportLogs" :disabled="!logData.length">
            <font-awesome-icon icon="download" />
            导出
          </el-button>
          
          <el-button @click="clearLogs">
            <font-awesome-icon icon="trash" />
            清空
          </el-button>
        </div>
      </div>

      <!-- 文件信息栏 -->
      <div v-if="fileInfo" class="file-info-bar">
        <div class="info-item">
          <span class="label">文件:</span>
          <span class="value">{{ fileInfo.fileName }}</span>
        </div>
        <div class="info-item">
          <span class="label">大小:</span>
          <span class="value">{{ formatFileSize(fileInfo.fileSize) }}</span>
        </div>
        <div class="info-item">
          <span class="label">行数:</span>
          <span class="value">{{ fileInfo.totalLines }}</span>
        </div>
        <div class="info-item">
          <span class="label">显示:</span>
          <span class="value">{{ filteredLogs.length }} / {{ logData.length }}</span>
        </div>
      </div>

      <!-- 日志内容区域 -->
      <div class="log-content">
        <div v-if="!logData.length && !isLoading" class="empty-state">
          <font-awesome-icon icon="file-alt" class="empty-icon" />
          <h3>暂无日志数据</h3>
          <p>请选择并加载一个日志文件</p>
        </div>

        <div v-if="isLoading" class="loading-state">
          <el-loading-spinner />
          <p>正在加载日志文件...</p>
        </div>

        <!-- 虚拟滚动日志列表 -->
        <div v-if="filteredLogs.length" class="log-list" ref="logListRef">
          <div
            v-for="log in visibleLogs"
            :key="log.id"
            class="log-entry"
            :class="getLogLevelClass(log.level)"
          >
            <div class="log-line-number">{{ log.lineNumber }}</div>
            <div class="log-timestamp">{{ log.timestamp }}</div>
            <div class="log-level">{{ log.level }}</div>
            <div class="log-message" v-html="highlightSearchTerm(log.message)"></div>
          </div>
        </div>

        <!-- 分页控制 -->
        <div v-if="filteredLogs.length > pageSize" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="filteredLogs.length"
            layout="total, prev, pager, next, jumper"
            @current-change="onPageChange"
          />
        </div>
      </div>
    </div>

    <!-- 统计面板 -->
    <div v-if="logData.length" class="stats-panel">
      <el-card>
        <template #header>
          <div class="card-header">
            <font-awesome-icon icon="chart-bar" />
            <span>日志统计</span>
          </div>
        </template>
        
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ logStats.total }}</div>
            <div class="stat-label">总计</div>
          </div>
          <div class="stat-item error">
            <div class="stat-value">{{ logStats.error }}</div>
            <div class="stat-label">错误</div>
          </div>
          <div class="stat-item warning">
            <div class="stat-value">{{ logStats.warning }}</div>
            <div class="stat-label">警告</div>
          </div>
          <div class="stat-item info">
            <div class="stat-value">{{ logStats.info }}</div>
            <div class="stat-label">信息</div>
          </div>
          <div class="stat-item debug">
            <div class="stat-value">{{ logStats.debug }}</div>
            <div class="stat-label">调试</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <span v-if="fileInfo">
        文件: {{ fileInfo.fileName }} |
        大小: {{ formatFileSize(fileInfo.fileSize) }} |
        总行数: {{ fileInfo.totalLines }}
      </span>
      <span v-if="!fileInfo">请选择 Log 文件</span>
      <span v-if="isLoading" class="loading-status">
        加载中...
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from "vue";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

interface LogEntry {
  id: number;
  lineNumber: number;
  timestamp: string;
  level: string;
  message: string;
}

interface FileInfo {
  fileName: string;
  fileSize: number;
  totalLines: number;
}

interface LogStats {
  total: number;
  error: number;
  warning: number;
  info: number;
  debug: number;
}

export default defineComponent({
  name: "LogViewerView",
  components: {
    FontAwesomeIcon,
  },
  setup() {
    // 响应式数据
    const currentFilePath = ref('');
    const logData = ref<LogEntry[]>([]);
    const searchQuery = ref('');
    const logLevel = ref('');
    const isLoading = ref(false);
    const fileInfo = ref<FileInfo | null>(null);
    const currentPage = ref(1);
    const pageSize = ref(100);
    const logListRef = ref<HTMLElement>();

    // 计算属性
    const filteredLogs = computed(() => {
      let filtered = logData.value;

      // 按日志级别过滤
      if (logLevel.value) {
        filtered = filtered.filter(log => log.level === logLevel.value);
      }

      // 按搜索关键词过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(log => 
          log.message.toLowerCase().includes(query) ||
          log.timestamp.toLowerCase().includes(query)
        );
      }

      return filtered;
    });

    const visibleLogs = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredLogs.value.slice(start, end);
    });

    const logStats = computed((): LogStats => {
      const stats = {
        total: logData.value.length,
        error: 0,
        warning: 0,
        info: 0,
        debug: 0
      };

      logData.value.forEach(log => {
        switch (log.level.toUpperCase()) {
          case 'ERROR':
            stats.error++;
            break;
          case 'WARN':
          case 'WARNING':
            stats.warning++;
            break;
          case 'INFO':
            stats.info++;
            break;
          case 'DEBUG':
            stats.debug++;
            break;
        }
      });

      return stats;
    });

    // 方法
    const selectFile = () => {
      // TODO: 实现文件选择对话框
      console.log('选择文件');
    };

    const loadFile = async () => {
      if (!currentFilePath.value) return;

      isLoading.value = true;
      try {
        // TODO: 实现文件加载逻辑
        // 这里应该调用后端API来加载文件
        await simulateFileLoad();
      } catch (error) {
        console.error('加载文件失败:', error);
      } finally {
        isLoading.value = false;
      }
    };

    const simulateFileLoad = async () => {
      // 模拟文件加载
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockLogs: LogEntry[] = [];
      for (let i = 1; i <= 500; i++) {
        const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
        const level = levels[Math.floor(Math.random() * levels.length)];
        const timestamp = new Date(Date.now() - Math.random() * 86400000).toISOString();
        
        mockLogs.push({
          id: i,
          lineNumber: i,
          timestamp,
          level,
          message: `这是第${i}行的日志消息，级别为${level}。包含一些示例内容用于测试搜索和过滤功能。`
        });
      }

      logData.value = mockLogs;
      fileInfo.value = {
        fileName: currentFilePath.value.split('/').pop() || 'unknown.log',
        fileSize: mockLogs.length * 100, // 模拟文件大小
        totalLines: mockLogs.length
      };
    };

    const onSearchInput = () => {
      currentPage.value = 1; // 重置到第一页
    };

    const applyFilters = () => {
      currentPage.value = 1; // 重置到第一页
    };

    const onPageChange = (page: number) => {
      currentPage.value = page;
      // 滚动到顶部
      if (logListRef.value) {
        logListRef.value.scrollTop = 0;
      }
    };

    const exportLogs = () => {
      // TODO: 实现日志导出功能
      console.log('导出日志');
    };

    const clearLogs = () => {
      logData.value = [];
      fileInfo.value = null;
      currentPage.value = 1;
    };

    const getLogLevelClass = (level: string): string => {
      switch (level.toUpperCase()) {
        case 'ERROR':
          return 'log-error';
        case 'WARN':
        case 'WARNING':
          return 'log-warning';
        case 'INFO':
          return 'log-info';
        case 'DEBUG':
          return 'log-debug';
        default:
          return '';
      }
    };

    const highlightSearchTerm = (text: string): string => {
      if (!searchQuery.value) return text;
      
      const regex = new RegExp(`(${searchQuery.value})`, 'gi');
      return text.replace(regex, '<mark>$1</mark>');
    };

    const formatFileSize = (bytes: number): string => {
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      if (bytes === 0) return '0 Bytes';
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    // 监听搜索和过滤变化
    watch([searchQuery, logLevel], () => {
      currentPage.value = 1;
    });

    return {
      currentFilePath,
      logData,
      searchQuery,
      logLevel,
      isLoading,
      fileInfo,
      currentPage,
      pageSize,
      logListRef,
      filteredLogs,
      visibleLogs,
      logStats,
      selectFile,
      loadFile,
      onSearchInput,
      applyFilters,
      onPageChange,
      exportLogs,
      clearLogs,
      getLogLevelClass,
      highlightSearchTerm,
      formatFileSize
    };
  },
});
</script>

<style scoped>
.log-viewer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.title-bar {
  background-color: var(--el-color-primary);
  color: white;
  padding: 15px 20px;
  text-align: center;
}

.title-bar h1 {
  font-size: 1.5rem;
  margin: 0;
}

.viewer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-info-bar {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 10px 15px;
  background-color: #e9ecef;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.info-item .label {
  color: #6c757d;
  font-weight: 500;
}

.info-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.log-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.empty-state,
.loading-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: #495057;
}

.log-list {
  flex: 1;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  padding: 4px 8px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
}

.log-entry:hover {
  background-color: #f8f9fa;
}

.log-entry.log-error {
  background-color: #fff5f5;
  border-left: 3px solid #dc3545;
}

.log-entry.log-warning {
  background-color: #fffbf0;
  border-left: 3px solid #ffc107;
}

.log-entry.log-info {
  background-color: #f0f8ff;
  border-left: 3px solid #17a2b8;
}

.log-entry.log-debug {
  background-color: #f8f9fa;
  border-left: 3px solid #6c757d;
}

.log-line-number {
  width: 60px;
  color: #6c757d;
  text-align: right;
  margin-right: 10px;
  flex-shrink: 0;
}

.log-timestamp {
  width: 180px;
  color: #495057;
  margin-right: 10px;
  flex-shrink: 0;
}

.log-level {
  width: 60px;
  font-weight: bold;
  margin-right: 10px;
  flex-shrink: 0;
}

.log-entry.log-error .log-level {
  color: #dc3545;
}

.log-entry.log-warning .log-level {
  color: #ffc107;
}

.log-entry.log-info .log-level {
  color: #17a2b8;
}

.log-entry.log-debug .log-level {
  color: #6c757d;
}

.log-message {
  flex: 1;
  word-break: break-word;
  white-space: pre-wrap;
}

.log-message :deep(mark) {
  background-color: #ffeb3b;
  padding: 1px 2px;
  border-radius: 2px;
}

.pagination-wrapper {
  padding: 15px;
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
  display: flex;
  justify-content: center;
}

.stats-panel {
  margin-top: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  border-radius: 6px;
  background-color: #f8f9fa;
}

.stat-item.error {
  background-color: #fff5f5;
  color: #dc3545;
}

.stat-item.warning {
  background-color: #fffbf0;
  color: #ffc107;
}

.stat-item.info {
  background-color: #f0f8ff;
  color: #17a2b8;
}

.stat-item.debug {
  background-color: #f8f9fa;
  color: #6c757d;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

@media (max-width: 1024px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .log-viewer {
    padding: 10px;
  }

  .file-info-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .log-entry {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .log-line-number,
  .log-timestamp,
  .log-level {
    width: auto;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.status-bar {
  background-color: var(--el-fill-color-light);
  padding: 10px 20px;
  border-top: 1px solid var(--el-border-color-base);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: var(--el-text-color-regular);
}

.loading-status {
  color: var(--el-color-primary);
  font-weight: bold;
}

@media (max-width: 1024px) {
  .viewer-content {
    padding: 10px;
  }
}

@media (max-width: 768px) {
  .status-bar {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
