<template>
  <div id="app">
    <!-- 侧边菜单 -->
    <div class="sidebar" :class="{ collapsed: isMenuCollapsed }">
      <!-- 菜单切换按钮 -->
      <div class="menu-toggle" @click="toggleMenu">
        <font-awesome-icon :icon="isMenuCollapsed ? 'chevron-right' : 'chevron-left'" />
      </div>

      <!-- 菜单项 -->
      <div class="menu-items">
        <!-- 主页 -->
        <router-link to="/" class="menu-item" active-class="active">
          <font-awesome-icon icon="home" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">主页</span>
        </router-link>

        <!-- Log转换工具 -->
        <router-link to="/log-converter" class="menu-item" active-class="active">
          <font-awesome-icon icon="exchange-alt" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">Log 转换工具</span>
        </router-link>

        <!-- Log查看工具 -->
        <router-link to="/log-viewer" class="menu-item" active-class="active">
          <font-awesome-icon icon="file-alt" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">Log 查看工具</span>
        </router-link>
      </div>

      <!-- 底部菜单项 -->
      <div class="menu-bottom">
        <!-- 关于 -->
        <router-link to="/about" class="menu-item" active-class="active">
          <font-awesome-icon icon="info-circle" class="menu-icon" />
          <span v-if="!isMenuCollapsed" class="menu-text">关于</span>
        </router-link>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ expanded: isMenuCollapsed }">
      <router-view />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export default defineComponent({
  name: 'App',
  components: {
    FontAwesomeIcon,
  },
  setup() {
    const isMenuCollapsed = ref(false);

    const toggleMenu = () => {
      isMenuCollapsed.value = !isMenuCollapsed.value;
    };

    return {
      isMenuCollapsed,
      toggleMenu,
    };
  },
});
</script>

<style lang="scss">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  display: flex;
  width: 100vw;
}

.sidebar {
  width: 250px;
  background-color: var(--el-color-primary);
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;

  &.collapsed {
    width: 60px;
  }

  .menu-toggle {
    position: absolute;
    top: 10px;
    right: -12px;
    width: 24px;
    height: 24px;
    background-color: var(--el-color-primary-dark-1);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    border: 1px solid var(--el-color-primary-light-1);

    &:hover {
      background-color: var(--el-color-primary-dark-2);
      transform: scale(1.1);
    }
  }

  .menu-items {
    flex: 1;
    padding-top: 20px;
  }

  .menu-bottom {
    padding-bottom: 20px;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--el-color-primary-light-8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;

    &:hover {
      background-color: var(--el-color-primary-dark-1);
      color: white;
    }

    &.active {
      background-color: var(--el-color-info);
      color: white;
      border-left-color: var(--el-color-info-dark-1);
    }

    .menu-icon {
      font-size: 18px;
      width: 20px;
      text-align: center;
    }

    .menu-text {
      margin-left: 15px;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  &.collapsed .menu-item {
    justify-content: center;
    padding: 15px 10px;

    .menu-icon {
      margin: 0;
    }
  }
}

.main-content {
  flex: 1;
  background-color: var(--el-fill-color-base);
  overflow-y: auto;
  transition: margin-left 0.3s ease;

  &.expanded {
    margin-left: 0;
  }
}
</style>
