{"ast": null, "code": "import baseSlice from './_baseSlice.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n  nativeMax = Math.max;\n\n/**\n * Creates an array of elements split into groups the length of `size`.\n * If `array` can't be split evenly, the final chunk will be the remaining\n * elements.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to process.\n * @param {number} [size=1] The length of each chunk\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the new array of chunks.\n * @example\n *\n * _.chunk(['a', 'b', 'c', 'd'], 2);\n * // => [['a', 'b'], ['c', 'd']]\n *\n * _.chunk(['a', 'b', 'c', 'd'], 3);\n * // => [['a', 'b', 'c'], ['d']]\n */\nfunction chunk(array, size, guard) {\n  if (guard ? isIterateeCall(array, size, guard) : size === undefined) {\n    size = 1;\n  } else {\n    size = nativeMax(toInteger(size), 0);\n  }\n  var length = array == null ? 0 : array.length;\n  if (!length || size < 1) {\n    return [];\n  }\n  var index = 0,\n    resIndex = 0,\n    result = Array(nativeCeil(length / size));\n  while (index < length) {\n    result[resIndex++] = baseSlice(array, index, index += size);\n  }\n  return result;\n}\nexport default chunk;", "map": {"version": 3, "names": ["baseSlice", "isIterateeCall", "toInteger", "nativeCeil", "Math", "ceil", "nativeMax", "max", "chunk", "array", "size", "guard", "undefined", "length", "index", "resIndex", "result", "Array"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/chunk.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeMax = Math.max;\n\n/**\n * Creates an array of elements split into groups the length of `size`.\n * If `array` can't be split evenly, the final chunk will be the remaining\n * elements.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The array to process.\n * @param {number} [size=1] The length of each chunk\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the new array of chunks.\n * @example\n *\n * _.chunk(['a', 'b', 'c', 'd'], 2);\n * // => [['a', 'b'], ['c', 'd']]\n *\n * _.chunk(['a', 'b', 'c', 'd'], 3);\n * // => [['a', 'b', 'c'], ['d']]\n */\nfunction chunk(array, size, guard) {\n  if ((guard ? isIterateeCall(array, size, guard) : size === undefined)) {\n    size = 1;\n  } else {\n    size = nativeMax(toInteger(size), 0);\n  }\n  var length = array == null ? 0 : array.length;\n  if (!length || size < 1) {\n    return [];\n  }\n  var index = 0,\n      resIndex = 0,\n      result = Array(nativeCeil(length / size));\n\n  while (index < length) {\n    result[resIndex++] = baseSlice(array, index, (index += size));\n  }\n  return result;\n}\n\nexport default chunk;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,UAAU,GAAGC,IAAI,CAACC,IAAI;EACtBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACjC,IAAKA,KAAK,GAAGV,cAAc,CAACQ,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGD,IAAI,KAAKE,SAAS,EAAG;IACrEF,IAAI,GAAG,CAAC;EACV,CAAC,MAAM;IACLA,IAAI,GAAGJ,SAAS,CAACJ,SAAS,CAACQ,IAAI,CAAC,EAAE,CAAC,CAAC;EACtC;EACA,IAAIG,MAAM,GAAGJ,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACI,MAAM;EAC7C,IAAI,CAACA,MAAM,IAAIH,IAAI,GAAG,CAAC,EAAE;IACvB,OAAO,EAAE;EACX;EACA,IAAII,KAAK,GAAG,CAAC;IACTC,QAAQ,GAAG,CAAC;IACZC,MAAM,GAAGC,KAAK,CAACd,UAAU,CAACU,MAAM,GAAGH,IAAI,CAAC,CAAC;EAE7C,OAAOI,KAAK,GAAGD,MAAM,EAAE;IACrBG,MAAM,CAACD,QAAQ,EAAE,CAAC,GAAGf,SAAS,CAACS,KAAK,EAAEK,KAAK,EAAGA,KAAK,IAAIJ,IAAK,CAAC;EAC/D;EACA,OAAOM,MAAM;AACf;AAEA,eAAeR,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}