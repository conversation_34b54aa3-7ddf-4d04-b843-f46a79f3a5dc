{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport shuffleSelf from './_shuffleSelf.js';\nimport values from './values.js';\n\n/**\n * The base implementation of `_.sampleSize` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to sample.\n * @param {number} n The number of elements to sample.\n * @returns {Array} Returns the random elements.\n */\nfunction baseSampleSize(collection, n) {\n  var array = values(collection);\n  return shuffleSelf(array, baseClamp(n, 0, array.length));\n}\nexport default baseSampleSize;", "map": {"version": 3, "names": ["baseClamp", "shuffleSelf", "values", "baseSampleSize", "collection", "n", "array", "length"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_baseSampleSize.js"], "sourcesContent": ["import baseClamp from './_baseClamp.js';\nimport shuffleSelf from './_shuffleSelf.js';\nimport values from './values.js';\n\n/**\n * The base implementation of `_.sampleSize` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to sample.\n * @param {number} n The number of elements to sample.\n * @returns {Array} Returns the random elements.\n */\nfunction baseSampleSize(collection, n) {\n  var array = values(collection);\n  return shuffleSelf(array, baseClamp(n, 0, array.length));\n}\n\nexport default baseSampleSize;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAEC,CAAC,EAAE;EACrC,IAAIC,KAAK,GAAGJ,MAAM,CAACE,UAAU,CAAC;EAC9B,OAAOH,WAAW,CAACK,KAAK,EAAEN,SAAS,CAACK,CAAC,EAAE,CAAC,EAAEC,KAAK,CAACC,MAAM,CAAC,CAAC;AAC1D;AAEA,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}