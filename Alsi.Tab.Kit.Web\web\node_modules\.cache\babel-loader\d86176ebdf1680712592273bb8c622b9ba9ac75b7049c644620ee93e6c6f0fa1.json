{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function (object, sources) {\n    var index = -1,\n      length = sources.length,\n      customizer = length > 1 ? sources[length - 1] : undefined,\n      guard = length > 2 ? sources[2] : undefined;\n    customizer = assigner.length > 3 && typeof customizer == 'function' ? (length--, customizer) : undefined;\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\nexport default createAssigner;", "map": {"version": 3, "names": ["baseRest", "isIterateeCall", "createAssigner", "assigner", "object", "sources", "index", "length", "customizer", "undefined", "guard", "Object", "source"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/_createAssigner.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\nexport default createAssigner;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAChC,OAAOH,QAAQ,CAAC,UAASI,MAAM,EAAEC,OAAO,EAAE;IACxC,IAAIC,KAAK,GAAG,CAAC,CAAC;MACVC,MAAM,GAAGF,OAAO,CAACE,MAAM;MACvBC,UAAU,GAAGD,MAAM,GAAG,CAAC,GAAGF,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,GAAGE,SAAS;MACzDC,KAAK,GAAGH,MAAM,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAC,CAAC,GAAGI,SAAS;IAE/CD,UAAU,GAAIL,QAAQ,CAACI,MAAM,GAAG,CAAC,IAAI,OAAOC,UAAU,IAAI,UAAU,IAC/DD,MAAM,EAAE,EAAEC,UAAU,IACrBC,SAAS;IAEb,IAAIC,KAAK,IAAIT,cAAc,CAACI,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE;MAC1DF,UAAU,GAAGD,MAAM,GAAG,CAAC,GAAGE,SAAS,GAAGD,UAAU;MAChDD,MAAM,GAAG,CAAC;IACZ;IACAH,MAAM,GAAGO,MAAM,CAACP,MAAM,CAAC;IACvB,OAAO,EAAEE,KAAK,GAAGC,MAAM,EAAE;MACvB,IAAIK,MAAM,GAAGP,OAAO,CAACC,KAAK,CAAC;MAC3B,IAAIM,MAAM,EAAE;QACVT,QAAQ,CAACC,MAAM,EAAEQ,MAAM,EAAEN,KAAK,EAAEE,UAAU,CAAC;MAC7C;IACF;IACA,OAAOJ,MAAM;EACf,CAAC,CAAC;AACJ;AAEA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}