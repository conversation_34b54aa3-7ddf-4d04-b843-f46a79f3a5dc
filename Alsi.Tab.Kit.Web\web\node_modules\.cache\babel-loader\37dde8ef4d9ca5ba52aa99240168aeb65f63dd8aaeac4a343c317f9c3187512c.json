{"ast": null, "code": "import isArray from './isArray.js';\n\n/**\n * Casts `value` as an array if it's not one.\n *\n * @static\n * @memberOf _\n * @since 4.4.0\n * @category Lang\n * @param {*} value The value to inspect.\n * @returns {Array} Returns the cast array.\n * @example\n *\n * _.castArray(1);\n * // => [1]\n *\n * _.castArray({ 'a': 1 });\n * // => [{ 'a': 1 }]\n *\n * _.castArray('abc');\n * // => ['abc']\n *\n * _.castArray(null);\n * // => [null]\n *\n * _.castArray(undefined);\n * // => [undefined]\n *\n * _.castArray();\n * // => []\n *\n * var array = [1, 2, 3];\n * console.log(_.castArray(array) === array);\n * // => true\n */\nfunction castArray() {\n  if (!arguments.length) {\n    return [];\n  }\n  var value = arguments[0];\n  return isArray(value) ? value : [value];\n}\nexport default castArray;", "map": {"version": 3, "names": ["isArray", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "value"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/castArray.js"], "sourcesContent": ["import isArray from './isArray.js';\n\n/**\n * Casts `value` as an array if it's not one.\n *\n * @static\n * @memberOf _\n * @since 4.4.0\n * @category Lang\n * @param {*} value The value to inspect.\n * @returns {Array} Returns the cast array.\n * @example\n *\n * _.castArray(1);\n * // => [1]\n *\n * _.castArray({ 'a': 1 });\n * // => [{ 'a': 1 }]\n *\n * _.castArray('abc');\n * // => ['abc']\n *\n * _.castArray(null);\n * // => [null]\n *\n * _.castArray(undefined);\n * // => [undefined]\n *\n * _.castArray();\n * // => []\n *\n * var array = [1, 2, 3];\n * console.log(_.castArray(array) === array);\n * // => true\n */\nfunction castArray() {\n  if (!arguments.length) {\n    return [];\n  }\n  var value = arguments[0];\n  return isArray(value) ? value : [value];\n}\n\nexport default castArray;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACnB,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE;IACrB,OAAO,EAAE;EACX;EACA,IAAIC,KAAK,GAAGF,SAAS,CAAC,CAAC,CAAC;EACxB,OAAOF,OAAO,CAACI,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACzC;AAEA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}