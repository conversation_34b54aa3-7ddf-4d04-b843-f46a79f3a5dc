{"ast": null, "code": "import { triggerEvent } from '../../../../utils/dom/aria.mjs';\nimport { EVENT_CODE } from '../../../../constants/aria.mjs';\nclass SubMenu {\n  constructor(parent, domNode) {\n    this.parent = parent;\n    this.domNode = domNode;\n    this.subIndex = 0;\n    this.subIndex = 0;\n    this.init();\n  }\n  init() {\n    this.subMenuItems = this.domNode.querySelectorAll(\"li\");\n    this.addListeners();\n  }\n  gotoSubIndex(idx) {\n    if (idx === this.subMenuItems.length) {\n      idx = 0;\n    } else if (idx < 0) {\n      idx = this.subMenuItems.length - 1;\n    }\n    this.subMenuItems[idx].focus();\n    this.subIndex = idx;\n  }\n  addListeners() {\n    const parentNode = this.parent.domNode;\n    Array.prototype.forEach.call(this.subMenuItems, el => {\n      el.addEventListener(\"keydown\", event => {\n        let prevDef = false;\n        switch (event.code) {\n          case EVENT_CODE.down:\n            {\n              this.gotoSubIndex(this.subIndex + 1);\n              prevDef = true;\n              break;\n            }\n          case EVENT_CODE.up:\n            {\n              this.gotoSubIndex(this.subIndex - 1);\n              prevDef = true;\n              break;\n            }\n          case EVENT_CODE.tab:\n            {\n              triggerEvent(parentNode, \"mouseleave\");\n              break;\n            }\n          case EVENT_CODE.enter:\n          case EVENT_CODE.numpadEnter:\n          case EVENT_CODE.space:\n            {\n              prevDef = true;\n              event.currentTarget.click();\n              break;\n            }\n        }\n        if (prevDef) {\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        return false;\n      });\n    });\n  }\n}\nexport { SubMenu as default };", "map": {"version": 3, "names": ["SubMenu", "constructor", "parent", "domNode", "subIndex", "init", "subMenuItems", "querySelectorAll", "addListeners", "gotoSubIndex", "idx", "length", "focus", "parentNode", "Array", "prototype", "for<PERSON>ach", "call", "el", "addEventListener", "event", "prevDef", "code", "EVENT_CODE", "down", "up", "tab", "triggerEvent", "enter", "numpadEnter", "space", "currentTarget", "click", "preventDefault", "stopPropagation"], "sources": ["../../../../../../../packages/components/menu/src/utils/submenu.ts"], "sourcesContent": ["// @ts-nocheck\nimport { triggerEvent } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport type MenuItem from './menu-item'\n\nclass SubMenu {\n  public subMenuItems: NodeList\n  public subIndex = 0\n  constructor(public parent: MenuItem, public domNode: ParentNode) {\n    this.subIndex = 0\n    this.init()\n  }\n\n  init(): void {\n    this.subMenuItems = this.domNode.querySelectorAll('li')\n    this.addListeners()\n  }\n\n  gotoSubIndex(idx: number): void {\n    if (idx === this.subMenuItems.length) {\n      idx = 0\n    } else if (idx < 0) {\n      idx = this.subMenuItems.length - 1\n    }\n    ;(this.subMenuItems[idx] as HTMLElement).focus()\n    this.subIndex = idx\n  }\n\n  addListeners(): void {\n    const parentNode = this.parent.domNode\n    Array.prototype.forEach.call(this.subMenuItems, (el: Element) => {\n      el.addEventListener('keydown', (event: KeyboardEvent) => {\n        let prevDef = false\n        switch (event.code) {\n          case EVENT_CODE.down: {\n            this.gotoSubIndex(this.subIndex + 1)\n            prevDef = true\n            break\n          }\n          case EVENT_CODE.up: {\n            this.gotoSubIndex(this.subIndex - 1)\n            prevDef = true\n            break\n          }\n          case EVENT_CODE.tab: {\n            triggerEvent(parentNode as HTMLElement, 'mouseleave')\n            break\n          }\n          case EVENT_CODE.enter:\n          case EVENT_CODE.numpadEnter:\n          case EVENT_CODE.space: {\n            prevDef = true\n            ;(event.currentTarget as HTMLElement).click()\n            break\n          }\n        }\n        if (prevDef) {\n          event.preventDefault()\n          event.stopPropagation()\n        }\n        return false\n      })\n    })\n  }\n}\n\nexport default SubMenu\n"], "mappings": ";;AAEA,MAAMA,OAAO,CAAC;EACZC,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAE;IAC3B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACA,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,IAAI,EAAE;EACf;EACEA,IAAIA,CAAA,EAAG;IACL,IAAI,CAACC,YAAY,GAAG,IAAI,CAACH,OAAO,CAACI,gBAAgB,CAAC,IAAI,CAAC;IACvD,IAAI,CAACC,YAAY,EAAE;EACvB;EACEC,YAAYA,CAACC,GAAG,EAAE;IAChB,IAAIA,GAAG,KAAK,IAAI,CAACJ,YAAY,CAACK,MAAM,EAAE;MACpCD,GAAG,GAAG,CAAC;IACb,CAAK,MAAM,IAAIA,GAAG,GAAG,CAAC,EAAE;MAClBA,GAAG,GAAG,IAAI,CAACJ,YAAY,CAACK,MAAM,GAAG,CAAC;IACxC;IAEI,IAAI,CAACL,YAAY,CAACI,GAAG,CAAC,CAACE,KAAK,EAAE;IAC9B,IAAI,CAACR,QAAQ,GAAGM,GAAG;EACvB;EACEF,YAAYA,CAAA,EAAG;IACb,MAAMK,UAAU,GAAG,IAAI,CAACX,MAAM,CAACC,OAAO;IACtCW,KAAK,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAACX,YAAY,EAAGY,EAAE,IAAK;MACtDA,EAAE,CAACC,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAK;QACxC,IAAIC,OAAO,GAAG,KAAK;QACnB,QAAQD,KAAK,CAACE,IAAI;UAChB,KAAKC,UAAU,CAACC,IAAI;YAAE;cACpB,IAAI,CAACf,YAAY,CAAC,IAAI,CAACL,QAAQ,GAAG,CAAC,CAAC;cACpCiB,OAAO,GAAG,IAAI;cACd;YACZ;UACU,KAAKE,UAAU,CAACE,EAAE;YAAE;cAClB,IAAI,CAAChB,YAAY,CAAC,IAAI,CAACL,QAAQ,GAAG,CAAC,CAAC;cACpCiB,OAAO,GAAG,IAAI;cACd;YACZ;UACU,KAAKE,UAAU,CAACG,GAAG;YAAE;cACnBC,YAAY,CAACd,UAAU,EAAE,YAAY,CAAC;cACtC;YACZ;UACU,KAAKU,UAAU,CAACK,KAAK;UACrB,KAAKL,UAAU,CAACM,WAAW;UAC3B,KAAKN,UAAU,CAACO,KAAK;YAAE;cACrBT,OAAO,GAAG,IAAI;cACdD,KAAK,CAACW,aAAa,CAACC,KAAK,EAAE;cAC3B;YACZ;QACA;QACQ,IAAIX,OAAO,EAAE;UACXD,KAAK,CAACa,cAAc,EAAE;UACtBb,KAAK,CAACc,eAAe,EAAE;QACjC;QACQ,OAAO,KAAK;MACpB,CAAO,CAAC;IACR,CAAK,CAAC;EACN;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}