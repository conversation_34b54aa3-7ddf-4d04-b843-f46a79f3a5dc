{"ast": null, "code": "import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string`, as space separated words, to upper case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the upper cased string.\n * @example\n *\n * _.upperCase('--foo-bar');\n * // => 'FOO BAR'\n *\n * _.upperCase('fooBar');\n * // => 'FOO BAR'\n *\n * _.upperCase('__foo_bar__');\n * // => 'FOO BAR'\n */\nvar upperCase = createCompounder(function (result, word, index) {\n  return result + (index ? ' ' : '') + word.toUpperCase();\n});\nexport default upperCase;", "map": {"version": 3, "names": ["createCompounder", "upperCase", "result", "word", "index", "toUpperCase"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/lodash-es/upperCase.js"], "sourcesContent": ["import createCompounder from './_createCompounder.js';\n\n/**\n * Converts `string`, as space separated words, to upper case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the upper cased string.\n * @example\n *\n * _.upperCase('--foo-bar');\n * // => 'FOO BAR'\n *\n * _.upperCase('fooBar');\n * // => 'FOO BAR'\n *\n * _.upperCase('__foo_bar__');\n * // => 'FOO BAR'\n */\nvar upperCase = createCompounder(function(result, word, index) {\n  return result + (index ? ' ' : '') + word.toUpperCase();\n});\n\nexport default upperCase;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGD,gBAAgB,CAAC,UAASE,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC7D,OAAOF,MAAM,IAAIE,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}