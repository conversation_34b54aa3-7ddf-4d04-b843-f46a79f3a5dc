{"ast": null, "code": "import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n  var color = new TinyColor(firstColor);\n  var hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n  var secondHex8String = hex8String;\n  var gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n  if (secondColor) {\n    var s = new TinyColor(secondColor);\n    secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n  }\n  return \"progid:DXImageTransform.Microsoft.gradient(\".concat(gradientType, \"startColorstr=\").concat(hex8String, \",endColorstr=\").concat(secondHex8String, \")\");\n}", "map": {"version": 3, "names": ["rgbaToArgbHex", "TinyColor", "to<PERSON><PERSON><PERSON><PERSON>", "firstColor", "secondColor", "color", "hex8String", "r", "g", "b", "a", "secondHex8String", "gradientType", "s", "concat"], "sources": ["D:/src/005_TAB/2、Src/1、Source Code/Alsi.Tab.Kit/Alsi.Tab.Kit.Web/web/node_modules/@ctrl/tinycolor/dist/module/to-ms-filter.js"], "sourcesContent": ["import { rgbaToArgbHex } from './conversion.js';\nimport { TinyColor } from './index.js';\n/**\n * Returns the color represented as a Microsoft filter for use in old versions of IE.\n */\nexport function toMsFilter(firstColor, secondColor) {\n    var color = new TinyColor(firstColor);\n    var hex8String = '#' + rgbaToArgbHex(color.r, color.g, color.b, color.a);\n    var secondHex8String = hex8String;\n    var gradientType = color.gradientType ? 'GradientType = 1, ' : '';\n    if (secondColor) {\n        var s = new TinyColor(secondColor);\n        secondHex8String = '#' + rgbaToArgbHex(s.r, s.g, s.b, s.a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\".concat(gradientType, \"startColorstr=\").concat(hex8String, \",endColorstr=\").concat(secondHex8String, \")\");\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,SAAS,QAAQ,YAAY;AACtC;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,UAAU,EAAEC,WAAW,EAAE;EAChD,IAAIC,KAAK,GAAG,IAAIJ,SAAS,CAACE,UAAU,CAAC;EACrC,IAAIG,UAAU,GAAG,GAAG,GAAGN,aAAa,CAACK,KAAK,CAACE,CAAC,EAAEF,KAAK,CAACG,CAAC,EAAEH,KAAK,CAACI,CAAC,EAAEJ,KAAK,CAACK,CAAC,CAAC;EACxE,IAAIC,gBAAgB,GAAGL,UAAU;EACjC,IAAIM,YAAY,GAAGP,KAAK,CAACO,YAAY,GAAG,oBAAoB,GAAG,EAAE;EACjE,IAAIR,WAAW,EAAE;IACb,IAAIS,CAAC,GAAG,IAAIZ,SAAS,CAACG,WAAW,CAAC;IAClCO,gBAAgB,GAAG,GAAG,GAAGX,aAAa,CAACa,CAAC,CAACN,CAAC,EAAEM,CAAC,CAACL,CAAC,EAAEK,CAAC,CAACJ,CAAC,EAAEI,CAAC,CAACH,CAAC,CAAC;EAC9D;EACA,OAAO,6CAA6C,CAACI,MAAM,CAACF,YAAY,EAAE,gBAAgB,CAAC,CAACE,MAAM,CAACR,UAAU,EAAE,eAAe,CAAC,CAACQ,MAAM,CAACH,gBAAgB,EAAE,GAAG,CAAC;AACjK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}