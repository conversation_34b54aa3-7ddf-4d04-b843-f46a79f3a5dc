<template>
  <div class="log-converter">
    <!-- 标题栏 -->
    <div class="title-bar">
      <h1>Log 转换工具</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="converter-content">
      <!-- 1. 文件选择区域 -->
      <el-card class="section-card">
        <template #header>
          <span>选择 Log 文件</span>
        </template>

        <el-form :model="fileForm" label-width="120px">
          <el-form-item label="源文件路径:">
            <el-input
              v-model="fileForm.filePath"
              placeholder="请输入文件路径或拖拽文件到此处"
              @blur="analyzeFile"
            >
              <template #append>
                <el-button @click="selectFile">
                  选择文件
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-form>

        <!-- 文件信息显示 -->
        <div v-if="fileInfo && fileInfo.isValid" class="file-info">
          <h4>文件信息</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">文件格式:</span>
              <span class="value">{{ getFormatName(fileInfo.format) }}</span>
            </div>
            <div class="info-item">
              <span class="label">文件大小:</span>
              <span class="value">{{ formatFileSize(fileInfo.fileSizeBytes) }}</span>
            </div>
            <div class="info-item">
              <span class="label">预估帧数:</span>
              <span class="value">{{ fileInfo.estimatedFrameCount }}</span>
            </div>
          </div>
          <div class="file-actions">
            <el-button size="small" @click="openSourceFolder">
              打开文件夹
            </el-button>
            <el-button size="small" @click="openSourceFile">
              打开文件
            </el-button>
          </div>
        </div>

        <div v-if="fileInfo && !fileInfo.isValid" class="error-info">
          <el-alert :title="fileInfo.errorMessage" type="error" show-icon />
        </div>
      </el-card>

      <!-- 2. 设置参数区域 -->
      <el-card class="section-card">
        <template #header>
          <span>设置参数</span>
        </template>

        <el-form :model="processRequest" label-width="120px">
          <el-form-item label="目标格式:">
            <el-radio-group v-model="processRequest.targetFormat">
              <el-radio :value="1">ASC</el-radio>
              <el-radio :value="2">BLF</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="输出目录:">
            <el-input v-model="processRequest.outputDirectory" placeholder="默认与源文件相同目录">
              <template #append>
                <el-button @click="selectOutputFolder">
                  选择目录
                </el-button>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="启用分割:">
            <el-switch v-model="processRequest.enableSplit" @change="onSplitToggle" />
          </el-form-item>

          <el-form-item v-if="processRequest.enableSplit" label="分割文件数:">
            <el-input-number
              v-model="splitFileCount"
              :min="1"
              :max="100"
              @change="onSplitCountChange"
            />
          </el-form-item>
        </el-form>
      </el-card>
      <!-- 3. 输出预览区域 -->
      <el-card class="section-card">
        <template #header>
          <span>输出预览</span>
        </template>

        <div v-if="!preview && (!fileInfo || !fileInfo.isValid)" class="preview-placeholder">
          <p>请先选择有效的 Log 文件</p>
        </div>

        <div v-if="preview" class="preview-content">
          <div class="preview-summary">
            <div class="summary-item">
              <span class="label">目标格式:</span>
              <span class="value">{{ getFormatName(preview.targetFormat) }}</span>
            </div>
            <div class="summary-item">
              <span class="label">输出文件数:</span>
              <span class="value">{{ preview.estimatedOutputFileCount }}</span>
            </div>
            <div class="summary-item">
              <span class="label">预计总大小:</span>
              <span class="value">{{ formatFileSize(preview.estimatedTotalSizeBytes) }}</span>
            </div>
          </div>

          <div class="output-files">
            <h4>输出文件列表</h4>
            <div class="file-list">
              <div
                v-for="(file, index) in preview.estimatedFiles"
                :key="index"
                class="file-item"
              >
                <div class="file-name">{{ file.fileName }}</div>
                <div class="file-size">{{ formatFileSize(file.estimatedSizeBytes) }}</div>
                <div class="file-actions">
                  <el-button size="small" text>
                    打开文件夹
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 4. 操作控制区域 -->
      <el-card class="section-card">
        <template #header>
          <span>操作控制</span>
        </template>

        <div class="action-buttons">
          <el-button
            type="primary"
            :disabled="!preview"
            @click="startProcess"
            :loading="isProcessing"
          >
            开始转换
          </el-button>

          <el-button
            type="danger"
            :disabled="!isProcessing"
            @click="cancelProcess"
          >
            取消转换
          </el-button>
        </div>

          <!-- 进度显示 -->
          <div v-if="progress" class="progress-section">
            <h4>转换进度</h4>
            <el-progress 
              :percentage="progress.overallProgressPercentage" 
              :status="progress.isCompleted ? 'success' : 'active'"
            />
            <p class="current-operation">{{ progress.currentOperation }}</p>
            
            <!-- 文件处理进度 -->
            <div v-if="progress.fileProgresses && progress.fileProgresses.length > 0" class="file-progress">
              <h5>文件处理详情</h5>
              <div class="file-progress-list">
                <div 
                  v-for="(fileProgress, index) in progress.fileProgresses" 
                  :key="index"
                  class="file-progress-item"
                >
                  <div class="file-name">{{ fileProgress.fileName }}</div>
                  <div class="file-status">{{ getStatusName(fileProgress.status) }}</div>
                  <el-progress 
                    :percentage="fileProgress.progressPercentage" 
                    :show-text="false" 
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>

        <!-- 完成状态 -->
        <div v-if="progress?.isCompleted" class="completion-section">
          <el-result icon="success" title="转换完成" sub-title="所有文件已成功转换">
            <template #extra>
              <el-button type="primary" @click="openOutputFolder">
                打开输出目录
              </el-button>
              <el-button @click="resetProcess">
                重新开始
              </el-button>
            </template>
          </el-result>
        </div>
      </el-card>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <span v-if="fileInfo">
        文件: {{ fileInfo.isValid ? '已选择' : '无效' }} |
        格式: {{ fileInfo.isValid ? getFormatName(fileInfo.format) : '未知' }}
      </span>
      <span v-if="!fileInfo">请选择 Log 文件</span>
      <span v-if="isProcessing" class="processing-status">
        转换中... {{ progress?.overallProgressPercentage || 0 }}%
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onUnmounted } from "vue";
import {
  appApi,
  DataLogFileInfo,
  DataLogProcessRequest,
  ProcessPreview,
  DataLogProcessResult,
  ProcessProgress,
  DataLogFormat,
  ProcessStatus
} from "@/api/appApi";

export default defineComponent({
  name: "LogConverterView",
  setup() {
    // 文件相关
    const fileForm = ref({ filePath: '' });
    const fileInfo = ref<DataLogFileInfo | null>(null);
    
    // 处理请求
    const processRequest = ref<DataLogProcessRequest>({
      sourceFilePath: '',
      targetFormat: DataLogFormat.Asc,
      outputDirectory: '',
      enableSplit: false,
      maxFileSizeBytes: 0,
      fileNamePrefix: 'converted'
    });
    
    const splitFileCount = ref(1);
    const preview = ref<ProcessPreview | null>(null);
    const processResult = ref<DataLogProcessResult | null>(null);
    const progress = ref<ProcessProgress | null>(null);

    // 状态控制
    const isProcessing = ref(false);
    
    let progressTimer: number | null = null;

    // 文件选择和分析
    const selectFile = () => {
      // TODO: 实现文件选择对话框
      console.log('选择文件');
    };

    const analyzeFile = async () => {
      if (!fileForm.value.filePath) return;

      try {
        const response = await appApi.dataLogConvert.analyzeFile(fileForm.value.filePath);
        fileInfo.value = response.data;
        processRequest.value.sourceFilePath = fileForm.value.filePath;

        // 自动预览
        if (fileInfo.value?.isValid) {
          await autoPreview();
        }
      } catch (error) {
        console.error('文件分析失败:', error);
      }
    };

    const selectOutputFolder = () => {
      // TODO: 实现文件夹选择对话框
      console.log('选择输出文件夹');
    };

    const openSourceFolder = () => {
      // TODO: 实现打开源文件夹
      console.log('打开源文件夹');
    };

    const openSourceFile = () => {
      // TODO: 实现打开源文件
      console.log('打开源文件');
    };

    // 自动预览
    const autoPreview = async () => {
      try {
        const response = await appApi.dataLogConvert.previewProcess(processRequest.value);
        preview.value = response.data;
      } catch (error) {
        console.error('预览失败:', error);
      }
    };

    // 分割开关变化
    const onSplitToggle = () => {
      if (processRequest.value.enableSplit) {
        splitFileCount.value = 1;
      }
      if (fileInfo.value?.isValid) {
        autoPreview();
      }
    };

    // 分割文件数变化
    const onSplitCountChange = () => {
      if (fileInfo.value?.isValid) {
        autoPreview();
      }
    };

    const startProcess = async () => {
      isProcessing.value = true;
      try {
        const response = await appApi.dataLogConvert.startProcess(processRequest.value);
        processResult.value = response.data;
        
        // 开始轮询进度
        startProgressPolling();
      } catch (error) {
        console.error('开始处理失败:', error);
        isProcessing.value = false;
      }
    };

    const startProgressPolling = () => {
      if (!processResult.value?.taskId) return;
      
      progressTimer = window.setInterval(async () => {
        try {
          const response = await appApi.dataLogConvert.getProgress(processResult.value!.taskId);
          progress.value = response.data;
          
          if (progress.value?.isCompleted) {
            stopProgressPolling();
            isProcessing.value = false;
          }
        } catch (error) {
          console.error('获取进度失败:', error);
        }
      }, 1000);
    };

    const stopProgressPolling = () => {
      if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = null;
      }
    };

    const cancelProcess = async () => {
      if (!processResult.value?.taskId) return;
      
      try {
        await appApi.dataLogConvert.cancelProcess(processResult.value.taskId);
        stopProgressPolling();
        isProcessing.value = false;
      } catch (error) {
        console.error('取消处理失败:', error);
      }
    };

    const openOutputFolder = async () => {
      if (!processRequest.value.outputDirectory) return;
      
      try {
        await appApi.dataLogConvert.openFolder(processRequest.value.outputDirectory);
      } catch (error) {
        console.error('打开文件夹失败:', error);
      }
    };

    const resetProcess = () => {
      fileInfo.value = null;
      preview.value = null;
      processResult.value = null;
      progress.value = null;
      isProcessing.value = false;
      splitFileCount.value = 1;
      stopProgressPolling();
    };

    const formatFileSize = (bytes: number): string => {
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      if (bytes === 0) return '0 Bytes';
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const getFormatName = (format: DataLogFormat): string => {
      switch (format) {
        case DataLogFormat.Asc: return 'ASC';
        case DataLogFormat.Blf: return 'BLF';
        default: return '未知';
      }
    };

    const getStatusName = (status: ProcessStatus): string => {
      switch (status) {
        case ProcessStatus.Pending: return '等待中';
        case ProcessStatus.Processing: return '处理中';
        case ProcessStatus.Completed: return '已完成';
        case ProcessStatus.Failed: return '失败';
        case ProcessStatus.Cancelled: return '已取消';
        default: return '未知';
      }
    };

    onUnmounted(() => {
      stopProgressPolling();
    });

    return {
      fileForm,
      fileInfo,
      processRequest,
      splitFileCount,
      preview,
      processResult,
      progress,
      isProcessing,
      selectFile,
      analyzeFile,
      selectOutputFolder,
      openSourceFolder,
      openSourceFile,
      onSplitToggle,
      onSplitCountChange,
      startProcess,
      cancelProcess,
      openOutputFolder,
      resetProcess,
      formatFileSize,
      getFormatName,
      getStatusName
    };
  },
});
</script>

<style scoped>
.log-converter {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.title-bar {
  background-color: var(--el-color-primary);
  color: white;
  padding: 15px 20px;
  text-align: center;
}

.title-bar h1 {
  font-size: 1.5rem;
  margin: 0;
}

.converter-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.section-card {
  border-radius: 8px;
}

.section-card :deep(.el-card__header) {
  background-color: var(--el-fill-color-light);
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.file-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;
}

.file-info h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1rem;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
}

.info-item .label {
  color: #7f8c8d;
  font-weight: 500;
}

.info-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.file-actions {
  display: flex;
  gap: 10px;
}

.error-info {
  margin-top: 15px;
}

.preview-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.placeholder-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.preview-content {
  padding: 10px 0;
}

.preview-summary {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item .label {
  color: #7f8c8d;
  font-weight: 500;
}

.summary-item .value {
  color: #2c3e50;
  font-weight: bold;
}

.output-files h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1rem;
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #fff;
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-name {
  flex: 1;
  color: #2c3e50;
  font-weight: 500;
}

.file-size {
  color: #7f8c8d;
  margin-right: 10px;
  font-size: 0.9rem;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.progress-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.progress-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1rem;
}

.current-operation {
  color: #7f8c8d;
  margin-top: 10px;
  font-size: 0.9rem;
}

.file-progress {
  margin-top: 20px;
}

.file-progress h5 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.file-progress-list {
  max-height: 150px;
  overflow-y: auto;
}

.file-progress-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 6px;
  background-color: #fff;
}

.file-progress-item:last-child {
  margin-bottom: 0;
}

.file-progress-item .file-name {
  flex: 1;
  font-size: 0.9rem;
}

.file-progress-item .file-status {
  width: 80px;
  font-size: 0.8rem;
  color: #7f8c8d;
}

.completion-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-light);
}

.status-bar {
  background-color: var(--el-fill-color-light);
  padding: 10px 20px;
  border-top: 1px solid var(--el-border-color-base);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: var(--el-text-color-regular);
}

.processing-status {
  color: var(--el-color-primary);
  font-weight: bold;
}

@media (max-width: 768px) {
  .converter-content {
    padding: 10px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .file-item,
  .file-progress-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .status-bar {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
